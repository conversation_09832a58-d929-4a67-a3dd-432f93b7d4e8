"use client"

import { useState } from "react"
import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, Github, Mail, GraduationCap, Users, Heart, TrendingUp, CheckCircle, Shield, Zap, Star } from "lucide-react"
import { toast } from "sonner"

export default function SignUpPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [name, setName] = useState("")
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [error, setError] = useState("")
  const [useCredentials, setUseCredentials] = useState(true)

  const handleCredentialsSignUp = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!agreedToTerms) {
      setError("Please agree to the Terms of Service and Privacy Policy")
      return
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match")
      return
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters long")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          name,
          graduationYear: new Date().getFullYear(), // Default for dev testing
          programmes: ["Development Testing"], // Default for dev testing
          province: "Gauteng", // Default for dev testing
          city: "Johannesburg", // Default for dev testing
          country: "South Africa",
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(data.message || "Registration failed")
        return
      }

      // Sign in the user after successful registration
      const result = await signIn("credentials", {
        email: email.toLowerCase(),
        password,
        callbackUrl: "/dashboard",
        redirect: false,
      })

      if (result?.ok) {
        router.push("/dashboard")
      } else {
        setError("Registration successful, but sign-in failed. Please try signing in.")
      }
    } catch (error) {
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleEmailSignUp = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!agreedToTerms) {
      setError("Please agree to the Terms of Service and Privacy Policy")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      const result = await signIn("email", {
        email: email.toLowerCase(),
        callbackUrl: "/onboarding",
        redirect: false,
      })

      if (result?.error) {
        setError("Failed to send sign-up email. Please try again.")
      } else {
        toast.success("Check your email for a sign-up link!")
      }
    } catch (error) {
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleOAuthSignUp = async (provider: string) => {
    if (!agreedToTerms) {
      setError("Please agree to the Terms of Service and Privacy Policy")
      return
    }

    setIsLoading(true)
    try {
      await signIn(provider, { callbackUrl: "/onboarding" })
    } catch (error) {
      setError(`Failed to sign up with ${provider}. Please try again.`)
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-protec-red via-protec-red/90 to-protec-red/80">
      <div className="flex min-h-screen">
        {/* Left Column - Branding & Benefits */}
        <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-12 xl:px-20">
          <div className="mx-auto max-w-lg">
            {/* Logo */}
            <div className="animate-fade-in-up">
              <Link href="/" className="inline-flex items-center space-x-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
                  <span className="text-xl font-bold text-white">P</span>
                </div>
                <span className="text-2xl font-bold text-white">PROTEC Alumni</span>
              </Link>
            </div>

            {/* Main Heading */}
            <div className="mt-12 animate-fade-in-up delay-200">
              <h1 className="text-4xl font-bold text-white tracking-tight">
                Join the future of
                <span className="text-white/90"> alumni networking</span>
              </h1>
              <p className="mt-4 text-lg text-white/80">
                Connect with thousands of PROTEC alumni worldwide and unlock opportunities for growth, collaboration, and impact.
              </p>
            </div>

            {/* Benefits */}
            <div className="mt-12 space-y-6 animate-fade-in-up delay-400">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/20">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Exclusive Network Access</h3>
                  <p className="text-sm text-white/70">Connect with 5,000+ verified PROTEC alumni</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/20">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Career Opportunities</h3>
                  <p className="text-sm text-white/70">Access exclusive job postings and mentorship</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/20">
                    <Heart className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Impact & Giving</h3>
                  <p className="text-sm text-white/70">Support current students and community projects</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/20">
                    <Zap className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Innovation Hub</h3>
                  <p className="text-sm text-white/70">Collaborate on projects and business ventures</p>
                </div>
              </div>
            </div>

            {/* Success Stories */}
            <div className="mt-12 animate-fade-in-up delay-600">
              <div className="rounded-lg bg-white/10 backdrop-blur-sm p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="text-sm font-medium text-white">5.0 rating</span>
                </div>
                <blockquote className="text-sm text-white/90 italic">
                  "The PROTEC alumni network has been instrumental in my career growth. I've found mentors, job opportunities, and lifelong friends."
                </blockquote>
                <div className="mt-4 flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-full bg-white/20"></div>
                  <div>
                    <p className="text-sm font-medium text-white">Sarah Johnson</p>
                    <p className="text-xs text-white/70">Software Engineer at Google</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Registration Form */}
        <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            {/* Mobile Header */}
            <div className="lg:hidden text-center mb-8">
              <Link href="/" className="inline-flex items-center space-x-2">
                <div className="flex h-10 w-10 items-center justify-center rounded-md bg-white/10 backdrop-blur-sm">
                  <span className="text-lg font-bold text-white">P</span>
                </div>
                <span className="text-2xl font-bold text-white">PROTEC Alumni</span>
              </Link>
              <h2 className="mt-6 text-3xl font-bold tracking-tight text-white">
                Join the network
              </h2>
              <p className="mt-2 text-sm text-white/70">
                Connect with thousands of PROTEC alumni worldwide
              </p>
            </div>

            <div className="animate-fade-in-up">
              <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-sm">
                <CardHeader className="space-y-1">
                  <CardTitle className="text-2xl text-center text-protec-navy">Create account</CardTitle>
                  <CardDescription className="text-center">
                    Get started with your PROTEC alumni account
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Error Alert */}
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  {/* Benefits Preview */}
                  <div className="bg-gradient-to-r from-protec-navy/5 to-protec-red/5 rounded-lg p-4 space-y-3">
                    <h3 className="text-sm font-semibold text-protec-navy flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                      What you'll get:
                    </h3>
                    <div className="grid grid-cols-1 gap-2 text-xs text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Shield className="h-3 w-3 text-protec-navy" />
                        <span>Verified alumni network access</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-3 w-3 text-protec-navy" />
                        <span>Exclusive networking events</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-3 w-3 text-protec-navy" />
                        <span>Career advancement opportunities</span>
                      </div>
                    </div>
                  </div>

                  {/* OAuth Buttons */}
                  <div className="space-y-3">
                    <Button
                      variant="outline"
                      className="w-full h-12 text-sm font-medium border-gray-300 hover:bg-gray-50"
                      onClick={() => handleOAuthSignUp("google")}
                      disabled={isLoading}
                    >
                      <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                        />
                        <path
                          fill="currentColor"
                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                        />
                        <path
                          fill="currentColor"
                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                        />
                        <path
                          fill="currentColor"
                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                        />
                      </svg>
                      Continue with Google
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full h-12 text-sm font-medium border-gray-300 hover:bg-gray-50"
                      onClick={() => handleOAuthSignUp("github")}
                      disabled={isLoading}
                    >
                      <Github className="mr-2 h-4 w-4" />
                      Continue with GitHub
                    </Button>
                  </div>

                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <Separator className="w-full" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-2 text-muted-foreground">
                        Or continue with email
                      </span>
                    </div>
                  </div>

                  {/* Auth Method Toggle */}
                  <div className="flex space-x-2 p-1 bg-muted rounded-lg">
                    <button
                      type="button"
                      onClick={() => setUseCredentials(true)}
                      className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                        useCredentials
                          ? 'bg-background text-foreground shadow-sm'
                          : 'text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      Password
                    </button>
                    <button
                      type="button"
                      onClick={() => setUseCredentials(false)}
                      className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                        !useCredentials
                          ? 'bg-background text-foreground shadow-sm'
                          : 'text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      Magic Link
                    </button>
                  </div>

                  {/* Credentials Form */}
                  {useCredentials ? (
                    <form onSubmit={handleCredentialsSignUp} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name</Label>
                        <Input
                          id="name"
                          type="text"
                          placeholder="Enter your full name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          required
                          disabled={isLoading}
                          className="h-12"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your email address"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          disabled={isLoading}
                          className="h-12"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="password">Password</Label>
                          <Input
                            id="password"
                            type="password"
                            placeholder="Min. 8 characters"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            disabled={isLoading}
                            className="h-12"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword">Confirm Password</Label>
                          <Input
                            id="confirmPassword"
                            type="password"
                            placeholder="Confirm password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            required
                            disabled={isLoading}
                            className="h-12"
                          />
                        </div>
                      </div>

                      {/* Password Strength Indicator */}
                      {password && (
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2 text-xs">
                            <CheckCircle className={`h-3 w-3 ${password.length >= 8 ? 'text-green-500' : 'text-gray-300'}`} />
                            <span className={password.length >= 8 ? 'text-green-600' : 'text-gray-500'}>
                              At least 8 characters
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs">
                            <CheckCircle className={`h-3 w-3 ${password === confirmPassword && password ? 'text-green-500' : 'text-gray-300'}`} />
                            <span className={password === confirmPassword && password ? 'text-green-600' : 'text-gray-500'}>
                              Passwords match
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Terms Agreement */}
                      <div className="flex items-start space-x-2">
                        <Checkbox
                          id="terms"
                          checked={agreedToTerms}
                          onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                        />
                        <div className="grid gap-1.5 leading-none">
                          <Label
                            htmlFor="terms"
                            className="text-xs font-normal leading-relaxed peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            I agree to the{" "}
                            <Link href="/terms" className="underline underline-offset-4 hover:text-primary">
                              Terms of Service
                            </Link>{" "}
                            and{" "}
                            <Link href="/privacy" className="underline underline-offset-4 hover:text-primary">
                              Privacy Policy
                            </Link>
                          </Label>
                        </div>
                      </div>

                      <Button
                        type="submit"
                        className="w-full h-12 bg-protec-red hover:bg-protec-red/90 text-white font-medium"
                        disabled={isLoading || !agreedToTerms}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Creating account...
                          </>
                        ) : (
                          'Create account'
                        )}
                      </Button>
                    </form>
                  ) : (
                    /* Magic Link Form */
                    <form onSubmit={handleEmailSignUp} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your email address"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          disabled={isLoading}
                          className="h-12"
                        />
                        <p className="text-xs text-muted-foreground">
                          We'll send you a magic link to complete your registration
                        </p>
                      </div>

                      {/* Terms Agreement */}
                      <div className="flex items-start space-x-2">
                        <Checkbox
                          id="terms"
                          checked={agreedToTerms}
                          onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                        />
                        <div className="grid gap-1.5 leading-none">
                          <Label
                            htmlFor="terms"
                            className="text-xs font-normal leading-relaxed peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            I agree to the{" "}
                            <Link href="/terms" className="underline underline-offset-4 hover:text-primary">
                              Terms of Service
                            </Link>{" "}
                            and{" "}
                            <Link href="/privacy" className="underline underline-offset-4 hover:text-primary">
                              Privacy Policy
                            </Link>
                          </Label>
                        </div>
                      </div>

                      <Button
                        type="submit"
                        className="w-full h-12 bg-protec-red hover:bg-protec-red/90 text-white font-medium"
                        disabled={isLoading || !agreedToTerms}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Creating account...
                          </>
                        ) : (
                          <>
                            <Mail className="mr-2 h-4 w-4" />
                            Create account
                          </>
                        )}
                      </Button>
                    </form>
                  )}

                  <div className="text-center text-sm">
                    <span className="text-muted-foreground">Already have an account? </span>
                    <Link
                      href="/auth/signin"
                      className="font-medium text-protec-red hover:text-protec-red/80"
                    >
                      Sign in
                    </Link>
                  </div>
                </CardContent>
              </Card>

              <p className="mt-8 text-center text-xs text-white/70">
                By creating an account, you agree to our{" "}
                <Link href="/terms" className="underline underline-offset-4 hover:text-white">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy" className="underline underline-offset-4 hover:text-white">
                  Privacy Policy
                </Link>
                .
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
