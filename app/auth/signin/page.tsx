"use client"

import { useState, Suspense } from "react"
import { signIn, getSession } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Github, Mail, GraduationCap, Users, Heart, TrendingUp, Check } from "lucide-react"
import { toast } from "sonner"

export default function SignInPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-protec-red" />
    </div>}>
      <SignInContent />
    </Suspense>
  )
}

function SignInContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [useCredentials, setUseCredentials] = useState(true)

  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard"
  const errorParam = searchParams.get("error")

  const handleCredentialsSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const result = await signIn("credentials", {
        email: email.toLowerCase(),
        password,
        callbackUrl,
        redirect: false,
      })

      if (result?.error) {
        setError("Invalid email or password. Please try again.")
      } else if (result?.ok) {
        router.push(callbackUrl)
      }
    } catch (error) {
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const result = await signIn("email", {
        email: email.toLowerCase(),
        callbackUrl,
        redirect: false,
      })

      if (result?.error) {
        setError("Failed to send sign-in email. Please try again.")
      } else {
        toast.success("Check your email for a sign-in link!")
      }
    } catch (error) {
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleOAuthSignIn = async (provider: string) => {
    setIsLoading(true)
    try {
      await signIn(provider, { callbackUrl })
    } catch (error) {
      setError(`Failed to sign in with ${provider}. Please try again.`)
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-protec-navy via-protec-navy/90 to-protec-navy">
      <div className="flex min-h-screen">
        {/* Left Column - Branding & Features */}
        <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-12 xl:px-20">
          <div className="mx-auto max-w-lg">
            {/* Logo */}
            <div className="animate-fade-in-up">
              <Link href="/" className="inline-flex items-center space-x-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
                  <span className="text-xl font-bold text-white">P</span>
                </div>
                <span className="text-2xl font-bold text-white">PROTEC Alumni</span>
              </Link>
            </div>

            {/* Main Heading */}
            <div className="mt-12 animate-fade-in-up delay-200">
              <h1 className="text-4xl font-bold text-white tracking-tight">
                Welcome back to your
                <span className="text-protec-red"> professional network</span>
              </h1>
              <p className="mt-4 text-lg text-white/80">
                Connect with thousands of PROTEC alumni, expand your network, and advance your career together.
              </p>
            </div>

            {/* Features */}
            <div className="mt-12 space-y-6 animate-fade-in-up delay-400">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-protec-red/20">
                    <Users className="h-5 w-5 text-protec-red" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Global Alumni Network</h3>
                  <p className="text-sm text-white/70">Connect with over 5,000 PROTEC alumni worldwide</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-protec-red/20">
                    <TrendingUp className="h-5 w-5 text-protec-red" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Career Advancement</h3>
                  <p className="text-sm text-white/70">Access exclusive job opportunities and mentorship</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-protec-red/20">
                    <Heart className="h-5 w-5 text-protec-red" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Give Back</h3>
                  <p className="text-sm text-white/70">Support current students and the next generation</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-protec-red/20">
                    <GraduationCap className="h-5 w-5 text-protec-red" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">Lifelong Learning</h3>
                  <p className="text-sm text-white/70">Access professional development resources</p>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="mt-12 animate-fade-in-up delay-600">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="rounded-lg bg-white/10 backdrop-blur-sm p-4">
                  <div className="text-2xl font-bold text-white">5,000+</div>
                  <div className="text-xs text-white/70">Alumni</div>
                </div>
                <div className="rounded-lg bg-white/10 backdrop-blur-sm p-4">
                  <div className="text-2xl font-bold text-white">50+</div>
                  <div className="text-xs text-white/70">Countries</div>
                </div>
                <div className="rounded-lg bg-white/10 backdrop-blur-sm p-4">
                  <div className="text-2xl font-bold text-white">1,000+</div>
                  <div className="text-xs text-white/70">Companies</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Sign In Form */}
        <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            {/* Mobile Header */}
            <div className="lg:hidden text-center mb-8">
              <Link href="/" className="inline-flex items-center space-x-2">
                <div className="flex h-10 w-10 items-center justify-center rounded-md bg-white/10 backdrop-blur-sm">
                  <span className="text-lg font-bold text-white">P</span>
                </div>
                <span className="text-2xl font-bold text-white">PROTEC Alumni</span>
              </Link>
              <h2 className="mt-6 text-3xl font-bold tracking-tight text-white">
                Welcome back
              </h2>
              <p className="mt-2 text-sm text-white/70">
                Sign in to your account to connect with the PROTEC community
              </p>
            </div>

            <div className="animate-fade-in-up">
              <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-sm">
                <CardHeader className="space-y-1">
                  <CardTitle className="text-2xl text-center text-protec-navy">Sign in</CardTitle>
                  <CardDescription className="text-center">
                    Choose your preferred sign-in method
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Error Alert */}
                  {(error || errorParam) && (
                    <Alert variant="destructive">
                      <AlertDescription>
                        {error || "An error occurred during sign-in. Please try again."}
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* OAuth Buttons */}
                  <div className="space-y-3">
                    <Button
                      variant="outline"
                      className="w-full h-12 text-sm font-medium border-gray-300 hover:bg-gray-50"
                      onClick={() => handleOAuthSignIn("google")}
                      disabled={isLoading}
                    >
                      <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                        />
                        <path
                          fill="currentColor"
                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                        />
                        <path
                          fill="currentColor"
                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                        />
                        <path
                          fill="currentColor"
                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                        />
                      </svg>
                      Continue with Google
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full h-12 text-sm font-medium border-gray-300 hover:bg-gray-50"
                      onClick={() => handleOAuthSignIn("github")}
                      disabled={isLoading}
                    >
                      <Github className="mr-2 h-4 w-4" />
                      Continue with GitHub
                    </Button>
                  </div>

                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <Separator className="w-full" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-2 text-muted-foreground">
                        Or continue with email
                      </span>
                    </div>
                  </div>

                  {/* Auth Method Toggle */}
                  <div className="flex space-x-2 p-1 bg-muted rounded-lg">
                    <button
                      type="button"
                      onClick={() => setUseCredentials(true)}
                      className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                        useCredentials
                          ? 'bg-background text-foreground shadow-sm'
                          : 'text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      Password
                    </button>
                    <button
                      type="button"
                      onClick={() => setUseCredentials(false)}
                      className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                        !useCredentials
                          ? 'bg-background text-foreground shadow-sm'
                          : 'text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      Magic Link
                    </button>
                  </div>

                  {/* Credentials Form */}
                  {useCredentials ? (
                    <form onSubmit={handleCredentialsSignIn} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your email address"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          disabled={isLoading}
                          className="h-12"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <Input
                          id="password"
                          type="password"
                          placeholder="Enter your password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                          disabled={isLoading}
                          className="h-12"
                        />
                      </div>

                      <Button
                        type="submit"
                        className="w-full h-12 bg-protec-red hover:bg-protec-red/90 text-white font-medium"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Signing in...
                          </>
                        ) : (
                          'Sign in'
                        )}
                      </Button>
                    </form>
                  ) : (
                    /* Magic Link Form */
                    <form onSubmit={handleEmailSignIn} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email-magic">Email address</Label>
                        <Input
                          id="email-magic"
                          type="email"
                          placeholder="Enter your email address"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          disabled={isLoading}
                          className="h-12"
                        />
                      </div>

                      <Button
                        type="submit"
                        className="w-full h-12 bg-protec-red hover:bg-protec-red/90 text-white font-medium"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Sending magic link...
                          </>
                        ) : (
                          <>
                            <Mail className="mr-2 h-4 w-4" />
                            Send magic link
                          </>
                        )}
                      </Button>
                    </form>
                  )}

                  <div className="text-center text-sm">
                    <span className="text-muted-foreground">Don't have an account? </span>
                    <Link
                      href="/auth/signup"
                      className="font-medium text-protec-red hover:text-protec-red/80"
                    >
                      Sign up
                    </Link>
                  </div>
                </CardContent>
              </Card>

              <p className="mt-8 text-center text-xs text-white/70">
                By signing in, you agree to our{" "}
                <Link href="/terms" className="underline underline-offset-4 hover:text-white">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy" className="underline underline-offset-4 hover:text-white">
                  Privacy Policy
                </Link>
                .
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
