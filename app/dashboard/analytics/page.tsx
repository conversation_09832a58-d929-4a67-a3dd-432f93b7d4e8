"use client"

import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { PersonalAnalyticsOverview } from "@/components/dashboard/personal-analytics-overview"
import { NetworkAnalytics } from "@/components/dashboard/network-analytics"
import { EngagementAnalytics } from "@/components/dashboard/engagement-analytics"
import { CareerProgress } from "@/components/dashboard/career-progress"
import { GoalsTracking } from "@/components/dashboard/goals-tracking"
import { ActivityHeatmap } from "@/components/dashboard/activity-heatmap"

export default function PersonalAnalyticsPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 space-y-6 p-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
              Your Analytics
            </h1>
            <p className="text-muted-foreground">
              Track your engagement, network growth, and career progress
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <select className="px-3 py-2 border rounded-md text-sm">
              <option value="30">Last 30 days</option>
              <option value="90">Last 3 months</option>
              <option value="365">Last year</option>
              <option value="all">All time</option>
            </select>
          </div>
        </div>

        {/* Overview Cards */}
        <PersonalAnalyticsOverview />

        {/* Main Analytics Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            <NetworkAnalytics />
            <EngagementAnalytics />
            <ActivityHeatmap />
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <CareerProgress />
            <GoalsTracking />
          </div>
        </div>
      </main>
    </div>
  )
}
