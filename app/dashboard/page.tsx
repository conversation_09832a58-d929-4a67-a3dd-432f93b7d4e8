"use client"

import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { DashboardHeader } from "@/components/dashboard/header"
import { DashboardStats } from "@/components/dashboard/stats"
import { RecentActivity } from "@/components/dashboard/recent-activity"
import { UpcomingEvents } from "@/components/dashboard/upcoming-events"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { WelcomeCard } from "@/components/dashboard/welcome-card"
import { NetworkGrowth } from "@/components/dashboard/network-growth"
import { DonationProgress } from "@/components/dashboard/donation-progress"
import { RecentPosts } from "@/components/dashboard/recent-posts"
import { EventsCalendar } from "@/components/dashboard/events-calendar"
import { AlumniSpotlight } from "@/components/dashboard/alumni-spotlight"

export default function DashboardPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 space-y-6 p-6">
        <DashboardHeader />

        {/* Key Metrics */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <DashboardStats />
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-12">
          {/* Left Column */}
          <div className="lg:col-span-8 space-y-6">
            <WelcomeCard />

            <div className="grid gap-6 md:grid-cols-2">
              <NetworkGrowth />
              <DonationProgress />
            </div>

            <RecentActivity />
            <RecentPosts />
          </div>

          {/* Right Column */}
          <div className="lg:col-span-4 space-y-6">
            <QuickActions />
            <UpcomingEvents />
            <EventsCalendar />
            <AlumniSpotlight />
          </div>
        </div>
      </main>
    </div>
  )
}
