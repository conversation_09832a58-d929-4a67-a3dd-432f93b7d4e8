import { MainNav } from "@/components/navigation/main-nav"
import { LandingFooter } from "@/components/landing/footer"
import { LandingFAQ } from "@/components/landing/faq"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  HelpCircle, 
  Search, 
  Book, 
  Video, 
  MessageCircle, 
  Mail,
  Phone,
  Download,
  ExternalLink,
  ArrowRight,
  Users,
  Settings,
  Shield,
  CreditCard
} from "lucide-react"
import Link from "next/link"

const helpCategories = [
  {
    icon: Users,
    title: "Getting Started",
    description: "Learn how to set up your profile and navigate the platform",
    articles: 12,
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    icon: Settings,
    title: "Account Settings",
    description: "Manage your account preferences and privacy settings",
    articles: 8,
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: MessageCircle,
    title: "Networking & Messaging",
    description: "Connect with alumni and use messaging features",
    articles: 15,
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  },
  {
    icon: Shield,
    title: "Privacy & Security",
    description: "Keep your account secure and control your privacy",
    articles: 6,
    color: "text-red-600",
    bgColor: "bg-red-50"
  },
  {
    icon: CreditCard,
    title: "Donations & Payments",
    description: "Learn about giving back and payment processes",
    articles: 5,
    color: "text-yellow-600",
    bgColor: "bg-yellow-50"
  },
  {
    icon: Book,
    title: "Platform Features",
    description: "Explore all the features available to alumni",
    articles: 20,
    color: "text-indigo-600",
    bgColor: "bg-indigo-50"
  }
]

const popularArticles = [
  {
    title: "How to complete your alumni profile",
    category: "Getting Started",
    readTime: "5 min read",
    views: 1250
  },
  {
    title: "Finding and connecting with other alumni",
    category: "Networking",
    readTime: "3 min read",
    views: 980
  },
  {
    title: "Privacy settings and profile visibility",
    category: "Privacy & Security",
    readTime: "4 min read",
    views: 750
  },
  {
    title: "How to RSVP for events",
    category: "Platform Features",
    readTime: "2 min read",
    views: 650
  },
  {
    title: "Making donations through the platform",
    category: "Donations & Payments",
    readTime: "6 min read",
    views: 520
  }
]

const quickLinks = [
  {
    icon: Video,
    title: "Video Tutorials",
    description: "Watch step-by-step guides",
    link: "/help/videos"
  },
  {
    icon: Download,
    title: "User Guide PDF",
    description: "Download the complete guide",
    link: "/help/user-guide.pdf"
  },
  {
    icon: MessageCircle,
    title: "Community Forum",
    description: "Ask questions and get answers",
    link: "/help/forum"
  },
  {
    icon: Mail,
    title: "Email Support",
    description: "Get help via email",
    link: "mailto:<EMAIL>"
  }
]

export default function HelpPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-br from-protec-navy to-protec-red py-20 sm:py-32">
          <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
          
          <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <Badge variant="outline" className="mb-6 border-white/30 bg-white/10 text-white">
                <HelpCircle className="mr-2 h-4 w-4" />
                Help Center
              </Badge>
              
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
                How can we help you?
              </h1>
              
              <p className="mt-6 text-xl leading-8 text-blue-100">
                Find answers to your questions, learn how to use the platform, 
                and get the most out of your PROTEC Alumni Network experience.
              </p>
              
              {/* Search Bar */}
              <div className="mt-10 mx-auto max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search for help articles..."
                    className="pl-10 bg-white/90 border-white/20 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Links */}
        <section className="py-16 bg-white">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickLinks.map((link) => {
                const Icon = link.icon
                return (
                  <Card key={link.title} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer">
                    <CardContent className="p-6 text-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-protec-navy/10 mx-auto mb-4">
                        <Icon className="h-6 w-6 text-protec-navy" />
                      </div>
                      <h3 className="text-lg font-semibold text-protec-navy mb-2">
                        {link.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4">
                        {link.description}
                      </p>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={link.link}>
                          Access
                          <ExternalLink className="ml-2 h-3 w-3" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* Help Categories */}
        <section className="py-20 bg-gradient-to-b from-white to-protec-gray/30">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
                Browse by Category
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Find help articles organized by topic to quickly get the information you need.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {helpCategories.map((category) => {
                const Icon = category.icon
                return (
                  <Card key={category.title} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer">
                    <CardContent className="p-6">
                      <div className={`flex h-12 w-12 items-center justify-center rounded-full ${category.bgColor} mb-4`}>
                        <Icon className={`h-6 w-6 ${category.color}`} />
                      </div>
                      <h3 className="text-lg font-semibold text-protec-navy mb-2">
                        {category.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4">
                        {category.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                          {category.articles} articles
                        </span>
                        <Button variant="ghost" size="sm">
                          Browse
                          <ArrowRight className="ml-2 h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* Popular Articles */}
        <section className="py-20 bg-white">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
                Popular Articles
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                The most helpful articles based on community feedback and views.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {popularArticles.map((article, index) => (
                <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline" className="border-protec-navy/20 bg-protec-navy/5 text-protec-navy text-xs">
                        {article.category}
                      </Badge>
                      <span className="text-xs text-gray-500">{article.views} views</span>
                    </div>
                    <CardTitle className="text-lg text-protec-navy line-clamp-2">
                      {article.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{article.readTime}</span>
                      <Button variant="ghost" size="sm">
                        Read Article
                        <ArrowRight className="ml-2 h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <LandingFAQ />

        {/* Contact Support */}
        <section className="py-20 bg-protec-navy">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Still need help?
              </h2>
              <p className="mt-6 text-lg leading-8 text-blue-100">
                Can't find what you're looking for? Our support team is here to help you 
                with any questions or issues you might have.
              </p>
              
              <div className="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6 max-w-lg mx-auto">
                <Card className="border-0 bg-white/10 backdrop-blur">
                  <CardContent className="p-6 text-center">
                    <Mail className="h-8 w-8 text-white mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">
                      Email Support
                    </h3>
                    <p className="text-blue-100 text-sm mb-4">
                      Get help via email within 24 hours
                    </p>
                    <Button variant="outline" className="border-white text-white hover:bg-white hover:text-protec-navy" asChild>
                      <Link href="mailto:<EMAIL>">
                        Send Email
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-white/10 backdrop-blur">
                  <CardContent className="p-6 text-center">
                    <Phone className="h-8 w-8 text-white mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">
                      Phone Support
                    </h3>
                    <p className="text-blue-100 text-sm mb-4">
                      Call us during business hours
                    </p>
                    <Button variant="outline" className="border-white text-white hover:bg-white hover:text-protec-navy" asChild>
                      <Link href="tel:+***********">
                        Call Now
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-8 text-center">
                <p className="text-blue-100 text-sm">
                  Support Hours: Monday - Friday, 8:00 AM - 5:00 PM (SAST)
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
      <LandingFooter />
    </div>
  )
}