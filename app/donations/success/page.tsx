"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MainNav } from "@/components/navigation/main-nav"
import { 
  CheckCircle, 
  Heart, 
  Share2, 
  Download, 
  ArrowRight,
  Calendar,
  CreditCard,
  Mail
} from "lucide-react"
import Link from "next/link"
import { api } from "@/components/providers/trpc-provider"

export default function DonationSuccessPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
    </div>}>
      <DonationSuccessContent />
    </Suspense>
  )
}

function DonationSuccessContent() {
  const searchParams = useSearchParams()
  const donationId = searchParams.get('id')
  const [showConfetti, setShowConfetti] = useState(true)

  const { data: donation, isLoading } = api.donations.getById.useQuery(
    { id: donationId! },
    { enabled: !!donationId }
  )

  useEffect(() => {
    // Hide confetti after animation
    const timer = setTimeout(() => setShowConfetti(false), 3000)
    return () => clearTimeout(timer)
  }, [])

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'I just donated to PROTEC!',
          text: 'I made a donation to support PROTEC Alumni Platform. Join me in making a difference!',
          url: window.location.origin + '/donations'
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback to copying to clipboard
      navigator.clipboard.writeText(
        `I just donated to PROTEC! Join me in making a difference: ${window.location.origin}/donations`
      )
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-protec-red"></div>
        </main>
      </div>
    )
  }

  if (!donation) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6 text-center">
              <p className="text-muted-foreground">Donation not found</p>
              <Button asChild className="mt-4">
                <Link href="/donations">Back to Donations</Link>
              </Button>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      
      {/* Confetti Effect */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-50">
          {Array.from({ length: 50 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-protec-red rounded-full"
              initial={{
                x: Math.random() * window.innerWidth,
                y: -10,
                rotate: 0,
              }}
              animate={{
                y: window.innerHeight + 10,
                rotate: 360,
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                ease: "easeOut",
              }}
            />
          ))}
        </div>
      )}

      <main className="flex-1 container mx-auto px-6 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto space-y-8"
        >
          {/* Success Header */}
          <div className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <CheckCircle className="w-20 h-20 text-green-500 mx-auto" />
            </motion.div>
            
            <h1 className="text-3xl font-bold text-protec-navy">
              Thank You for Your Donation!
            </h1>
            
            <p className="text-lg text-muted-foreground">
              Your generous contribution will make a real difference in supporting PROTEC's mission.
            </p>
          </div>

          {/* Donation Details Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Heart className="w-5 h-5 text-protec-red" />
                <span>Donation Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Amount</p>
                  <p className="text-2xl font-bold text-protec-red">
                    R{donation.amountZAR.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Donation ID</p>
                  <p className="font-mono text-sm">{donation.id}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Purpose</p>
                  <Badge variant="secondary" className="capitalize">
                    {donation.purpose}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Payment Method</p>
                  <div className="flex items-center space-x-1">
                    <CreditCard className="w-4 h-4" />
                    <span className="capitalize">{donation.gateway}</span>
                  </div>
                </div>
              </div>

              {donation.isRecurring && (
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-blue-600" />
                    <span className="font-medium text-blue-800">Recurring Donation</span>
                  </div>
                  <p className="text-sm text-blue-600 mt-1">
                    Your next donation will be processed on{' '}
                    {donation.nextPaymentDate 
                      ? new Date(donation.nextPaymentDate).toLocaleDateString()
                      : 'the same date next month'
                    }
                  </p>
                </div>
              )}

              <div className="pt-4 border-t">
                <p className="text-sm text-muted-foreground">
                  A confirmation email has been sent to your registered email address.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Impact Message */}
          <Card className="bg-gradient-to-r from-protec-navy to-protec-red text-white">
            <CardContent className="pt-6">
              <h3 className="text-xl font-bold mb-2">Your Impact</h3>
              <p className="text-blue-100">
                Your donation of R{donation.amountZAR.toLocaleString()} will help us:
              </p>
              <ul className="mt-3 space-y-1 text-blue-100">
                <li>• Support student scholarships and bursaries</li>
                <li>• Improve educational facilities and equipment</li>
                <li>• Fund mentorship and career development programs</li>
                <li>• Organize networking events and workshops</li>
              </ul>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleShare}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Share2 className="w-4 h-4" />
              <span>Share Your Impact</span>
            </Button>
            
            <Button
              asChild
              variant="outline"
              className="flex items-center space-x-2"
            >
              <a href={`/donations/receipt/${donation.id}`}>
                <Download className="w-4 h-4" />
                <span>Download Receipt</span>
              </a>
            </Button>
            
            <Button
              asChild
              className="flex items-center space-x-2 bg-protec-red hover:bg-protec-red/90"
            >
              <Link href="/dashboard">
                <span>Go to Dashboard</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
            </Button>
          </div>

          {/* Additional Actions */}
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              Want to make an even bigger impact?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild variant="outline">
                <Link href="/donations">Make Another Donation</Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/volunteer">Become a Volunteer</Link>
              </Button>
            </div>
          </div>

          {/* Contact Information */}
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <h4 className="font-medium">Questions about your donation?</h4>
                <p className="text-sm text-muted-foreground">
                  Contact our donor support team
                </p>
                <div className="flex items-center justify-center space-x-2 text-protec-red">
                  <Mail className="w-4 h-4" />
                  <a href="mailto:<EMAIL>" className="hover:underline">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </main>
    </div>
  )
}
