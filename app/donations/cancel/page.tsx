"use client"

import { Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { MainNav } from "@/components/navigation/main-nav"
import { 
  XCircle, 
  ArrowLeft, 
  Heart, 
  HelpCircle,
  RefreshCw,
  MessageCircle
} from "lucide-react"
import Link from "next/link"

export default function DonationCancelPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
    </div>}>
      <DonationCancelContent />
    </Suspense>
  )
}

function DonationCancelContent() {
  const searchParams = useSearchParams()
  const donationId = searchParams.get('id')

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      
      <main className="flex-1 container mx-auto px-6 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto space-y-8"
        >
          {/* Cancel Header */}
          <div className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <XCircle className="w-20 h-20 text-orange-500 mx-auto" />
            </motion.div>
            
            <h1 className="text-3xl font-bold text-protec-navy">
              Donation Cancelled
            </h1>
            
            <p className="text-lg text-muted-foreground">
              Your donation was cancelled and no payment was processed.
            </p>
          </div>

          {/* Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <HelpCircle className="w-5 h-5 text-orange-500" />
                <span>What Happened?</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Your donation was cancelled before payment could be processed. This could have happened for several reasons:
              </p>
              
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>You clicked the "Cancel" button during the payment process</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>You closed the payment window before completing the transaction</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>There was a technical issue with the payment gateway</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>The payment session expired due to inactivity</span>
                </li>
              </ul>

              {donationId && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm">
                    <strong>Donation Reference:</strong> {donationId}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    This reference can be used if you need to contact support about this transaction.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <RefreshCw className="w-5 h-5 text-protec-red" />
                <span>What's Next?</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Don't worry! You can easily try again or explore other ways to support PROTEC.
              </p>
              
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="p-4 border rounded-lg">
                  <Heart className="w-8 h-8 text-protec-red mb-2" />
                  <h4 className="font-medium mb-1">Try Again</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Restart the donation process with the same or different amount.
                  </p>
                  <Button asChild className="w-full bg-protec-red hover:bg-protec-red/90">
                    <Link href="/donations">
                      Make a Donation
                    </Link>
                  </Button>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <MessageCircle className="w-8 h-8 text-blue-600 mb-2" />
                  <h4 className="font-medium mb-1">Get Help</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Contact our support team if you're experiencing issues.
                  </p>
                  <Button asChild variant="outline" className="w-full">
                    <Link href="/contact">
                      Contact Support
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Alternative Ways to Help */}
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardContent className="pt-6">
              <h3 className="text-xl font-bold mb-4 text-center">Other Ways to Support PROTEC</h3>
              
              <div className="grid gap-4 sm:grid-cols-3">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Heart className="w-6 h-6 text-blue-600" />
                  </div>
                  <h4 className="font-medium mb-1">Volunteer</h4>
                  <p className="text-sm text-muted-foreground">
                    Share your time and skills
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <MessageCircle className="w-6 h-6 text-green-600" />
                  </div>
                  <h4 className="font-medium mb-1">Mentor</h4>
                  <p className="text-sm text-muted-foreground">
                    Guide the next generation
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <RefreshCw className="w-6 h-6 text-purple-600" />
                  </div>
                  <h4 className="font-medium mb-1">Spread the Word</h4>
                  <p className="text-sm text-muted-foreground">
                    Share our mission
                  </p>
                </div>
              </div>
              
              <div className="text-center mt-6">
                <Button asChild variant="outline">
                  <Link href="/get-involved">
                    Learn More
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild variant="outline" className="flex items-center space-x-2">
              <Link href="/donations">
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Donations</span>
              </Link>
            </Button>
            
            <Button asChild className="bg-protec-red hover:bg-protec-red/90">
              <Link href="/dashboard">
                Go to Dashboard
              </Link>
            </Button>
          </div>

          {/* Support Contact */}
          <div className="text-center p-6 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Need Help?</h4>
            <p className="text-sm text-muted-foreground mb-4">
              If you're experiencing technical difficulties or have questions about donations, 
              our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center text-sm">
              <a 
                href="mailto:<EMAIL>" 
                className="text-protec-red hover:underline"
              >
                <EMAIL>
              </a>
              <span className="hidden sm:inline text-muted-foreground">•</span>
              <a 
                href="tel:+27123456789" 
                className="text-protec-red hover:underline"
              >
                +27 12 345 6789
              </a>
            </div>
          </div>
        </motion.div>
      </main>
    </div>
  )
}
