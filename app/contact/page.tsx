"use client"

import { useState } from "react"
import { MainNav } from "@/components/navigation/main-nav"
import { LandingFooter } from "@/components/landing/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  MessageCircle, 
  Send,
  Building,
  Users,
  HelpCircle,
  Briefcase
} from "lucide-react"
import { toast } from "sonner"

const contactReasons = [
  { value: "general", label: "General Inquiry" },
  { value: "alumni", label: "Alumni Services" },
  { value: "partnerships", label: "Industry Partnerships" },
  { value: "media", label: "Media & Press" },
  { value: "support", label: "Technical Support" },
  { value: "other", label: "Other" }
]

const offices = [
  {
    city: "Johannesburg",
    address: "123 PROTEC House, Braamfontein, Johannesburg 2001",
    phone: "+27 11 403 6861",
    email: "<EMAIL>",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    isHeadquarters: true
  },
  {
    city: "Cape Town",
    address: "456 Innovation Centre, Observatory, Cape Town 7925",
    phone: "+27 21 447 1234",
    email: "<EMAIL>",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    isHeadquarters: false
  },
  {
    city: "Durban",
    address: "789 Tech Hub, Umhlanga, Durban 4319",
    phone: "+27 31 566 7890",
    email: "<EMAIL>",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    isHeadquarters: false
  }
]

const quickContacts = [
  {
    icon: Users,
    title: "Alumni Services",
    description: "Questions about alumni benefits, events, or platform access",
    email: "<EMAIL>",
    phone: "+27 11 403 6862"
  },
  {
    icon: Briefcase,
    title: "Industry Partnerships",
    description: "Collaboration opportunities, sponsorships, and corporate partnerships",
    email: "<EMAIL>",
    phone: "+27 11 403 6863"
  },
  {
    icon: HelpCircle,
    title: "Technical Support",
    description: "Platform issues, account problems, or technical assistance",
    email: "<EMAIL>",
    phone: "+27 11 403 6864"
  }
]

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    reason: "",
    subject: "",
    message: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast.success("Message sent successfully! We'll get back to you within 24 hours.")
    setFormData({
      name: "",
      email: "",
      phone: "",
      reason: "",
      subject: "",
      message: ""
    })
    setIsSubmitting(false)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-br from-protec-navy to-protec-red py-20 sm:py-32">
          <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
          
          <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <Badge variant="outline" className="mb-6 border-white/30 bg-white/10 text-white">
                <MessageCircle className="mr-2 h-4 w-4" />
                Contact Us
              </Badge>
              
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
                Get in Touch
              </h1>
              
              <p className="mt-6 text-xl leading-8 text-blue-100">
                Have questions about PROTEC or our alumni platform? We're here to help. 
                Reach out to us and we'll get back to you as soon as possible.
              </p>
            </div>
          </div>
        </section>

        {/* Quick Contact Options */}
        <section className="py-20 bg-gradient-to-b from-white to-protec-gray/30">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
                Quick Contact Options
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Choose the best way to reach us based on your inquiry type.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {quickContacts.map((contact) => {
                const Icon = contact.icon
                return (
                  <Card key={contact.title} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <CardContent className="p-6">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-protec-navy/10 mb-4">
                        <Icon className="h-6 w-6 text-protec-navy" />
                      </div>
                      <h3 className="text-lg font-semibold text-protec-navy mb-2">
                        {contact.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4">
                        {contact.description}
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-protec-red" />
                          <a href={`mailto:${contact.email}`} className="text-protec-red hover:underline">
                            {contact.email}
                          </a>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-protec-red" />
                          <a href={`tel:${contact.phone}`} className="text-protec-red hover:underline">
                            {contact.phone}
                          </a>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* Contact Form and Office Info */}
        <section className="py-20 bg-white">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div>
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-2xl text-protec-navy">Send us a message</CardTitle>
                    <p className="text-gray-600">
                      Fill out the form below and we'll get back to you within 24 hours.
                    </p>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Full Name *</Label>
                          <Input
                            id="name"
                            value={formData.name}
                            onChange={(e) => handleInputChange("name", e.target.value)}
                            required
                            placeholder="Your full name"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address *</Label>
                          <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            required
                            placeholder="<EMAIL>"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="phone">Phone Number</Label>
                          <Input
                            id="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleInputChange("phone", e.target.value)}
                            placeholder="+27 XX XXX XXXX"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="reason">Inquiry Type *</Label>
                          <Select value={formData.reason} onValueChange={(value) => handleInputChange("reason", value)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select inquiry type" />
                            </SelectTrigger>
                            <SelectContent>
                              {contactReasons.map((reason) => (
                                <SelectItem key={reason.value} value={reason.value}>
                                  {reason.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="subject">Subject *</Label>
                        <Input
                          id="subject"
                          value={formData.subject}
                          onChange={(e) => handleInputChange("subject", e.target.value)}
                          required
                          placeholder="Brief description of your inquiry"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="message">Message *</Label>
                        <Textarea
                          id="message"
                          value={formData.message}
                          onChange={(e) => handleInputChange("message", e.target.value)}
                          required
                          placeholder="Please provide details about your inquiry..."
                          rows={6}
                        />
                      </div>

                      <Button
                        type="submit"
                        className="w-full bg-protec-red hover:bg-protec-red/90"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="mr-2 h-4 w-4" />
                            Send Message
                          </>
                        )}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>

              {/* Office Locations */}
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-protec-navy mb-4">Our Offices</h2>
                  <p className="text-gray-600 mb-8">
                    Visit us at any of our locations across South Africa.
                  </p>
                </div>

                {offices.map((office) => (
                  <Card key={office.city} className="border-0 shadow-lg">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                          <Building className="h-5 w-5 text-protec-navy" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-protec-navy">
                            {office.city}
                          </h3>
                          {office.isHeadquarters && (
                            <Badge variant="outline" className="border-protec-red/20 bg-protec-red/5 text-protec-red text-xs">
                              Headquarters
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-3 text-sm text-gray-600">
                        <div className="flex items-start gap-2">
                          <MapPin className="h-4 w-4 text-protec-red mt-0.5 flex-shrink-0" />
                          <span>{office.address}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-protec-red flex-shrink-0" />
                          <a href={`tel:${office.phone}`} className="hover:text-protec-red">
                            {office.phone}
                          </a>
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-protec-red flex-shrink-0" />
                          <a href={`mailto:${office.email}`} className="hover:text-protec-red">
                            {office.email}
                          </a>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-protec-red flex-shrink-0" />
                          <span>{office.hours}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>
      <LandingFooter />
    </div>
  )
}