"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MainNav } from "@/components/navigation/main-nav"
import { AdvancedSearch } from "@/components/alumni/advanced-search"
import { EnhancedAlumniCard } from "@/components/alumni/enhanced-alumni-card"
import { api } from "@/components/providers/trpc-provider"
import { 
  Users, 
  Search, 
  Filter, 
  Grid, 
  List, 
  TrendingUp,
  MapPin,
  GraduationCap,
  Briefcase,
  Star,
  Heart,
  UserPlus,
  MessageCircle
} from "lucide-react"
import { toast } from "sonner"

export default function EnhancedAlumniDirectoryPage() {
  const { data: session } = useSession()
  const [searchFilters, setSearchFilters] = useState<any>({})
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [savedSearches, setSavedSearches] = useState<any[]>([])

  // Enhanced search query
  const { 
    data: searchResults, 
    isLoading: isSearching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = api.alumni.enhancedSearch.useInfiniteQuery(
    searchFilters,
    {
      getNextPageParam: (lastPage) => lastPage.nextCursor,
      enabled: true
    }
  )

  // Get alumni stats
  const { data: stats } = api.alumni.getStats.useQuery()

  // Connection request mutation
  const sendConnectionRequestMutation = api.alumni.sendConnectionRequest.useMutation({
    onSuccess: () => {
      toast.success("Connection request sent successfully!")
    },
    onError: (error) => {
      toast.error(error.message || "Failed to send connection request")
    }
  })

  const handleSearch = (filters: any) => {
    setSearchFilters(filters)
  }

  const handleSaveSearch = (name: string, filters: any) => {
    const newSearch = {
      id: Date.now().toString(),
      name,
      filters
    }
    setSavedSearches(prev => [...prev, newSearch])
    toast.success(`Search "${name}" saved successfully!`)
  }

  const handleConnect = (alumniId: string) => {
    if (!session) {
      toast.error("Please sign in to connect with alumni")
      return
    }
    sendConnectionRequestMutation.mutate({ alumniId })
  }

  const handleMessage = (alumniId: string) => {
    if (!session) {
      toast.error("Please sign in to message alumni")
      return
    }
    // Navigate to messaging
    window.location.href = `/messages?to=${alumniId}`
  }

  const handleSave = (alumniId: string) => {
    // Implement save functionality
    toast.success("Alumni saved to your list!")
  }

  // Flatten search results
  const allAlumni = searchResults?.pages.flatMap(page => page.alumni) || []

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      
      <main className="flex-1 container mx-auto px-6 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-2"
            >
              <h1 className="text-4xl font-bold text-protec-navy">
                Enhanced Alumni Directory
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Connect with fellow PROTEC alumni using advanced search and networking features
              </p>
            </motion.div>

            {/* Stats */}
            {stats && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="flex justify-center space-x-8 text-sm text-muted-foreground"
              >
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>{stats.totalAlumni.toLocaleString()} Alumni</span>
                </div>
                <div className="flex items-center space-x-2">
                  <UserPlus className="w-4 h-4" />
                  <span>{stats.totalConnections.toLocaleString()} Connections</span>
                </div>
                <div className="flex items-center space-x-2">
                  <GraduationCap className="w-4 h-4" />
                  <span>{stats.recentGraduates} Recent Graduates</span>
                </div>
              </motion.div>
            )}
          </div>

          {/* Advanced Search */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <AdvancedSearch
              onSearch={handleSearch}
              onSaveSearch={handleSaveSearch}
              savedSearches={savedSearches}
              isLoading={isSearching}
            />
          </motion.div>

          {/* Results Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-protec-navy">
                {allAlumni.length > 0 ? (
                  <>
                    {allAlumni.length} Alumni Found
                    {Object.keys(searchFilters).length > 0 && (
                      <Badge variant="secondary" className="ml-2">
                        Filtered
                      </Badge>
                    )}
                  </>
                ) : isSearching ? (
                  "Searching..."
                ) : (
                  "No alumni found"
                )}
              </h2>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Results */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {isSearching && allAlumni.length === 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div className="flex items-start space-x-4">
                          <div className="w-16 h-16 bg-gray-200 rounded-full" />
                          <div className="flex-1 space-y-2">
                            <div className="h-4 bg-gray-200 rounded w-3/4" />
                            <div className="h-3 bg-gray-200 rounded w-1/2" />
                            <div className="h-3 bg-gray-200 rounded w-2/3" />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="h-3 bg-gray-200 rounded" />
                          <div className="h-3 bg-gray-200 rounded w-5/6" />
                        </div>
                        <div className="flex space-x-2">
                          <div className="h-6 bg-gray-200 rounded w-16" />
                          <div className="h-6 bg-gray-200 rounded w-20" />
                          <div className="h-6 bg-gray-200 rounded w-14" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : allAlumni.length > 0 ? (
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'md:grid-cols-2 lg:grid-cols-3' 
                  : 'grid-cols-1 max-w-4xl mx-auto'
              }`}>
                {allAlumni.map((alumni, index) => (
                  <motion.div
                    key={alumni.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <EnhancedAlumniCard
                      alumni={alumni}
                      onConnect={handleConnect}
                      onMessage={handleMessage}
                      onSave={handleSave}
                      variant={viewMode === 'list' ? 'detailed' : 'default'}
                    />
                  </motion.div>
                ))}
              </div>
            ) : !isSearching ? (
              <Card>
                <CardContent className="py-12 text-center">
                  <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No alumni found</h3>
                  <p className="text-muted-foreground mb-4">
                    Try adjusting your search criteria or browse all alumni
                  </p>
                  <Button onClick={() => handleSearch({})}>
                    Show All Alumni
                  </Button>
                </CardContent>
              </Card>
            ) : null}

            {/* Load More */}
            {hasNextPage && (
              <div className="text-center pt-8">
                <Button
                  onClick={() => fetchNextPage()}
                  disabled={isFetchingNextPage}
                  variant="outline"
                  size="lg"
                >
                  {isFetchingNextPage ? (
                    <>
                      <div className="w-4 h-4 border-2 border-protec-red border-t-transparent rounded-full animate-spin mr-2" />
                      Loading more...
                    </>
                  ) : (
                    'Load More Alumni'
                  )}
                </Button>
              </div>
            )}
          </motion.div>

          {/* Quick Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Filter className="w-5 h-5" />
                  <span>Quick Filters</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button
                    variant="outline"
                    onClick={() => handleSearch({ 
                      graduationYearRange: { 
                        from: new Date().getFullYear() - 5 
                      } 
                    })}
                    className="justify-start"
                  >
                    <GraduationCap className="w-4 h-4 mr-2" />
                    Recent Graduates
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => handleSearch({ 
                      industries: ['Technology'] 
                    })}
                    className="justify-start"
                  >
                    <Briefcase className="w-4 h-4 mr-2" />
                    Tech Industry
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => handleSearch({ 
                      locations: { provinces: ['Western Cape'] } 
                    })}
                    className="justify-start"
                  >
                    <MapPin className="w-4 h-4 mr-2" />
                    Western Cape
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => handleSearch({ 
                      availability: { mentoring: true } 
                    })}
                    className="justify-start"
                  >
                    <Star className="w-4 h-4 mr-2" />
                    Mentors Available
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </main>
    </div>
  )
}
