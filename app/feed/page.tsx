"use client"

import { useSession } from "next-auth/react"
import { MainNav } from "@/components/navigation/main-nav"
import { PostFeed } from "@/components/feed/post-feed"
import { CreatePost } from "@/components/feed/create-post"
import { TrendingTags } from "@/components/feed/trending-tags"
import { SuggestedConnections } from "@/components/feed/suggested-connections"

export default function FeedPage() {
  const { data: session } = useSession()

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid gap-6 lg:grid-cols-4">
            {/* Main Feed */}
            <div className="lg:col-span-3 space-y-6">
              <div>
                <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
                  Community Feed
                </h1>
                <p className="text-muted-foreground">
                  Stay connected with the latest updates from the PROTEC alumni community
                </p>
              </div>

              {session && <CreatePost />}
              <PostFeed />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <TrendingTags />
              <SuggestedConnections />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
