"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { MainNav } from "@/components/navigation/main-nav"
import { EventsList } from "@/components/events/events-list"
import { EventFilters } from "@/components/events/event-filters"
import { But<PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"

export default function EventsPage() {
  const { data: session } = useSession()
  const [filters, setFilters] = useState({})

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 space-y-6 p-6">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
              Events
            </h1>
            <p className="text-muted-foreground">
              Discover and join alumni events, workshops, and networking opportunities
            </p>
          </div>
          
          {session && (
            <Button className="bg-protec-red hover:bg-protec-red/90" asChild>
              <Link href="/events/new">
                <Plus className="mr-2 h-4 w-4" />
                Create Event
              </Link>
            </Button>
          )}
        </div>

        <div className="grid gap-6 lg:grid-cols-4">
          <div className="lg:col-span-1">
            <EventFilters filters={filters} onFiltersChange={setFilters} />
          </div>
          <div className="lg:col-span-3">
            <EventsList filters={filters} />
          </div>
        </div>
      </main>
    </div>
  )
}
