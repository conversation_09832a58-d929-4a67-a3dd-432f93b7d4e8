import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const token = await getToken({ req: request })
    
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const paymentId = params.id

    if (!paymentId) {
      return NextResponse.json(
        { error: 'Payment ID is required' },
        { status: 400 }
      )
    }

    // Find the donation by payment ID or donation ID
    const donation = await prisma.donation.findFirst({
      where: {
        OR: [
          { id: paymentId },
          { 
            metadata: {
              path: ['paymentId'],
              equals: paymentId
            }
          }
        ]
      },
      include: {
        alumni: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!donation) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }

    // Check if the user has access to this payment
    const userEmail = token.email
    if (donation.alumni.email !== userEmail && token.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Return payment status
    return NextResponse.json({
      id: donation.id,
      status: donation.status,
      amount: donation.currency === 'ZAR' ? donation.amountZAR : donation.amountUSD,
      currency: donation.currency,
      gateway: donation.gateway,
      purpose: donation.purpose,
      frequency: donation.frequency,
      isRecurring: donation.isRecurring,
      isAnonymous: donation.isAnonymous,
      dedicationMessage: donation.dedicationMessage,
      createdAt: donation.createdAt,
      completedAt: donation.completedAt,
      metadata: donation.metadata,
      donor: {
        id: donation.alumni.id,
        name: donation.alumni.name,
        email: donation.alumni.email
      }
    })

  } catch (error) {
    console.error('Payment status error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
