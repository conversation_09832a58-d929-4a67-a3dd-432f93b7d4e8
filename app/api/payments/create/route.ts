import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { prisma } from '@/lib/prisma'
import { paymentService } from '@/lib/services/payment-service'
import { securityService } from '@/lib/services/security-service'
import { v4 as uuidv4 } from 'uuid'

export async function POST(request: NextRequest) {
  try {
    const token = await getToken({ req: request })
    
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      amount,
      currency,
      gateway,
      purpose,
      frequency,
      donorEmail,
      donorName,
      returnUrl,
      cancelUrl,
      notifyUrl,
      isAnonymous,
      dedicationMessage
    } = body

    // Validate required fields
    if (!amount || !currency || !gateway || !purpose || !frequency || !donorEmail || !donorName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate amount
    if (amount < 50) {
      return NextResponse.json(
        { error: 'Minimum donation amount is R50' },
        { status: 400 }
      )
    }

    if (amount > 100000) {
      return NextResponse.json(
        { error: 'Maximum donation amount is R100,000' },
        { status: 400 }
      )
    }

    // Validate gateway
    const supportedGateways = ['payfast', 'snapscan', 'ozow', 'paypal']
    if (!supportedGateways.includes(gateway)) {
      return NextResponse.json(
        { error: 'Unsupported payment gateway' },
        { status: 400 }
      )
    }

    // Validate currency for gateway
    if ((gateway === 'snapscan' || gateway === 'ozow') && currency !== 'ZAR') {
      return NextResponse.json(
        { error: `${gateway} only supports ZAR currency` },
        { status: 400 }
      )
    }

    // Find the alumni
    const alumni = await prisma.alumni.findUnique({
      where: { email: donorEmail }
    })

    if (!alumni) {
      return NextResponse.json(
        { error: 'Alumni not found' },
        { status: 404 }
      )
    }

    // Rate limiting check
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    
    const rateLimitResult = await securityService.checkRateLimit(
      `payment:${ipAddress}`,
      10, // max 10 payment attempts
      3600 // per hour
    )

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: 'Too many payment attempts. Please wait before trying again.',
          retryAfter: rateLimitResult.resetTime
        },
        { status: 429 }
      )
    }

    // Generate donation ID
    const donationId = uuidv4()

    // Create donation record
    const donation = await prisma.donation.create({
      data: {
        id: donationId,
        alumniId: alumni.id,
        amountZAR: currency === 'ZAR' ? amount : amount * 18, // Rough conversion for tracking
        amountUSD: currency === 'USD' ? amount : amount / 18,
        currency,
        gateway,
        purpose,
        frequency,
        status: 'pending',
        isRecurring: frequency !== 'ONE_TIME',
        isAnonymous: isAnonymous || false,
        dedicationMessage: dedicationMessage || null,
        metadata: {
          donorName,
          donorEmail,
          userAgent: request.headers.get('user-agent'),
          ipAddress
        }
      }
    })

    // Create payment request
    const paymentRequest = {
      amount,
      currency,
      gateway,
      purpose,
      frequency,
      donorEmail,
      donorName,
      returnUrl,
      cancelUrl,
      notifyUrl,
      donationId
    }

    // Process payment based on gateway
    let paymentResponse
    
    switch (gateway) {
      case 'payfast':
        paymentResponse = await paymentService.createPayment(paymentRequest)
        break
      case 'snapscan':
        paymentResponse = await paymentService.createPayment(paymentRequest)
        break
      case 'ozow':
        paymentResponse = await paymentService.createPayment(paymentRequest)
        break
      case 'paypal':
        paymentResponse = await paymentService.createPayment({
          ...paymentRequest,
          currency: 'USD' // PayPal uses USD
        })
        break
      default:
        paymentResponse = {
          success: false,
          error: 'Unsupported gateway'
        }
    }

    if (!paymentResponse.success) {
      // Update donation status to failed
      await prisma.donation.update({
        where: { id: donationId },
        data: { 
          status: 'failed',
          metadata: {
            ...donation.metadata as object,
            error: paymentResponse.error
          }
        }
      })

      return NextResponse.json(
        { error: paymentResponse.error },
        { status: 400 }
      )
    }

    // Update donation with payment details
    await prisma.donation.update({
      where: { id: donationId },
      data: {
        metadata: {
          ...donation.metadata as object,
          paymentId: paymentResponse.paymentId,
          paymentUrl: paymentResponse.paymentUrl
        }
      }
    })

    // Log payment creation
    await securityService.logSecurityEvent({
      type: 'PAYMENT_CREATED',
      userId: alumni.id,
      ipAddress,
      userAgent: request.headers.get('user-agent') || 'unknown',
      metadata: {
        donationId,
        amount,
        currency,
        gateway,
        paymentId: paymentResponse.paymentId
      },
      severity: 'LOW'
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'DONATION_INITIATED',
        refId: donationId,
        alumniId: alumni.id,
        metadata: {
          amount,
          currency,
          gateway,
          purpose
        }
      }
    })

    return NextResponse.json({
      success: true,
      paymentUrl: paymentResponse.paymentUrl,
      paymentId: paymentResponse.paymentId,
      donationId,
      metadata: paymentResponse.metadata
    })

  } catch (error) {
    console.error('Payment creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
