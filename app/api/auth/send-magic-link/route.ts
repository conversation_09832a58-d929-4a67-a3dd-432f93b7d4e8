import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { emailService } from '@/lib/services/email-service'
import { securityService } from '@/lib/services/security-service'
import * as crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, token, magicLinkUrl, expiresAt, appName } = body

    if (!email || !token || !magicLinkUrl) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Rate limiting check
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    
    const rateLimitResult = await securityService.checkRateLimit(
      `magic-link:${ipAddress}`,
      5, // max 5 requests
      300 // per 5 minutes
    )

    if (!rateLimitResult.allowed) {
      await securityService.logSecurityEvent({
        type: 'RATE_LIMIT_EXCEEDED',
        userId: email,
        ipAddress,
        userAgent: request.headers.get('user-agent') || 'unknown',
        metadata: {
          endpoint: 'send-magic-link',
          remainingTime: rateLimitResult.resetTime
        },
        severity: 'MEDIUM'
      })

      return NextResponse.json(
        { 
          error: 'Too many requests. Please wait before requesting another magic link.',
          retryAfter: rateLimitResult.resetTime
        },
        { status: 429 }
      )
    }

    // Check if user exists, if not create a pending user record
    let alumni = await prisma.alumni.findUnique({
      where: { email }
    })

    if (!alumni) {
      // Create a pending alumni record
      alumni = await prisma.alumni.create({
        data: {
          email,
          name: email.split('@')[0], // Temporary name
          isActive: false, // Will be activated after verification
          role: 'ALUMNI',
          graduationYear: new Date().getFullYear(), // Will be updated during onboarding
          programmes: [],
          skills: [],
          interests: []
        }
      })
    }

    // Store the magic link token in database
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: crypto.createHash('sha256').update(token).digest('hex'), // Hash the token
        expires: new Date(expiresAt)
      }
    })

    // Send magic link email
    const emailResult = await emailService.sendMagicLink(
      email,
      magicLinkUrl,
      new URL(process.env.NEXTAUTH_URL!).host,
      appName || 'PROTEC Alumni Network'
    )

    if (!emailResult.success) {
      return NextResponse.json(
        { error: 'Failed to send magic link email' },
        { status: 500 }
      )
    }

    // Log successful magic link request
    await securityService.logSecurityEvent({
      type: 'MAGIC_LINK_REQUESTED',
      userId: email,
      ipAddress,
      userAgent: request.headers.get('user-agent') || 'unknown',
      metadata: {
        appName,
        messageId: emailResult.messageId
      },
      severity: 'LOW'
    })

    return NextResponse.json({
      success: true,
      message: 'Magic link sent successfully',
      messageId: emailResult.messageId
    })

  } catch (error) {
    console.error('Send magic link error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
