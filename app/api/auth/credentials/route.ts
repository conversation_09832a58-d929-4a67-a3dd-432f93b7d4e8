import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validationResult = loginSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false,
          message: 'Invalid input',
          errors: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { email, password } = validationResult.data

    // Find user by email
    const alumni = await prisma.alumni.findUnique({
      where: { email: email.toLowerCase() },
      select: {
        id: true,
        email: true,
        name: true,
        password: true,
        photoUrl: true,
        role: true,
        isActive: true,
        province: true,
        city: true,
        country: true,
      }
    })

    if (!alumni) {
      return NextResponse.json(
        { 
          success: false,
          message: 'Invalid email or password' 
        },
        { status: 401 }
      )
    }

    if (!alumni.password) {
      return NextResponse.json(
        { 
          success: false,
          message: 'Please use OAuth sign-in for this account' 
        },
        { status: 401 }
      )
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, alumni.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { 
          success: false,
          message: 'Invalid email or password' 
        },
        { status: 401 }
      )
    }

    if (!alumni.isActive) {
      return NextResponse.json(
        { 
          success: false,
          message: 'Account is not active. Please contact support.' 
        },
        { status: 401 }
      )
    }

    // Update last login
    await prisma.alumni.update({
      where: { id: alumni.id },
      data: { lastLoginAt: new Date() }
    })

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: alumni.id,
        email: alumni.email,
        role: alumni.role 
      },
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '7d' }
    )

    // Return user data and token
    const user = {
      id: alumni.id,
      email: alumni.email,
      name: alumni.name,
      image: alumni.photoUrl,
      role: alumni.role,
      isActive: alumni.isActive,
      province: alumni.province,
      city: alumni.city,
      country: alumni.country,
    }

    return NextResponse.json({
      success: true,
      user,
      token,
    })

  } catch (error) {
    console.error('Credentials authentication error:', error)
    return NextResponse.json(
      { 
        success: false,
        message: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
