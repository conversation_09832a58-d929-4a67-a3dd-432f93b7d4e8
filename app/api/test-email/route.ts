import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/services/email-service'
import { auth } from '@/auth'

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Only allow admins to test email configuration
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { type, email, testData } = body

    switch (type) {
      case 'connection':
        // Test email server connection
        const connectionResult = await emailService.testConnection()
        return NextResponse.json(connectionResult)

      case 'magic-link':
        // Test magic link email
        if (!email) {
          return NextResponse.json(
            { error: 'Email address is required' },
            { status: 400 }
          )
        }
        
        const testUrl = `${process.env.NEXTAUTH_URL}/auth/signin?token=test-token-123&email=${encodeURIComponent(email)}`
        const magicLinkResult = await emailService.sendMagicLink(
          email,
          testUrl,
          new URL(process.env.NEXTAUTH_URL!).host
        )
        
        return NextResponse.json({
          success: true,
          message: 'Test magic link email sent successfully',
          messageId: magicLinkResult.messageId
        })

      case 'welcome':
        // Test welcome email
        if (!email) {
          return NextResponse.json(
            { error: 'Email address is required' },
            { status: 400 }
          )
        }
        
        const welcomeResult = await emailService.sendWelcomeEmail(
          email,
          testData?.name || 'Test User'
        )
        
        return NextResponse.json({
          success: true,
          message: 'Test welcome email sent successfully',
          messageId: welcomeResult.messageId
        })

      case 'custom':
        // Test custom email
        if (!email || !testData?.subject || !testData?.content) {
          return NextResponse.json(
            { error: 'Email, subject, and content are required' },
            { status: 400 }
          )
        }
        
        const customResult = await emailService.sendCustomEmail(
          email,
          testData.subject,
          testData.content,
          testData.text
        )
        
        return NextResponse.json({
          success: true,
          message: 'Test custom email sent successfully',
          messageId: customResult.messageId
        })

      case 'templated':
        // Test templated email
        if (!email || !testData?.templateType || !testData?.templateData) {
          return NextResponse.json(
            { error: 'Email, template type, and template data are required' },
            { status: 400 }
          )
        }
        
        const templatedResult = await emailService.sendTemplatedEmail(
          testData.templateType,
          email,
          testData.templateData
        )
        
        return NextResponse.json({
          success: true,
          message: `Test ${testData.templateType} email sent successfully`,
          messageId: templatedResult.messageId
        })

      default:
        return NextResponse.json(
          { error: 'Invalid test type' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Email test error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to send test email',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      )
    }

    // Return email configuration status (without sensitive data)
    const config = {
      host: process.env.EMAIL_SERVER_HOST || 'Not configured',
      port: process.env.EMAIL_SERVER_PORT || 'Not configured',
      secure: process.env.EMAIL_SERVER_SECURE === 'true',
      from: process.env.EMAIL_FROM || 'Not configured',
      hasUser: !!process.env.EMAIL_SERVER_USER,
      hasPassword: !!process.env.EMAIL_SERVER_PASSWORD,
    }

    return NextResponse.json({
      success: true,
      configuration: config,
      status: 'Email service is configured'
    })
  } catch (error) {
    console.error('Email configuration check error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check email configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}