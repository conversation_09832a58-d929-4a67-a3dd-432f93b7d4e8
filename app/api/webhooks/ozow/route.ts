import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { paymentService } from '@/lib/services/payment-service'

export async function POST(request: NextRequest) {
  try {
    // Parse the form data from Ozow webhook
    const formData = await request.formData()
    const data: Record<string, string> = {}
    
    // Convert FormData to object
    for (const [key, value] of formData.entries()) {
      data[key] = value.toString()
    }

    console.log('Ozow webhook received:', data)

    // Verify the webhook signature
    const headers = Object.fromEntries(request.headers.entries())
    const verification = await paymentService.verifyWebhook('ozow', data, headers)
    
    if (!verification.isValid) {
      console.error('Ozow webhook verification failed:', verification.error)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Extract payment information from Ozow payload
    const {
      TransactionId,
      TransactionReference,
      Amount,
      Status,
      StatusMessage,
      CurrencyCode,
      IsTest
    } = data

    // Find the donation by transaction reference
    const donationId = TransactionReference?.replace('PROTEC_', '')
    if (!donationId) {
      console.error('Ozow webhook: Invalid transaction reference:', TransactionReference)
      return NextResponse.json(
        { error: 'Invalid transaction reference' },
        { status: 400 }
      )
    }

    const donation = await prisma.donation.findUnique({
      where: { id: donationId },
      include: { alumni: true }
    })

    if (!donation) {
      console.error('Ozow webhook: Donation not found:', donationId)
      return NextResponse.json(
        { error: 'Donation not found' },
        { status: 404 }
      )
    }

    // Map Ozow status to our status
    let newStatus: string
    switch (Status?.toLowerCase()) {
      case 'complete':
      case 'completed':
        newStatus = 'completed'
        break
      case 'cancelled':
      case 'error':
      case 'abandoned':
        newStatus = 'failed'
        break
      case 'pending':
      case 'pendingpayment':
        newStatus = 'pending'
        break
      default:
        newStatus = 'pending'
    }

    // Update donation status
    const updatedDonation = await prisma.donation.update({
      where: { id: donationId },
      data: {
        status: newStatus,
        transactionId: TransactionId,
        completedAt: newStatus === 'completed' ? new Date() : null,
        metadata: {
          ...donation.metadata,
          ozowTransactionId: TransactionId,
          ozowStatus: Status,
          ozowStatusMessage: StatusMessage,
          amountCents: Amount,
          currencyCode: CurrencyCode,
          isTest: IsTest,
          webhookReceived: new Date().toISOString()
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: newStatus === 'completed' ? 'DONATION_COMPLETED' : 'DONATION_UPDATED',
        refId: donationId,
        alumniId: donation.alumniId,
        metadata: {
          status: Status,
          amount: Amount,
          gateway: 'ozow',
          transactionId: TransactionId,
          statusMessage: StatusMessage
        }
      }
    })

    // If payment is complete and it's a recurring donation, log a note
    // (Ozow doesn't support recurring payments, so this is just for tracking)
    if (newStatus === 'completed' && donation.isRecurring) {
      console.log('Ozow recurring donation completed (manual setup required):', {
        donationId,
        frequency: donation.frequency
      })
    }

    console.log('Ozow webhook processed successfully:', {
      donationId,
      status: newStatus,
      amount: Amount,
      transactionId: TransactionId
    })

    // Ozow expects a specific response format
    return new NextResponse('OK', { status: 200 })

  } catch (error) {
    console.error('Ozow webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

// Handle GET requests (Ozow sometimes sends GET requests for testing)
export async function GET(req: NextRequest) {
  return new NextResponse('Ozow webhook endpoint is active', { status: 200 })
}
