"use client"

import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { AnalyticsOverview } from "@/components/admin/analytics-overview"
import { UserGrowthChart } from "@/components/admin/user-growth-chart"
import { EngagementMetrics } from "@/components/admin/engagement-metrics"
import { DonationAnalytics } from "@/components/admin/donation-analytics"
import { EventAnalytics } from "@/components/admin/event-analytics"
import { GeographicDistribution } from "@/components/admin/geographic-distribution"
import { TopContributors } from "@/components/admin/top-contributors"
import { RecentActivities } from "@/components/admin/recent-activities"

export default function AnalyticsPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  // In a real app, check if user has admin privileges
  // if (!session.user.isAdmin) {
  //   redirect("/dashboard")
  // }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 space-y-6 p-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
              Analytics Dashboard
            </h1>
            <p className="text-muted-foreground">
              Comprehensive insights into the PROTEC Alumni Platform
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <select className="px-3 py-2 border rounded-md text-sm">
              <option value="30">Last 30 days</option>
              <option value="90">Last 3 months</option>
              <option value="365">Last year</option>
              <option value="all">All time</option>
            </select>
          </div>
        </div>

        {/* Overview Cards */}
        <AnalyticsOverview />

        {/* Charts Grid */}
        <div className="grid gap-6 lg:grid-cols-2">
          <UserGrowthChart />
          <EngagementMetrics />
        </div>

        {/* Detailed Analytics */}
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <DonationAnalytics />
            <EventAnalytics />
          </div>
          
          <div className="space-y-6">
            <GeographicDistribution />
            <TopContributors />
          </div>
        </div>

        {/* Recent Activities */}
        <RecentActivities />
      </main>
    </div>
  )
}
