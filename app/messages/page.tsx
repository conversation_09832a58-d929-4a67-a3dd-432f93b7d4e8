"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { ConversationsList } from "@/components/messages/conversations-list"
import { ChatWindow } from "@/components/messages/chat-window"
import { NewConversationDialog } from "@/components/messages/new-conversation-dialog"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MessageSquare, Plus, Search } from "lucide-react"
import { cn } from "@/lib/utils"

export default function MessagesPage() {
  const { status } = useSession()
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [showNewConversation, setShowNewConversation] = useState(false)

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
                Messages
              </h1>
              <p className="text-muted-foreground">
                Connect privately with fellow PROTEC alumni
              </p>
            </div>
            
            <Button 
              onClick={() => setShowNewConversation(true)}
              className="bg-protec-red hover:bg-protec-red/90"
            >
              <Plus className="mr-2 h-4 w-4" />
              New Message
            </Button>
          </div>

          {/* Messages Interface */}
          <div className="grid gap-6 lg:grid-cols-12 h-[calc(100vh-200px)]">
            {/* Conversations Sidebar */}
            <div className={cn(
              "lg:col-span-4 space-y-4",
              selectedConversationId && "hidden lg:block"
            )}>
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conversations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 rounded-full border-2 focus:border-primary"
                />
              </div>

              {/* Conversations List */}
              <Card className="flex-1 overflow-hidden shadow-lg border-2">
                <ConversationsList
                  searchTerm={searchTerm}
                  selectedConversationId={selectedConversationId}
                  onSelectConversation={setSelectedConversationId}
                />
              </Card>
            </div>

            {/* Chat Window */}
            <div className={cn(
              "lg:col-span-8",
              !selectedConversationId && "hidden lg:block"
            )}>
              <Card className="h-full shadow-lg border-2">
                {selectedConversationId ? (
                  <ChatWindow
                    conversationId={selectedConversationId}
                    onClose={() => setSelectedConversationId(null)}
                  />
                ) : (
                  <div className="h-full flex items-center justify-center bg-gradient-to-br from-muted/30 to-muted/10">
                    <div className="text-center space-y-6 p-8">
                      <div className="w-20 h-20 mx-auto bg-primary/10 rounded-full flex items-center justify-center animate-pulse">
                        <MessageSquare className="h-10 w-10 text-primary" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-semibold text-foreground">
                          Welcome to Messages
                        </h3>
                        <p className="text-muted-foreground max-w-md">
                          Connect privately with fellow PROTEC alumni. Select a conversation from the sidebar or start a new one.
                        </p>
                      </div>
                      <Button
                        onClick={() => setShowNewConversation(true)}
                        className="bg-primary hover:bg-primary/90 shadow-lg transition-all duration-200 hover:scale-105"
                        size="lg"
                      >
                        <Plus className="mr-2 h-5 w-5" />
                        Start New Conversation
                      </Button>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        </div>
      </main>

      {/* New Conversation Dialog */}
      <NewConversationDialog
        open={showNewConversation}
        onOpenChange={setShowNewConversation}
        onConversationCreated={(conversationId: string) => {
          setSelectedConversationId(conversationId)
          setShowNewConversation(false)
        }}
      />
    </div>
  )
}
