"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Users, 
  Calendar, 
  MessageSquare, 
  Heart, 
  Star,
  TrendingUp,
  Settings,
  Bell
} from "lucide-react"

export default function StyleGuidePage() {
  return (
    <div className="container-wide mx-auto p-6 space-y-12">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-protec-navy">
          PROTEC Design System
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          A comprehensive design system for the PROTEC Alumni Platform, 
          built with shadcn/ui and Tai<PERSON>wind CSS v4.
        </p>
      </div>

      {/* Brand Colors */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Brand Colors</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-protec-navy">PROTEC Navy</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="h-20 bg-protec-navy rounded-lg"></div>
              <div className="space-y-2 text-sm">
                <p><strong>Hex:</strong> #3B4A6B</p>
                <p><strong>HSL:</strong> 220 25% 33%</p>
                <p><strong>Usage:</strong> Headers, navigation, primary text</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-protec-red">PROTEC Red</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="h-20 bg-protec-red rounded-lg"></div>
              <div className="space-y-2 text-sm">
                <p><strong>Hex:</strong> #E31E24</p>
                <p><strong>HSL:</strong> 356 85% 52%</p>
                <p><strong>Usage:</strong> CTAs, highlights, accents</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>PROTEC Gray</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="h-20 bg-protec-gray rounded-lg border"></div>
              <div className="space-y-2 text-sm">
                <p><strong>Hex:</strong> #F5F6F7</p>
                <p><strong>HSL:</strong> 210 17% 96%</p>
                <p><strong>Usage:</strong> Backgrounds, subtle sections</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Typography */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Typography</h2>
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl font-bold text-protec-navy">Heading 1 - 4xl Bold</h1>
              <h2 className="text-3xl font-semibold text-protec-navy">Heading 2 - 3xl Semibold</h2>
              <h3 className="text-2xl font-semibold text-protec-navy">Heading 3 - 2xl Semibold</h3>
              <h4 className="text-xl font-medium text-protec-navy">Heading 4 - xl Medium</h4>
              <h5 className="text-lg font-medium text-protec-navy">Heading 5 - lg Medium</h5>
              <h6 className="text-base font-medium text-protec-navy">Heading 6 - base Medium</h6>
            </div>
            <div className="space-y-2">
              <p className="text-base">Body text - base regular</p>
              <p className="text-sm text-muted-foreground">Small text - sm muted</p>
              <p className="text-xs text-muted-foreground">Extra small text - xs muted</p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Buttons */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Buttons</h2>
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="flex flex-wrap gap-4">
              <Button className="bg-protec-red hover:bg-protec-red/90">
                Primary Button
              </Button>
              <Button variant="outline" className="border-protec-navy text-protec-navy">
                Secondary Button
              </Button>
              <Button variant="ghost">
                Ghost Button
              </Button>
              <Button variant="destructive">
                Destructive Button
              </Button>
            </div>
            <div className="flex flex-wrap gap-4">
              <Button size="sm" className="bg-protec-red hover:bg-protec-red/90">
                Small Button
              </Button>
              <Button size="lg" className="bg-protec-red hover:bg-protec-red/90">
                Large Button
              </Button>
              <Button disabled>
                Disabled Button
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Cards */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="card-hover">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-protec-red" />
                <span>Standard Card</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                A standard card with hover effects and proper spacing.
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-protec-red card-hover">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-protec-red" />
                <span>Branded Card</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                A card with PROTEC brand accent border.
              </p>
            </CardContent>
          </Card>

          <Card className="protec-gradient text-white">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Star className="h-5 w-5" />
                <span>Gradient Card</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm opacity-90">
                A card with PROTEC gradient background.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Form Elements */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Form Elements</h2>
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="Enter your email" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" placeholder="Enter your full name" />
              </div>
            </div>
            <div className="space-y-2">
              <Label>Progress Example</Label>
              <Progress value={65} className="h-2" />
              <p className="text-xs text-muted-foreground">65% complete</p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Badges and Avatars */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Badges & Avatars</h2>
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="flex flex-wrap gap-2">
              <Badge>Default Badge</Badge>
              <Badge variant="secondary">Secondary Badge</Badge>
              <Badge variant="destructive">Destructive Badge</Badge>
              <Badge variant="outline">Outline Badge</Badge>
              <Badge className="bg-protec-red text-white">PROTEC Badge</Badge>
            </div>
            <div className="flex items-center space-x-4">
              <Avatar>
                <AvatarImage src="/avatars/user.jpg" alt="User" />
                <AvatarFallback className="bg-protec-navy text-white">JD</AvatarFallback>
              </Avatar>
              <Avatar className="h-12 w-12">
                <AvatarFallback className="bg-protec-red text-white text-lg">AB</AvatarFallback>
              </Avatar>
              <Avatar className="h-16 w-16">
                <AvatarFallback className="bg-protec-gray text-protec-navy text-xl">CD</AvatarFallback>
              </Avatar>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Dashboard Components */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Dashboard Components</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            { title: "Total Alumni", value: "5,247", icon: Users, change: "+12%" },
            { title: "Active Events", value: "23", icon: Calendar, change: "+5%" },
            { title: "Community Posts", value: "1,247", icon: MessageSquare, change: "+18%" },
            { title: "Total Donations", value: "R2.1M", icon: Heart, change: "+25%" },
          ].map((stat) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title} className="card-hover border-l-4 border-l-protec-red/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </CardTitle>
                  <Icon className="h-4 w-4 text-protec-red" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">{stat.value}</div>
                  <p className="text-xs text-green-600 font-medium">{stat.change} from last month</p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </section>

      {/* Utility Classes */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-protec-navy">Utility Classes</h2>
        <Card>
          <CardContent className="p-6 space-y-4">
            <div className="p-4 protec-gradient-subtle rounded-lg">
              <p className="text-sm font-medium">.protec-gradient-subtle</p>
              <p className="text-xs text-muted-foreground">Subtle gradient background</p>
            </div>
            <div className="p-4 border rounded-lg card-hover">
              <p className="text-sm font-medium">.card-hover</p>
              <p className="text-xs text-muted-foreground">Enhanced hover effects</p>
            </div>
            <div className="p-4 border rounded-lg focus-ring" tabIndex={0}>
              <p className="text-sm font-medium">.focus-ring</p>
              <p className="text-xs text-muted-foreground">Consistent focus styling (click to focus)</p>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}
