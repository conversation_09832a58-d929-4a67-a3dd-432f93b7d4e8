import { MainNav } from "@/components/navigation/main-nav"
import { LandingAbout } from "@/components/landing/about"
import { LandingStats } from "@/components/landing/stats"
import { LandingFooter } from "@/components/landing/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Users, 
  Award, 
  Target, 
  Heart, 
  ArrowRight,
  Building,
  MapPin,
  Phone,
  Mail
} from "lucide-react"
import Link from "next/link"

const leadership = [
  {
    name: "Dr. <PERSON>",
    role: "Executive Director",
    bio: "Leading PROTEC's strategic vision with over 20 years in STEM education",
    image: "/team/sarah.jpg",
    initials: "<PERSON>"
  },
  {
    name: "Prof. <PERSON>",
    role: "Academic Director",
    bio: "Overseeing curriculum development and academic partnerships",
    image: "/team/michael.jpg",
    initials: "MJ"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Alumni Relations Director",
    bio: "Building and maintaining our global alumni network",
    image: "/team/nomsa.jpg",
    initials: "ND"
  },
  {
    name: "<PERSON>",
    role: "Industry Partnerships Director",
    bio: "Connecting PROTEC with leading technology companies",
    image: "/team/ahmed.jpg",
    initials: "AH"
  }
]

const offices = [
  {
    city: "Johannesburg",
    address: "123 PROTEC House, Braamfontein, Johannesburg 2001",
    phone: "+27 11 403 6861",
    email: "<EMAIL>",
    isHeadquarters: true
  },
  {
    city: "Cape Town",
    address: "456 Innovation Centre, Observatory, Cape Town 7925",
    phone: "+27 21 447 1234",
    email: "<EMAIL>",
    isHeadquarters: false
  },
  {
    city: "Durban",
    address: "789 Tech Hub, Umhlanga, Durban 4319",
    phone: "+27 31 566 7890",
    email: "<EMAIL>",
    isHeadquarters: false
  }
]

export default function AboutPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-br from-protec-navy to-protec-red py-20 sm:py-32">
          <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
          
          <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <Badge variant="outline" className="mb-6 border-white/30 bg-white/10 text-white">
                <Award className="mr-2 h-4 w-4" />
                About PROTEC
              </Badge>
              
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
                Empowering STEM Excellence Since 1982
              </h1>
              
              <p className="mt-6 text-xl leading-8 text-blue-100">
                For over four decades, PROTEC has been transforming lives through quality STEM education, 
                building a network of successful professionals who are shaping the future of technology and innovation.
              </p>
              
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button 
                  size="lg" 
                  className="bg-white text-protec-navy hover:bg-gray-100 px-8 py-3 text-lg"
                  asChild
                >
                  <Link href="/auth/signup">
                    Join Our Community
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-protec-navy px-8 py-3 text-lg"
                  asChild
                >
                  <Link href="/contact">
                    Contact Us
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* About Content */}
        <LandingAbout />

        {/* Leadership Team */}
        <section className="py-20 sm:py-32 bg-white">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center mb-16">
              <Badge variant="outline" className="mb-4 border-protec-navy/20 bg-protec-navy/5 text-protec-navy">
                <Users className="mr-2 h-4 w-4" />
                Leadership Team
              </Badge>
              <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
                Meet Our Leaders
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our experienced leadership team brings together decades of expertise in education, 
                technology, and community development.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {leadership.map((leader) => (
                <Card key={leader.name} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  <CardContent className="p-6 text-center">
                    <div className="w-24 h-24 mx-auto mb-4 bg-protec-navy/10 rounded-full flex items-center justify-center">
                      <span className="text-2xl font-bold text-protec-navy">
                        {leader.initials}
                      </span>
                    </div>
                    <h3 className="text-lg font-semibold text-protec-navy mb-1">
                      {leader.name}
                    </h3>
                    <p className="text-protec-red font-medium mb-3">
                      {leader.role}
                    </p>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {leader.bio}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Stats */}
        <LandingStats />

        {/* Offices */}
        <section className="py-20 sm:py-32 bg-gradient-to-b from-white to-protec-gray/30">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center mb-16">
              <Badge variant="outline" className="mb-4 border-protec-red/20 bg-protec-red/5 text-protec-red">
                <MapPin className="mr-2 h-4 w-4" />
                Our Locations
              </Badge>
              <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
                Find Us Across South Africa
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                With offices in major cities, we're accessible to students and alumni nationwide.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {offices.map((office) => (
                <Card key={office.city} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-protec-navy/10">
                        <Building className="h-6 w-6 text-protec-navy" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-protec-navy">
                          {office.city}
                        </h3>
                        {office.isHeadquarters && (
                          <Badge variant="outline" className="border-protec-red/20 bg-protec-red/5 text-protec-red text-xs">
                            Headquarters
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="space-y-3 text-sm text-gray-600">
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 text-protec-red mt-0.5 flex-shrink-0" />
                        <span>{office.address}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-protec-red flex-shrink-0" />
                        <span>{office.phone}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-protec-red flex-shrink-0" />
                        <span>{office.email}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 sm:py-32 bg-protec-navy">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to be part of our story?
              </h2>
              <p className="mt-6 text-lg leading-8 text-blue-100">
                Join thousands of PROTEC alumni who are building successful careers and making a difference in the world.
              </p>
              <div className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4">
                <Button 
                  size="lg" 
                  className="bg-protec-red hover:bg-protec-red/90 text-white px-8 py-3 text-lg w-full sm:w-auto"
                  asChild
                >
                  <Link href="/auth/signup">
                    Join the Network
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-protec-navy px-8 py-3 text-lg w-full sm:w-auto"
                  asChild
                >
                  <Link href="/contact">
                    Get in Touch
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <LandingFooter />
    </div>
  )
}