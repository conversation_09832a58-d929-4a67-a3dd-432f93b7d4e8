declare module 'node-payfast' {
  export interface PayFastConfig {
    merchant_id: string
    merchant_key: string
    passphrase: string
    sandbox: boolean
  }

  export interface PayFastPaymentData {
    merchant_id: string
    merchant_key: string
    return_url: string
    cancel_url: string
    notify_url: string
    name_first?: string
    name_last?: string
    email_address: string
    m_payment_id: string
    amount: string
    item_name: string
    item_description?: string
    custom_int1?: string
    custom_int2?: string
    custom_int3?: string
    custom_int4?: string
    custom_int5?: string
    custom_str1?: string
    custom_str2?: string
    custom_str3?: string
    custom_str4?: string
    custom_str5?: string
    email_confirmation?: string
    confirmation_address?: string
    payment_method?: string
    subscription_type?: string
    billing_date?: string
    recurring_amount?: string
    frequency?: string
    cycles?: string
  }

  export class PayFast {
    constructor(config: PayFastConfig)

    createPayment(data: PayFastPaymentData): {
      url: string
      data: PayFastPaymentData
    }

    createStringfromObject(data: PayFastPaymentData): string
    createSignature(urlString: string): string
    createPaymentObject(data: PayFastPaymentData, hash: string): PayFastPaymentData
    generatePaymentUrl(paymentObject: PayFastPaymentData): Promise<string>

    validateSignature(data: Record<string, string>, signature: string): boolean
    generateSignature(data: Record<string, string>): string
  }
}
