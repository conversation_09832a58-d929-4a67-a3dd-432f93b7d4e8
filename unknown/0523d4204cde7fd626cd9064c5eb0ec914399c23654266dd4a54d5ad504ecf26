import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function BrandDemoPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary text-primary-foreground py-8">
        <div className="container mx-auto px-4">
          <h1 className="font-heading text-4xl font-bold mb-2">
            PROTEC Alumni Platform
          </h1>
          <p className="font-body text-lg opacity-90">
            Brand Identity & Design System Demo
          </p>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 space-y-12">
        {/* Color Palette */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">Color Palette</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="bg-primary h-20 rounded-lg mb-2"></div>
              <p className="font-body text-sm font-medium">Primary Blue</p>
              <p className="text-xs text-muted-foreground">#012A5B</p>
            </div>
            <div className="text-center">
              <div className="bg-accent h-20 rounded-lg mb-2"></div>
              <p className="font-body text-sm font-medium">Accent Red</p>
              <p className="text-xs text-muted-foreground">#D71920</p>
            </div>
            <div className="text-center">
              <div className="bg-background border h-20 rounded-lg mb-2"></div>
              <p className="font-body text-sm font-medium">Background</p>
              <p className="text-xs text-muted-foreground">#FFFFFF</p>
            </div>
            <div className="text-center">
              <div className="bg-foreground h-20 rounded-lg mb-2"></div>
              <p className="font-body text-sm font-medium">Foreground</p>
              <p className="text-xs text-muted-foreground">#2C2C2C</p>
            </div>
            <div className="text-center">
              <div className="bg-muted h-20 rounded-lg mb-2"></div>
              <p className="font-body text-sm font-medium">Muted</p>
              <p className="text-xs text-muted-foreground">#F3F3F3</p>
            </div>
          </div>
        </section>

        {/* Typography */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">Typography</h2>
          <div className="space-y-6">
            <div>
              <h3 className="font-heading text-xl font-semibold mb-2">Headings (Poppins)</h3>
              <div className="space-y-2">
                <h1 className="font-heading text-4xl font-bold text-foreground">Heading 1 - 36px Bold</h1>
                <h2 className="font-heading text-3xl font-bold text-foreground">Heading 2 - 30px Bold</h2>
                <h3 className="font-heading text-2xl font-semibold text-foreground">Heading 3 - 24px Semibold</h3>
                <h4 className="font-heading text-xl font-semibold text-foreground">Heading 4 - 20px Semibold</h4>
              </div>
            </div>
            <div>
              <h3 className="font-heading text-xl font-semibold mb-2">Body Text (Inter)</h3>
              <p className="font-body text-base text-foreground mb-2">
                This is regular body text using Inter font family. It's designed for optimal readability 
                and accessibility across all devices and screen sizes.
              </p>
              <p className="font-body text-sm text-muted-foreground">
                This is smaller body text, often used for captions or secondary information.
              </p>
            </div>
            <div>
              <h3 className="font-heading text-xl font-semibold mb-2">Accent Text (Roboto Slab)</h3>
              <p className="font-accent text-lg font-medium text-primary">
                "This is accent text using Roboto Slab, perfect for quotes and highlights."
              </p>
            </div>
          </div>
        </section>

        {/* Buttons */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">Buttons</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="default">Primary Button</Button>
            <Button variant="destructive">Accent Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="secondary">Secondary Button</Button>
          </div>
          <div className="mt-4 flex flex-wrap gap-4">
            <Button variant="default" size="sm">Small</Button>
            <Button variant="default">Default</Button>
            <Button variant="default" size="lg">Large</Button>
          </div>
        </section>

        {/* Badges */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">Badges</h2>
          <div className="flex flex-wrap gap-4">
            <Badge variant="default">Engineering</Badge>
            <Badge variant="destructive">Featured</Badge>
            <Badge variant="outline">Alumni</Badge>
            <Badge variant="secondary">STEM</Badge>
          </div>
        </section>

        {/* Cards */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">Cards</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="font-heading">Alumni Spotlight</CardTitle>
                  <Badge variant="default">Engineering</Badge>
                </div>
                <CardDescription className="font-body">
                  Featured alumni making a difference in STEM
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border-l-4 border-l-accent pl-4">
                  <p className="font-body text-sm">
                    "PROTEC gave me the foundation to pursue my dreams in engineering. 
                    Today, I'm working on renewable energy solutions for South Africa."
                  </p>
                  <p className="font-accent text-sm mt-2 font-medium text-primary">- Sarah Mthembu, Class of 2015</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-background/80 backdrop-blur-sm border border-primary/10">
              <CardHeader>
                <CardTitle className="font-heading">Upcoming Event</CardTitle>
                <CardDescription className="font-body">
                  Join us for our next networking session
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-body font-medium">STEM Career Fair 2025</p>
                  <p className="font-body text-sm text-muted-foreground">March 15, 2025 • Cape Town</p>
                  <Button variant="destructive" size="sm" className="mt-3">
                    Register Now
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-protec text-white">
              <CardHeader>
                <CardTitle className="text-white font-heading">40+ Years of Impact</CardTitle>
                <CardDescription className="text-white/90">
                  Celebrating PROTEC's legacy since 1982
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/90">Alumni Network</span>
                    <span className="font-bold">10,000+</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/90">STEM Graduates</span>
                    <span className="font-bold">8,500+</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/90">Active Mentors</span>
                    <span className="font-bold">500+</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Gradients */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">Gradients & Effects</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="gradient-protec p-8 rounded-lg text-white text-center">
              <h3 className="font-heading text-2xl font-bold mb-2">Primary Gradient</h3>
              <p className="text-white/90">From PROTEC Primary to Accent</p>
            </div>
            <div className="hero-bg p-8 rounded-lg text-center border">
              <h3 className="font-heading text-2xl font-semibold mb-2">Subtle Background</h3>
              <p className="font-body text-muted-foreground">Gentle background effect</p>
            </div>
          </div>
          <div className="mt-6">
            <h3 className="gradient-text-protec text-4xl font-heading font-bold text-center">
              Gradient Text Effect
            </h3>
            <p className="text-center font-body text-muted-foreground mt-2">
              Perfect for hero sections and highlights
            </p>
          </div>
        </section>

        {/* South African Context */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">South African Context</h2>
          <Card>
            <CardHeader>
              <CardTitle className="font-heading">Localization Features</CardTitle>
              <CardDescription className="font-body">
                Designed for the South African market
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-heading text-lg font-semibold mb-2">Currency</h4>
                  <p className="font-body">All amounts displayed in <strong>R (ZAR)</strong></p>
                  <p className="font-body text-sm text-muted-foreground">Example: R 500.00 donation</p>
                </div>
                <div>
                  <h4 className="font-heading text-lg font-semibold mb-2">Diversity</h4>
                  <p className="font-body">Inclusive visuals representing South Africa's diverse alumni base</p>
                </div>
                <div>
                  <h4 className="font-heading text-lg font-semibold mb-2">Regional Events</h4>
                  <p className="font-body">Cape Town, Johannesburg, Durban, and other major cities</p>
                </div>
                <div>
                  <h4 className="font-heading text-lg font-semibold mb-2">Accessibility</h4>
                  <p className="font-body">WCAG 2.1 AA compliant with high contrast support</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Usage Examples */}
        <section>
          <h2 className="font-heading text-3xl font-bold mb-6">Usage Examples</h2>
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="font-heading">Navigation Bar</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-primary text-primary-foreground p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-white/20 rounded"></div>
                      <span className="font-heading font-bold">PROTEC Alumni</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="font-body text-white/90">Welcome, John</span>
                      <Button variant="destructive" size="sm">Donate</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="font-heading">Hero Section</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="hero-bg p-8 rounded-lg text-center">
                  <h1 className="gradient-text-protec text-4xl font-heading font-bold mb-4">
                    Connect. Grow. Impact.
                  </h1>
                  <p className="font-body text-lg text-foreground mb-6 max-w-2xl mx-auto">
                    Join thousands of PROTEC alumni making a difference in STEM careers across South Africa. 
                    Build lasting professional relationships and give back to the next generation.
                  </p>
                  <div className="flex flex-wrap justify-center gap-4">
                    <Button variant="default" size="lg">Join the Network</Button>
                    <Button variant="outline" size="lg">Learn More</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>

      {/* Footer */}
      <footer className="bg-foreground text-background py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p className="font-body text-background/90">
            © 2025 PROTEC Alumni Platform. Empowering STEM careers since 1982.
          </p>
        </div>
      </footer>
    </div>
  );
}