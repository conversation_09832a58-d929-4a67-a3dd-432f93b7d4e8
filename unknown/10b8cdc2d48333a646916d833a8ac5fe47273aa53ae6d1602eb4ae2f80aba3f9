# PROTEC Alumni Platform Design System

## Overview

The PROTEC Alumni Platform uses a comprehensive design system built on top of shadcn/ui components with Tailwind CSS v4. This document outlines the design tokens, components, and usage guidelines.

## Brand Colors

### Primary Colors

- **PROTEC Navy**: `#3B4A6B` (HSL: 220 25% 33%)
  - Primary brand color for headers, navigation, and key UI elements
  - Available as: `protec-navy`, `text-protec-navy`, `bg-protec-navy`, `border-protec-navy`

- **PROTEC Red**: `#E31E24` (HSL: 356 85% 52%)
  - Accent color for CTAs, highlights, and interactive elements
  - Available as: `protec-red`, `text-protec-red`, `bg-protec-red`, `border-protec-red`

- **PROTEC Gray**: `#F5F6F7` (HSL: 210 17% 96%)
  - Background color for subtle sections and cards
  - Available as: `protec-gray`, `bg-protec-gray`

### Color Scales

Each brand color includes a full scale from 50-900:

```css
/* Navy Scale */
--protec-navy-50: 220 25% 95%
--protec-navy-100: 220 25% 90%
--protec-navy-200: 220 25% 80%
--protec-navy-300: 220 25% 70%
--protec-navy-400: 220 25% 50%
--protec-navy: 220 25% 33%        /* Default */
--protec-navy-600: 220 25% 25%
--protec-navy-700: 220 25% 20%
--protec-navy-800: 220 25% 15%
--protec-navy-900: 220 25% 10%
```

## Typography

### Font Family
- **Primary**: Inter (Google Fonts)
- **Fallback**: ui-sans-serif, system-ui, sans-serif

### Font Sizes
- `text-xs`: 0.75rem (12px)
- `text-sm`: 0.875rem (14px)
- `text-base`: 1rem (16px)
- `text-lg`: 1.125rem (18px)
- `text-xl`: 1.25rem (20px)
- `text-2xl`: 1.5rem (24px)
- `text-3xl`: 1.875rem (30px)
- `text-4xl`: 2.25rem (36px)

### Font Weights
- `font-normal`: 400
- `font-medium`: 500
- `font-semibold`: 600
- `font-bold`: 700

## Spacing

### Standard Spacing Scale
- `space-1`: 0.25rem (4px)
- `space-2`: 0.5rem (8px)
- `space-3`: 0.75rem (12px)
- `space-4`: 1rem (16px)
- `space-6`: 1.5rem (24px)
- `space-8`: 2rem (32px)
- `space-12`: 3rem (48px)
- `space-16`: 4rem (64px)

### Custom Spacing
- `space-18`: 4.5rem (72px)
- `space-88`: 22rem (352px)

## Border Radius

- `rounded-sm`: calc(var(--radius) - 4px)
- `rounded-md`: calc(var(--radius) - 2px)
- `rounded-lg`: var(--radius)
- `rounded-xl`: calc(var(--radius) + 4px)

Default radius: `0.5rem` (8px)

## Shadows

- `shadow-xs`: Subtle shadow for cards
- `shadow-sm`: Small shadow for elevated elements
- `shadow-md`: Medium shadow for modals and dropdowns
- `shadow-lg`: Large shadow for major elevation changes

## Component Guidelines

### Cards
```tsx
// Standard card
<Card className="card-hover">
  <CardHeader>
    <CardTitle>Title</CardTitle>
  </CardHeader>
  <CardContent>
    Content
  </CardContent>
</Card>

// PROTEC branded card
<Card className="border-l-4 border-l-protec-red">
  <CardContent>
    Branded content
  </CardContent>
</Card>
```

### Buttons
```tsx
// Primary button (PROTEC Red)
<Button className="bg-protec-red hover:bg-protec-red/90">
  Primary Action
</Button>

// Secondary button (PROTEC Navy)
<Button variant="outline" className="border-protec-navy text-protec-navy">
  Secondary Action
</Button>
```

### Typography
```tsx
// Page title
<h1 className="text-3xl font-bold text-protec-navy">
  Page Title
</h1>

// Section heading
<h2 className="text-xl font-semibold text-protec-navy">
  Section Title
</h2>

// Body text
<p className="text-sm text-muted-foreground">
  Body content
</p>
```

## Utility Classes

### PROTEC Specific
- `.protec-gradient`: Navy to red gradient background
- `.protec-gradient-subtle`: Subtle gradient with opacity
- `.card-hover`: Enhanced hover effect for cards
- `.focus-ring`: Consistent focus styling
- `.loading-skeleton`: Loading state styling

### Animation
- `.animate-fade-in`: Fade in animation
- `.animate-slide-in`: Slide in animation
- `.animate-bounce-gentle`: Gentle bounce animation

### Layout
- `.container-narrow`: Max width 4xl container
- `.container-wide`: Max width 7xl container

## Dark Mode

The design system includes comprehensive dark mode support:

### Color Adjustments
- Navy colors become lighter in dark mode
- Red colors become slightly more vibrant
- Background becomes dark with proper contrast ratios

### Usage
```tsx
// Automatic dark mode support
<div className="bg-background text-foreground">
  Content adapts to theme
</div>

// Manual dark mode variants
<div className="bg-white dark:bg-gray-900">
  Manual theme handling
</div>
```

## Accessibility

### Color Contrast
- All color combinations meet WCAG AA standards
- Text contrast ratios are 4.5:1 or higher
- Interactive elements have sufficient contrast

### Focus Management
- All interactive elements have visible focus indicators
- Focus rings use the brand colors appropriately
- Keyboard navigation is fully supported

### Screen Readers
- Semantic HTML structure
- Proper ARIA labels and descriptions
- Meaningful alt text for images

## Responsive Design

### Breakpoints
- `sm`: 640px
- `md`: 768px
- `lg`: 1024px
- `xl`: 1280px
- `2xl`: 1536px

### Mobile-First Approach
All components are designed mobile-first with progressive enhancement for larger screens.

## Usage Examples

### Dashboard Card
```tsx
<Card className="border-l-4 border-l-protec-red card-hover">
  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
    <CardTitle className="text-sm font-medium">Total Alumni</CardTitle>
    <Users className="h-4 w-4 text-protec-red" />
  </CardHeader>
  <CardContent>
    <div className="text-2xl font-bold text-protec-navy">5,247</div>
    <p className="text-xs text-muted-foreground">+12% from last month</p>
  </CardContent>
</Card>
```

### Navigation Item
```tsx
<Link 
  href="/dashboard"
  className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-protec-navy hover:bg-protec-gray transition-colors focus-ring"
>
  <Home className="h-4 w-4" />
  <span>Dashboard</span>
</Link>
```

This design system ensures consistency across the entire PROTEC Alumni Platform while maintaining accessibility and usability standards.
