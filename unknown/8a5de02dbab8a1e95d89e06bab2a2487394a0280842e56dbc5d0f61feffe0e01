"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Users, Calendar, MessageSquare, Heart } from "lucide-react"

const stats = [
  {
    title: "Connections",
    value: "127",
    change: "+12%",
    changeType: "positive" as const,
    icon: Users,
    description: "Alumni in your network"
  },
  {
    title: "Events Attended",
    value: "8",
    change: "+2",
    changeType: "positive" as const,
    icon: Calendar,
    description: "This year"
  },
  {
    title: "Posts Shared",
    value: "23",
    change: "+5",
    changeType: "positive" as const,
    icon: MessageSquare,
    description: "Total posts"
  },
  {
    title: "Donations Made",
    value: "R2,500",
    change: "+R500",
    changeType: "positive" as const,
    icon: Heart,
    description: "Total contributed"
  }
]

export function DashboardStats() {
  return (
    <>
      {stats.map((stat) => {
        const Icon = stat.icon
        return (
          <Card key={stat.title} className="card-hover border-l-4 border-l-protec-red/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-protec-navy" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-protec-navy">{stat.value}</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span 
                  className={`font-medium ${
                    stat.changeType === 'positive' 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}
                >
                  {stat.change}
                </span>
                <span>{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </>
  )
}
