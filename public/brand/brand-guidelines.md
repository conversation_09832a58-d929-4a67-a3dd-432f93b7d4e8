# PROTEC Alumni Platform - Brand Guidelines

> **Note**: This project uses **Tailwind CSS v4** with CSS-first configuration and OKLCH color format for better consistency and modern design system practices.

## Overview
The PROTEC Alumni Platform represents a modern, inclusive, and STEM-driven identity that honours 40+ years of legacy in empowering South African youth.

## Brand Attributes
- **Mission**: <PERSON><PERSON><PERSON>te and connect PROTEC alumni across South Africa
- **Audience**: Young professionals, educators, sponsors, and alumni in STEM
- **Tone**: Bold, Professional, Legacy-driven, Empowering
- **Style**: Clean, contrast-rich, localised, accessible
- **Heritage**: PROTEC has promoted STEM careers since 1982

## Color Palette

### Core Colors
| Role | Token (Tailwind) | HEX | RGB | Usage |
|------|------------------|-----|-----|-------|
| Primary Blue | `protec-primary` | #012A5B | rgb(1, 42, 91) | Main brand color, headers, CTAs |
| Accent Red | `protec-accent` | #D71920 | rgb(215, 25, 32) | Highlights, alerts, secondary CTAs |
| White | `white` | #FFFFFF | rgb(255,255,255) | Backgrounds, text on dark |
| Black Text | `protec-foreground` | #2C2C2C | rgb(44,44,44) | Primary text color |
| Muted Grey | `protec-muted` | #F3F3F3 | rgb(243,243,243) | Subtle backgrounds, borders |

### Tailwind CSS v4 Configuration
```css
/* @theme directive in globals.css */
@theme {
  --color-protec-primary: oklch(0.2 0.15 240); /* #012A5B */
  --color-protec-accent: oklch(0.5 0.2 15);    /* #D71920 */
  --color-protec-foreground: oklch(0.25 0 0);  /* #2C2C2C */
  --color-protec-muted: oklch(0.96 0 0);       /* #F3F3F3 */
}
```

### CSS Variables (OKLCH Format)
```css
:root {
  --primary: oklch(0.2 0.15 240);         /* #012A5B - PROTEC Primary */
  --accent: oklch(0.5 0.2 15);            /* #D71920 - PROTEC Accent */
  --foreground: oklch(0.25 0 0);          /* #2C2C2C - Black Text */
  --muted: oklch(0.96 0 0);               /* #F3F3F3 - Muted Grey */
}
```

### shadcn/ui Semantic Classes (Recommended)
```css
/* Background Colors */
.bg-primary           /* PROTEC Primary Blue */
.bg-accent            /* PROTEC Accent Red */
.bg-muted             /* PROTEC Muted Grey */
.bg-background        /* White */
.bg-foreground        /* PROTEC Black Text */

/* Text Colors */
.text-primary         /* PROTEC Primary Blue */
.text-accent          /* PROTEC Accent Red */
.text-foreground      /* PROTEC Black Text */
.text-muted-foreground /* Muted text */

/* Border Colors */
.border-primary       /* PROTEC Primary Blue */
.border-accent        /* PROTEC Accent Red */
.border-l-primary     /* Left border accent */
.border-l-accent      /* Left border accent */
```

## Typography

### Font Families (Tailwind v4)
```css
/* @theme directive configuration */
@theme {
  --font-family-heading: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  --font-family-body: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-family-accent: 'Roboto Slab', ui-serif, serif;
}
```

### Usage Guidelines
| Type | Font | Size | Weight | Use Case |
|------|------|------|--------|----------|
| Headings | Poppins | 24px+ | Bold (700+) | Sections, hero areas |
| Body Text | Inter | 16px | Normal (400) | Paragraphs, articles |
| Accents | Roboto Slab | 14px+ | Medium (500) | Quotes, highlights |

### shadcn/ui Typography Classes
```css
.font-heading    /* Poppins */
.font-body       /* Inter */
.font-accent     /* Roboto Slab */

/* Semantic text colors */
.text-foreground      /* Primary text color */
.text-muted-foreground /* Secondary text color */
.text-primary         /* Brand primary color */
.text-accent          /* Brand accent color */
```

## UI Components

### shadcn/ui Components

#### Buttons
```jsx
<Button variant="default">Primary Button</Button>     // PROTEC Primary
<Button variant="destructive">Accent Button</Button>  // PROTEC Accent  
<Button variant="outline">Outline Button</Button>     // Outlined
<Button variant="ghost">Ghost Button</Button>         // Subtle
<Button variant="secondary">Secondary</Button>        // Muted
```

#### Badges
```jsx
<Badge variant="default">Primary</Badge>      // PROTEC Primary
<Badge variant="destructive">Accent</Badge>   // PROTEC Accent
<Badge variant="outline">Outline</Badge>      // Outlined
<Badge variant="secondary">Secondary</Badge>  // Muted
```

#### Cards
```jsx
<Card className="transition-all hover:shadow-lg">
  <CardHeader>
    <CardTitle className="font-heading">Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>Content</CardContent>
</Card>
```

#### PROTEC Brand Utilities
```css
.gradient-protec       /* Primary to accent gradient */
.gradient-text-protec  /* Gradient text effect */
.hero-bg              /* Subtle hero background */
.border-l-primary     /* Left border accent */
.border-l-accent      /* Left border accent */
```

## Logo Usage

### Guidelines
- **Minimum width**: 120px for digital use
- **Clear space**: Equal to the height of the logo mark
- **Backgrounds**: Use full color on light backgrounds, white version on dark
- **Never**: Stretch, recolor, or crop the logo

### Versions
- **Default**: Full color SVG for light backgrounds
- **Dark Mode**: White mono version for dark backgrounds
- **Favicon**: 32x32 PNG simplified version

## South African Context

### Localization
- Use **R (ZAR)** for all monetary references
- Reflect South Africa's diverse alumni base in imagery
- Include local events, regions, and languages when relevant
- Consider local holidays and cultural events

### Accessibility
- High contrast ratios (WCAG AA compliant)
- Support for `prefers-contrast: high`
- Support for `prefers-reduced-motion: reduce`
- Screen reader friendly markup

## Dark Mode

### Color Adaptations
```css
.dark {
  --background: 210 96% 18%;      /* PROTEC Primary as background */
  --foreground: 0 0% 95%;         /* Light text */
  --primary: 356 79% 47%;         /* PROTEC Accent as primary */
  --accent: 356 79% 47%;          /* PROTEC Accent */
}
```

## Implementation Examples

### Hero Section (shadcn/ui approach)
```jsx
<section className="hero-bg py-16">
  <h1 className="gradient-text-protec text-4xl font-heading font-bold">
    PROTEC Alumni Platform
  </h1>
  <p className="font-body text-lg text-foreground">
    Connect with PROTEC alumni across South Africa
  </p>
  <Button variant="default" size="lg">
    Join the Network
  </Button>
</section>
```

### Card Component (shadcn/ui approach)
```jsx
<Card className="transition-all hover:shadow-lg hover:-translate-y-1">
  <CardContent className="border-l-4 border-l-accent pl-4">
    <CardTitle className="font-heading text-xl">Alumni Spotlight</CardTitle>
    <p className="font-body">Featured alumni story...</p>
    <Badge variant="default">Engineering</Badge>
  </CardContent>
</Card>
```

### Navigation (shadcn/ui approach)
```jsx
<nav className="bg-primary text-primary-foreground">
  <div className="container mx-auto flex items-center justify-between">
    <img src="/logos/protec-white.svg" alt="PROTEC" className="h-8" />
    <Button variant="destructive">
      Donate
    </Button>
  </div>
</nav>
```

## File Structure
```
/public
├── logos/
│   ├── protec-primary.svg
│   ├── protec-white.svg
│   └── favicon.png
├── brand/
│   └── brand-guidelines.md
└── icons/
    └── [SVG system icons]
```

## Tools & Resources
- **Design System**: Tailwind CSS v4 + shadcn/ui (latest)
- **Configuration**: CSS-first with `@theme` directive
- **Fonts**: Google Fonts (Poppins, Inter, Roboto Slab)
- **Icons**: Lucide React
- **Color Format**: OKLCH for better consistency and perceptual uniformity
- **Accessibility**: WCAG 2.1 AA compliance
- **Framework**: Next.js 15 with React 19

---

*Last updated: January 2025*
*Version: 1.0*