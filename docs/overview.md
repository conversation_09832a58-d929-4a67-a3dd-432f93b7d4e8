# PROTEC Alumni Platform — Next.js + PWA Spec

## 1. High‑Level Architecture

```
[Next.js Frontend (App Router + PWA)]  
      ↕  
[Next.js API Routes (app/api) + tRPC]  
      ↕  
[PostgreSQL (AWS RDS) + Prisma ORM]  
      ↕  
[Third‑party Integrations & Services]
```

> **Optional mobile:** Wrap core UI in an \[Expo for Web + Expo Go] project, sharing React components via a `ui/` package.

---

## 2. Tech Stack & Key Libraries

| Layer          | Technology / Library                         |
| -------------- | -------------------------------------------- |
| **Framework**  | Next.js v14 (App Router, TypeScript)         |
| **PWA**        | `next-pwa` plugin + Workbox                  |
| **Mobile**     | Expo (optional)                              |
| **Styling/UI** | shadcn/ui (Tailwind CSS), Lucide‑React icons |
| **Auth**       | NextAuth.js (OAuth 2.0, Email Magic Link)    |
| **API**        | tRPC (auto‑typed), <PERSON>od (validation)          |
| **DB**         | PostgreSQL (AWS RDS, Africa‑Cape cluster)    |
| **ORM**        | Prisma (schema, migrations, Studio)          |
| **State**      | React Query (TanStack Query) + React Context |
| **Forms**      | React Hook Form + Zod resolver               |
| **Payments**   | PayFast, SnapScan, Ozow SDKs                 |
| **Analytics**  | Vercel Analytics, Google Analytics           |
| **CI/CD**      | GitHub Actions → Vercel + AWS infra          |
| **Monitoring** | Sentry, LogRocket                            |

---

## 3. Data Models (Prisma Schema)

```prisma
generator client { provider = "prisma-client-js" }

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Alumni {
  id             String    @id @default(uuid())
  email          String    @unique
  name           String
  photoUrl       String?
  bio            String?
  graduationYear Int
  programmes     String[]
  currentRole    String?
  skills         String[]
  province       String
  city           String
  socialLinks    Json?
  privacy        Json
  connections    Alumni[]  @relation("Connections", references: [id])
  connectedWith  Alumni[]  @relation("Connections", references: [id])
  activityLog    Activity[]
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}

model Event {
  id          String   @id @default(uuid())
  title       String
  description String
  category    String
  startTime   DateTime
  endTime     DateTime
  location    Json
  organizer   Alumni   @relation(fields: [organizerId], references: [id])
  organizerId String
  rsvps       RSVP[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Post {
  id        String    @id @default(uuid())
  author    Alumni    @relation(fields: [authorId], references: [id])
  authorId  String
  content   String
  mediaUrls String[]
  tags      String[]
  likes     String[]  // list of Alumni.id
  comments  Comment[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Donation {
  id            String   @id @default(uuid())
  alumni        Alumni   @relation(fields: [alumniId], references: [id])
  alumniId      String
  amountZAR     Float
  gateway       String
  transactionId String
  status        String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Activity {
  id        String   @id @default(uuid())
  type      String
  refId     String
  timestamp DateTime @default(now())
  alumni    Alumni   @relation(fields: [alumniId], references: [id])
  alumniId  String
}

model RSVP {
  id        String   @id @default(uuid())
  status    String
  responded DateTime @default(now())
  alumni    Alumni   @relation(fields: [alumniId], references: [id])
  alumniId  String
  event     Event    @relation(fields: [eventId], references: [id])
  eventId   String
}

model Comment {
  id        String   @id @default(uuid())
  text      String
  author    Alumni   @relation(fields: [authorId], references: [id])
  authorId  String
  post      Post     @relation(fields: [postId], references: [id])
  postId    String
  createdAt DateTime @default(now())
}
```

---

## 4. Authentication (NextAuth.js)

* **Providers**: Google, Facebook, GitHub, Email (Magic Link)
* **Session**: JWT strategy, persisted in secure HTTP‑only cookies
* **Callbacks**: Enrich session with `role`, `alumniId`

```ts
// [...nextauth].ts
import NextAuth from "next-auth";
import Providers from "next-auth/providers";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import prisma from "@/lib/prisma";

export default NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    Providers.Google({ clientId: process.env.GOOGLE_ID!, clientSecret: process.env.GOOGLE_SECRET! }),
    Providers.Email({ server: process.env.EMAIL_SERVER!, from: process.env.EMAIL_FROM! }),
  ],
  callbacks: {
    async session({ session, user }) {
      session.user.id = user.id;
      return session;
    },
  },
});
```

---

## 5. API Design (tRPC + Next.js API Routes)

* Use **tRPC** in `app/api/trpc/[trpc].ts` for end‑to‑end types
* Zod schemas for input validation
* Example router:

```ts
// src/server/routers/alumni.ts
import { z } from "zod";
import { createRouter } from "../trpc";

export const alumniRouter = createRouter()
  .middleware(isAuthed)
  .query("me", {
    resolve({ ctx }) {
      return ctx.prisma.alumni.findUnique({ where: { id: ctx.session.user.id } });
    },
  })
  .mutation("update", {
    input: z.object({ bio: z.string().optional(), photoUrl: z.string().url().optional() }),
    async resolve({ ctx, input }) {
      return ctx.prisma.alumni.update({
        where: { id: ctx.session.user.id },
        data: input,
      });
    },
  });
```

---

## 6. Frontend Structure (App Router)

```
/app
 ├─ layout.tsx          # root layout (PWA meta, manifest)
 ├─ page.tsx            # home/dashboard
 ├─ profile/
 │    ├─ page.tsx       # view profile
 │    └─ edit/page.tsx  # edit profile
 ├─ directory/
 │    └─ page.tsx       # search & list alumni
 ├─ events/
 │    ├─ page.tsx       # list
 │    └─ [id]/page.tsx  # details + RSVP
 ├─ feed/
 │    └─ page.tsx       # news feed
 ├─ donations/
 │    ├─ page.tsx       # donate form
 │    └─ history/page.tsx
 └─ api/
      └─ trpc/[trpc].ts
```

* **shadcn/ui** for cards, forms, buttons, navigation
* **next-themes** for dark/light mode
* **SWR** or **React Query** for client caching of tRPC queries

---

## 7. PWA Configuration

```js
// next.config.js
const withPWA = require("next-pwa");
module.exports = withPWA({
  pwa: { dest: "public", register: true, skipWaiting: true },
  images: { domains: ["res.cloudinary.com"] },
});
```

* Add `public/manifest.json`, `public/icons/`
* Service worker for offline caching of assets & API

---

## 8. Expo Wrapper (Optional)

* Structure:

  ```
  /packages/ui    ← shared React components
  /apps/web       ← Next.js project
  /apps/mobile    ← Expo project
  ```
* Use **monorepo** tooling (pnpm, Turborepo)
* Expo integrates `"@expo/next-adapter"` for Web parity

---

## 9. Payment Integration

* **Server‑side**: encapsulate PayFast, SnapScan, Ozow SDK calls in `donations` tRPC router
* **Client**: redirect to checkout in a Next.js `<WebView>` or `window.location` for PWA
* **Webhooks**: Next.js API route under `/app/api/payfast/webhook/route.ts`

---

## 10. CI/CD & Deployment

* **Vercel** for Next.js (Preview & Production)
* **GitHub Actions**:

  * Lint, typecheck, tests on PRs
  * On merge to `main`, deploy to Vercel
* **Expo**:

  * EAS Build & Submit for Android/iOS
* **Infra**:

  * AWS RDS (Postgres) backups + read replicas
  * Vercel environment vars, AWS Parameter Store

---

## 11. Monitoring & Testing

* **Unit**: Jest + React Testing Library
* **Integration**: tRPC SHARED server/client calls
* **E2E**: Playwright for Web; Detox for Expo
* **Error tracking**: Sentry in frontend & backend

---

## 12. Next Phases

1. **Push Notifications**: Vercel Edge Functions + FCM
2. **Offline Mode**: Background sync of RSVPs & drafts
3. **Mentor Matching**: Algorithmic pairing with Redis
4. **AI Chatbot**: GPT‑driven Q\&A in-app
