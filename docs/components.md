# PROTEC Alumni Platform - Component Library

## Overview

This document provides comprehensive documentation for all components used in the PROTEC Alumni Platform, including shadcn/ui components and custom PROTEC-specific components.

## Base UI Components (shadcn/ui)

### Button

```tsx
import { Button } from "@/components/ui/button"

// Primary PROTEC button
<Button className="bg-protec-red hover:bg-protec-red/90">
  Primary Action
</Button>

// Secondary PROTEC button
<Button variant="outline" className="border-protec-navy text-protec-navy">
  Secondary Action
</Button>

// Variants: default, destructive, outline, secondary, ghost, link
// Sizes: default, sm, lg, icon
```

### Card

```tsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Standard card with PROTEC styling
<Card className="card-hover border-l-4 border-l-protec-red">
  <CardHeader>
    <CardTitle className="text-protec-navy">Title</CardTitle>
  </CardHeader>
  <CardContent>
    Content goes here
  </CardContent>
</Card>
```

### Input & Form Elements

```tsx
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input 
    id="email" 
    type="email" 
    placeholder="Enter your email"
    className="focus-ring"
  />
</div>
```

### Select

```tsx
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select an option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>
```

### Progress

```tsx
import { Progress } from "@/components/ui/progress"

<Progress value={65} className="h-2" />
```

### Badge

```tsx
import { Badge } from "@/components/ui/badge"

<Badge className="bg-protec-red text-white">PROTEC Badge</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="outline">Outline</Badge>
```

### Avatar

```tsx
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

<Avatar>
  <AvatarImage src="/avatars/user.jpg" alt="User" />
  <AvatarFallback className="bg-protec-navy text-white">JD</AvatarFallback>
</Avatar>
```

## Custom PROTEC Components

### Dashboard Components

#### DashboardStats
```tsx
import { DashboardStats } from "@/components/dashboard/stats"

<DashboardStats />
```
Displays key metrics in a grid layout with PROTEC styling.

#### NetworkGrowth
```tsx
import { NetworkGrowth } from "@/components/dashboard/network-growth"

<NetworkGrowth />
```
Shows network growth metrics and progress towards goals.

#### DonationProgress
```tsx
import { DonationProgress } from "@/components/dashboard/donation-progress"

<DonationProgress />
```
Displays donation impact and progress towards giving goals.

#### RecentPosts
```tsx
import { RecentPosts } from "@/components/dashboard/recent-posts"

<RecentPosts />
```
Shows recent community posts with engagement metrics.

#### EventsCalendar
```tsx
import { EventsCalendar } from "@/components/dashboard/events-calendar"

<EventsCalendar />
```
Mini calendar view with upcoming events.

#### AlumniSpotlight
```tsx
import { AlumniSpotlight } from "@/components/dashboard/alumni-spotlight"

<AlumniSpotlight />
```
Features successful alumni with their achievements.

### Navigation Components

#### MainNav
```tsx
import { MainNav } from "@/components/navigation/main-nav"

<MainNav />
```
Main navigation component with PROTEC branding and user menu.

### Alumni Components

#### AlumniCard
```tsx
import { AlumniCard } from "@/components/alumni/alumni-card"

<AlumniCard alumni={alumniData} />
```
Displays alumni information in a card format.

#### AlumniDirectory
```tsx
import { AlumniDirectory } from "@/components/alumni/directory"

<AlumniDirectory />
```
Complete alumni directory with search and filtering.

### Events Components

#### EventCard
```tsx
import { EventCard } from "@/components/events/event-card"

<EventCard event={eventData} />
```
Displays event information with RSVP functionality.

#### EventsList
```tsx
import { EventsList } from "@/components/events/events-list"

<EventsList />
```
List view of events with filtering options.

### Donations Components

#### DonationForm
```tsx
import { DonationForm } from "@/components/donations/donation-form"

<DonationForm />
```
Complete donation form with payment gateway integration.

#### DonationStats
```tsx
import { DonationStats } from "@/components/donations/donation-stats"

<DonationStats />
```
Displays donation statistics and impact metrics.

## Layout Components

### Container Utilities

```tsx
// Narrow container (max-width: 4xl)
<div className="container-narrow mx-auto">
  Content
</div>

// Wide container (max-width: 7xl)
<div className="container-wide mx-auto">
  Content
</div>
```

## Styling Guidelines

### PROTEC Brand Classes

```css
/* Background Colors */
.bg-protec-navy     /* Navy background */
.bg-protec-red      /* Red background */
.bg-protec-gray     /* Gray background */

/* Text Colors */
.text-protec-navy   /* Navy text */
.text-protec-red    /* Red text */

/* Border Colors */
.border-protec-navy /* Navy border */
.border-protec-red  /* Red border */

/* Gradients */
.protec-gradient         /* Navy to red gradient */
.protec-gradient-subtle  /* Subtle gradient with opacity */
```

### Utility Classes

```css
/* Interactive Effects */
.card-hover         /* Enhanced hover effect for cards */
.focus-ring         /* Consistent focus styling */

/* Animations */
.animate-fade-in    /* Fade in animation */
.animate-slide-in   /* Slide in animation */
.animate-bounce-gentle /* Gentle bounce animation */

/* Loading States */
.loading-skeleton   /* Loading skeleton animation */
```

### Responsive Design

All components follow mobile-first responsive design:

```tsx
// Example responsive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
  {/* Content */}
</div>

// Responsive text sizes
<h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">
  Responsive Heading
</h1>
```

## Accessibility Features

### Focus Management
- All interactive elements have visible focus indicators
- Focus rings use PROTEC brand colors
- Keyboard navigation is fully supported

### Color Contrast
- All color combinations meet WCAG AA standards
- Text contrast ratios are 4.5:1 or higher

### Semantic HTML
- Proper heading hierarchy
- Meaningful alt text for images
- ARIA labels where appropriate

## Best Practices

### Component Composition
```tsx
// Good: Compose components with proper styling
<Card className="card-hover border-l-4 border-l-protec-red">
  <CardHeader>
    <CardTitle className="flex items-center space-x-2 text-protec-navy">
      <Icon className="h-5 w-5 text-protec-red" />
      <span>Title</span>
    </CardTitle>
  </CardHeader>
  <CardContent>
    <p className="text-sm text-muted-foreground">Content</p>
  </CardContent>
</Card>
```

### Consistent Spacing
```tsx
// Use consistent spacing classes
<div className="space-y-6">        {/* Vertical spacing */}
  <div className="space-x-4">      {/* Horizontal spacing */}
    {/* Content */}
  </div>
</div>
```

### Icon Usage
```tsx
// Consistent icon sizing and colors
<Icon className="h-4 w-4 text-protec-red" />  {/* Small icons */}
<Icon className="h-5 w-5 text-protec-navy" /> {/* Medium icons */}
<Icon className="h-6 w-6 text-muted-foreground" /> {/* Large icons */}
```

This component library ensures consistency across the entire PROTEC Alumni Platform while maintaining accessibility and usability standards.
