# Ozow Payment Gateway Integration

This document outlines the Ozow instant EFT payment gateway integration for the PROTEC Alumni platform.

## Overview

Ozow is South Africa's leading instant EFT payment solution that allows customers to make secure payments directly from their bank accounts without needing cards or additional apps.

## Features

- ✅ Instant EFT payments
- ✅ Direct bank account transfers
- ✅ Real-time payment notifications
- ✅ Secure hash verification
- ✅ Support for all major South African banks
- ⚠️ One-time payments only (no recurring payments)
- ✅ ZAR currency support only

## Configuration

### Environment Variables

Add the following to your `.env.local` file:

```env
# Ozow Configuration
OZOW_API_KEY="your-ozow-api-key"
OZOW_SITE_CODE="your-ozow-site-code"
OZOW_PRIVATE_KEY="your-ozow-private-key"
```

### Getting Ozow Credentials

1. **Sign up for Ozow Merchant Account**
   - Visit [Ozow Merchant Portal](https://merchant.ozow.com/)
   - Complete merchant registration and verification
   - Get approved for payment processing

2. **API Key**
   - Provided during account setup
   - Used for API authentication

3. **Site Code**
   - Your unique merchant site identifier
   - Used in payment requests and webhooks

4. **Private Key**
   - Used for hash generation and verification
   - Keep this secure and never expose publicly

## How It Works

### Payment Flow

1. **Payment Initiation**
   - User selects Ozow as payment method
   - System generates secure hash with payment details
   - User is redirected to Ozow payment page

2. **Bank Selection**
   - User selects their bank from available options
   - Redirected to their online banking platform
   - Completes payment using existing banking credentials

3. **Payment Processing**
   - Bank processes the instant EFT
   - Ozow receives confirmation from bank
   - Webhook notification sent to our system

4. **Payment Confirmation**
   - Webhook updates donation status
   - User receives confirmation
   - Funds are settled to merchant account

### Security

Ozow uses SHA-512 hash verification for security:

```
Hash = SHA512(SiteCode + Email + TransactionReference + Amount + SuccessUrl + CancelUrl + NotifyUrl + PrivateKey)
```

## API Integration

### Payment Creation

```typescript
const paymentRequest = {
  amount: 100.00,
  currency: 'ZAR',
  gateway: 'ozow',
  purpose: 'general',
  frequency: 'ONE_TIME',
  donorEmail: '<EMAIL>',
  donorName: 'John Doe',
  returnUrl: 'https://app.protec.co.za/donations/success',
  cancelUrl: 'https://app.protec.co.za/donations/cancel',
  notifyUrl: 'https://app.protec.co.za/api/webhooks/ozow',
  donationId: 'unique-donation-id'
}

const response = await paymentService.createPayment(paymentRequest)
```

### Payment URL Parameters

The generated payment URL includes:

- `SiteCode`: Your merchant site code
- `CountryCode`: Always 'ZA' for South Africa
- `CurrencyCode`: Always 'ZAR'
- `Amount`: Payment amount in cents
- `TransactionReference`: Unique reference (format: `PROTEC_{donationId}`)
- `BankReference`: Description shown to customer
- `Customer`: Customer email address
- `SuccessUrl`: Redirect URL for successful payments
- `CancelUrl`: Redirect URL for cancelled payments
- `ErrorUrl`: Redirect URL for failed payments
- `NotifyUrl`: Webhook URL for notifications
- `HashCheck`: SHA-512 security hash
- `IsTest`: Boolean for sandbox mode

### Webhook Handling

Webhooks are received at `/api/webhooks/ozow` with the following data:

```json
{
  "SiteCode": "your-site-code",
  "TransactionId": "12345",
  "TransactionReference": "PROTEC_donation-id",
  "Amount": "10000",
  "Status": "Complete",
  "StatusMessage": "Transaction Successful",
  "CurrencyCode": "ZAR",
  "IsTest": "false",
  "Hash": "sha512-hash-for-verification"
}
```

## Status Mapping

Ozow statuses are mapped to our internal statuses:

| Ozow Status | Our Status | Description |
|-------------|------------|-------------|
| Complete | completed | Payment successful |
| Cancelled | failed | User cancelled payment |
| Error | failed | Payment failed |
| Abandoned | failed | User abandoned payment |
| Pending | pending | Payment in progress |
| PendingPayment | pending | Awaiting bank confirmation |

## Limitations

### Recurring Payments

Ozow does not support automatic recurring payments. For recurring donations:

1. Initial payment is processed normally
2. System logs the recurring intent
3. Manual follow-up required for subsequent payments
4. Consider alternative gateways for true recurring payments

### Currency Support

- Only ZAR (South African Rand) is supported
- International donors should use PayPal

### Bank Support

Ozow supports all major South African banks:
- ABSA
- Standard Bank
- FNB
- Nedbank
- Capitec
- African Bank
- And many others

## Testing

### Sandbox Environment

Ozow provides a sandbox environment for testing:

1. Use test credentials provided by Ozow
2. Set `IsTest=true` in payment requests
3. Use test bank accounts for payment simulation
4. Webhook testing with ngrok or similar tools

### Test Scenarios

1. **Successful Payment**
   - Complete payment flow with test bank
   - Verify webhook received with "Complete" status
   - Check donation status updated to "completed"

2. **Cancelled Payment**
   - Cancel payment during bank selection
   - Verify webhook received with "Cancelled" status
   - Check status remains "pending" or updates to "failed"

3. **Hash Verification**
   - Test with valid hash
   - Test with invalid hash
   - Verify security measures work correctly

## Monitoring

### Payment Tracking

Monitor Ozow payments through:

1. **Ozow Merchant Dashboard**
   - Real-time transaction status
   - Settlement reports
   - Transaction history

2. **Application Logs**
   - Webhook processing logs
   - Payment creation logs
   - Hash verification logs

3. **Database Queries**
   - Donation status tracking
   - Payment reconciliation
   - Activity audit trails

## Troubleshooting

### Common Issues

1. **Hash Verification Failed**
   - Check private key configuration
   - Verify parameter order in hash calculation
   - Ensure all parameters are included

2. **Webhook Not Received**
   - Check webhook URL configuration
   - Verify firewall/security settings
   - Test webhook endpoint manually

3. **Payment Redirect Issues**
   - Check success/cancel URL configuration
   - Verify URL accessibility
   - Test redirect flow

### Support

For Ozow-specific issues:
- Email: <EMAIL>
- Phone: +27 21 813 9800
- Documentation: Contact Ozow support for API docs
- Merchant Portal: https://merchant.ozow.com/

## Production Checklist

- [ ] Ozow merchant account approved
- [ ] API credentials configured
- [ ] Hash generation tested
- [ ] Webhook endpoint tested
- [ ] SSL certificate valid
- [ ] Error handling implemented
- [ ] Monitoring set up
- [ ] Bank testing completed
- [ ] Documentation updated
- [ ] Team training completed

## Benefits

- **Instant Settlement**: Funds available immediately
- **Low Fees**: Competitive transaction fees (typically 1.5%)
- **High Success Rate**: Direct bank integration reduces failures
- **User Experience**: Familiar banking interface for customers
- **Security**: Bank-grade security and fraud protection
- **Coverage**: Supports all major South African banks
