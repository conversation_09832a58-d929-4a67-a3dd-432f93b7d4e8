"use client"

import { useState } from "react"
import { UserRole } from "@/lib/types/user"
import { api } from "@/components/providers/trpc-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from "@/components/ui/dropdown-menu"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  MoreHorizontal, 
  Shield, 
  UserCheck, 
  UserX, 
  Calendar,
  MapPin,
  Briefcase,
  Mail,
  Phone
} from "lucide-react"
import { toast } from "sonner"
import { ROLE_DISPLAY_NAMES } from "@/lib/types/user"

interface UserManagementProps {
  searchTerm: string
  roleFilter: UserRole | "all"
  statusFilter: "all" | "active" | "inactive"
}

export function UserManagement({ searchTerm, roleFilter, statusFilter }: UserManagementProps) {
  const [selectedUser, setSelectedUser] = useState<string | null>(null)
  const [actionType, setActionType] = useState<"role" | "status" | null>(null)
  const [newRole, setNewRole] = useState<UserRole>(UserRole.ALUMNI)
  const [newStatus, setNewStatus] = useState<boolean>(true)

  const { data: usersData, isLoading, refetch } = api.users.getAll.useQuery({
    search: searchTerm || undefined,
    role: roleFilter !== "all" ? roleFilter : undefined,
    isActive: statusFilter === "all" ? undefined : statusFilter === "active",
    limit: 50,
  })

  const updateRoleMutation = api.users.updateRole.useMutation({
    onSuccess: () => {
      toast.success("User role updated successfully")
      refetch()
      setSelectedUser(null)
      setActionType(null)
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update user role")
    },
  })

  const updateStatusMutation = api.users.updateStatus.useMutation({
    onSuccess: () => {
      toast.success("User status updated successfully")
      refetch()
      setSelectedUser(null)
      setActionType(null)
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update user status")
    },
  })

  const handleRoleChange = () => {
    if (selectedUser) {
      updateRoleMutation.mutate({
        userId: selectedUser,
        role: newRole,
      })
    }
  }

  const handleStatusChange = () => {
    if (selectedUser) {
      updateStatusMutation.mutate({
        userId: selectedUser,
        isActive: newStatus,
      })
    }
  }

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "bg-red-100 text-red-800"
      case UserRole.DONOR_COORDINATOR:
        return "bg-green-100 text-green-800"
      case UserRole.EVENT_ORGANIZER:
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-gray-200 rounded-full animate-pulse" />
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-1/3" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const users = usersData?.users || []

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Users ({users.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={user.photoUrl || ""} alt={user.name} />
                    <AvatarFallback className="bg-protec-navy text-white">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{user.name}</h3>
                      <Badge 
                        variant="secondary" 
                        className={getRoleBadgeColor(user.role)}
                      >
                        {ROLE_DISPLAY_NAMES[user.role]}
                      </Badge>
                      <Badge variant={user.isActive ? "default" : "destructive"}>
                        {user.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span className="flex items-center">
                        <Mail className="mr-1 h-3 w-3" />
                        {user.email}
                      </span>
                      {user.currentRole && (
                        <span className="flex items-center">
                          <Briefcase className="mr-1 h-3 w-3" />
                          {user.currentRole}
                        </span>
                      )}
                      {user.company && (
                        <span>@ {user.company}</span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span className="flex items-center">
                        <MapPin className="mr-1 h-3 w-3" />
                        {user.city}, {user.province}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="mr-1 h-3 w-3" />
                        Class of {user.graduationYear}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="text-right text-sm text-muted-foreground">
                    <div>{user._count.posts} posts</div>
                    <div>{user._count.connections} connections</div>
                    <div>{user._count.donations} donations</div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedUser(user.id)
                          setNewRole(user.role)
                          setActionType("role")
                        }}
                      >
                        <Shield className="mr-2 h-4 w-4" />
                        Change Role
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedUser(user.id)
                          setNewStatus(!user.isActive)
                          setActionType("status")
                        }}
                      >
                        {user.isActive ? (
                          <>
                            <UserX className="mr-2 h-4 w-4" />
                            Deactivate User
                          </>
                        ) : (
                          <>
                            <UserCheck className="mr-2 h-4 w-4" />
                            Activate User
                          </>
                        )}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
            
            {users.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No users found matching your criteria.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Role Change Dialog */}
      <AlertDialog open={actionType === "role"} onOpenChange={() => setActionType(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change User Role</AlertDialogTitle>
            <AlertDialogDescription>
              Select a new role for this user. This will change their permissions immediately.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="py-4">
            <Select value={newRole} onValueChange={(value) => setNewRole(value as UserRole)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={UserRole.ALUMNI}>Alumni</SelectItem>
                <SelectItem value={UserRole.EVENT_ORGANIZER}>Event Organizer</SelectItem>
                <SelectItem value={UserRole.DONOR_COORDINATOR}>Donor Coordinator</SelectItem>
                <SelectItem value={UserRole.ADMIN}>Administrator</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleRoleChange}>
              Update Role
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Change Dialog */}
      <AlertDialog open={actionType === "status"} onOpenChange={() => setActionType(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {newStatus ? "Activate User" : "Deactivate User"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {newStatus 
                ? "This user will regain access to the platform."
                : "This user will lose access to the platform immediately."
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStatusChange}>
              {newStatus ? "Activate" : "Deactivate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
