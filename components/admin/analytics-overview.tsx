"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import {
  Users,
  Calendar,
  MessageSquare,
  Heart,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign
} from "lucide-react"

interface MetricCardProps {
  title: string
  value: string | number
  change: number
  trend: "up" | "down"
  icon: React.ElementType
  prefix?: string
  suffix?: string
}

function MetricCard({ title, value, change, trend, icon: Icon, prefix = "", suffix = "" }: MetricCardProps) {
  const isPositive = trend === "up"
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={`h-4 w-4 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-protec-navy">
          {prefix}{typeof value === 'number' ? value.toLocaleString() : value}{suffix}
        </div>
        <div className="flex items-center space-x-2 mt-2">
          {isPositive ? (
            <TrendingUp className="h-4 w-4 text-green-600" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-600" />
          )}
          <Badge 
            variant="secondary" 
            className={isPositive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
          >
            {isPositive ? "+" : ""}{change.toFixed(1)}%
          </Badge>
          <span className="text-xs text-muted-foreground">vs last period</span>
        </div>
      </CardContent>
    </Card>
  )
}

function MetricSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-20 mb-2" />
        <div className="flex items-center space-x-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-12" />
          <Skeleton className="h-3 w-16" />
        </div>
      </CardContent>
    </Card>
  )
}

export function AnalyticsOverview() {
  const { data: overviewData, isLoading, error } = api.analytics.getOverviewStats.useQuery({
    timeRange: '30d',
    compareWithPrevious: true
  })

  if (error) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-full">
          <CardContent className="pt-6">
            <p className="text-center text-red-600">Failed to load analytics data</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading || !overviewData) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <MetricSkeleton key={i} />
        ))}
      </div>
    )
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <MetricCard
        title="Total Alumni"
        value={overviewData.totalUsers.current}
        change={overviewData.totalUsers.change}
        trend={overviewData.totalUsers.trend}
        icon={Users}
      />

      <MetricCard
        title="Active Users (30d)"
        value={overviewData.activeUsers.current}
        change={overviewData.activeUsers.change}
        trend={overviewData.activeUsers.trend}
        icon={Activity}
      />

      <MetricCard
        title="Total Events"
        value={overviewData.totalEvents.current}
        change={overviewData.totalEvents.change}
        trend={overviewData.totalEvents.trend}
        icon={Calendar}
      />

      <MetricCard
        title="Community Posts"
        value={overviewData.totalPosts.current}
        change={overviewData.totalPosts.change}
        trend={overviewData.totalPosts.trend}
        icon={MessageSquare}
      />

      <MetricCard
        title="Total Donations"
        value={overviewData.totalDonations.current}
        change={overviewData.totalDonations.change}
        trend={overviewData.totalDonations.trend}
        icon={DollarSign}
        prefix="R"
      />

      <MetricCard
        title="Engagement Rate"
        value={overviewData.engagement.current}
        change={overviewData.engagement.change}
        trend={overviewData.engagement.trend}
        icon={Heart}
        suffix="%"
      />
    </div>
  )
}
