"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Award, 
  TrendingUp, 
  Users, 
  DollarSign,
  Calendar,
  MessageCircle
} from "lucide-react"

interface TopContributorsProps {
  className?: string
}

export function TopContributors({ className }: TopContributorsProps) {
  // Mock data - replace with real API calls
  const topDonors = [
    {
      id: '1',
      name: '<PERSON>',
      photoUrl: null,
      totalDonated: 15000,
      donationCount: 12,
      lastDonation: '2024-03-15',
      type: 'recurring'
    },
    {
      id: '2',
      name: '<PERSON>',
      photoUrl: null,
      totalDonated: 12500,
      donationCount: 8,
      lastDonation: '2024-03-10',
      type: 'one-time'
    },
    {
      id: '3',
      name: '<PERSON>',
      photoUrl: null,
      totalDonated: 10000,
      donationCount: 15,
      lastDonation: '2024-03-12',
      type: 'recurring'
    }
  ]

  const topVolunteers = [
    {
      id: '1',
      name: '<PERSON>',
      photoUrl: null,
      hoursVolunteered: 120,
      eventsHelped: 8,
      lastActivity: '2024-03-14',
      role: 'Event Coordinator'
    },
    {
      id: '2',
      name: 'David Wilson',
      photoUrl: null,
      hoursVolunteered: 95,
      eventsHelped: 6,
      lastActivity: '2024-03-13',
      role: 'Mentor'
    },
    {
      id: '3',
      name: 'Lisa Anderson',
      photoUrl: null,
      hoursVolunteered: 87,
      eventsHelped: 5,
      lastActivity: '2024-03-11',
      role: 'Workshop Leader'
    }
  ]

  const topNetworkers = [
    {
      id: '1',
      name: 'Robert Taylor',
      photoUrl: null,
      connections: 156,
      messagesExchanged: 234,
      eventsAttended: 12,
      lastActive: '2024-03-15'
    },
    {
      id: '2',
      name: 'Jennifer White',
      photoUrl: null,
      connections: 142,
      messagesExchanged: 189,
      eventsAttended: 10,
      lastActive: '2024-03-14'
    },
    {
      id: '3',
      name: 'Mark Johnson',
      photoUrl: null,
      connections: 128,
      messagesExchanged: 167,
      eventsAttended: 9,
      lastActive: '2024-03-13'
    }
  ]

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Top Donors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-green-600" />
            <span>Top Donors</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topDonors.map((donor, index) => (
              <div key={donor.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-green-600 text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={donor.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(donor.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{donor.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {donor.donationCount} donations • Last: {new Date(donor.lastDonation).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">R{donor.totalDonated.toLocaleString()}</div>
                  <Badge 
                    variant={donor.type === 'recurring' ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {donor.type}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Volunteers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5 text-blue-600" />
            <span>Top Volunteers</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topVolunteers.map((volunteer, index) => (
              <div key={volunteer.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={volunteer.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(volunteer.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{volunteer.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {volunteer.role} • {volunteer.eventsHelped} events
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{volunteer.hoursVolunteered}h</div>
                  <div className="text-xs text-muted-foreground">
                    Last: {new Date(volunteer.lastActivity).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Networkers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-purple-600" />
            <span>Top Networkers</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topNetworkers.map((networker, index) => (
              <div key={networker.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-purple-600 text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={networker.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(networker.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{networker.name}</div>
                    <div className="text-sm text-muted-foreground flex items-center space-x-2">
                      <span>{networker.connections} connections</span>
                      <span>•</span>
                      <span>{networker.messagesExchanged} messages</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{networker.eventsAttended} events</div>
                  <div className="text-xs text-muted-foreground">
                    Active: {new Date(networker.lastActive).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-protec-red" />
            <span>Contribution Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                R{topDonors.reduce((sum, donor) => sum + donor.totalDonated, 0).toLocaleString()}
              </div>
              <div className="text-sm text-green-700">Total from Top Donors</div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {topVolunteers.reduce((sum, volunteer) => sum + volunteer.hoursVolunteered, 0)}h
              </div>
              <div className="text-sm text-blue-700">Total Volunteer Hours</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {topNetworkers.reduce((sum, networker) => sum + networker.connections, 0)}
              </div>
              <div className="text-sm text-purple-700">Total Connections Made</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
