"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  MapPin, 
  Globe, 
  Users, 
  TrendingUp,
  Building,
  Flag
} from "lucide-react"

interface GeographicDistributionProps {
  className?: string
}

export function GeographicDistribution({ className }: GeographicDistributionProps) {
  // Mock data - replace with real API calls
  const provinceData = [
    { province: 'Western Cape', count: 456, percentage: 35, growth: 12.5 },
    { province: 'Gauteng', count: 389, percentage: 30, growth: 8.3 },
    { province: 'KwaZulu-Natal', count: 234, percentage: 18, growth: 15.2 },
    { province: 'Eastern Cape', count: 123, percentage: 9, growth: 5.7 },
    { province: 'Limpopo', count: 67, percentage: 5, growth: 22.1 },
    { province: 'Mpumalanga', count: 34, percentage: 3, growth: -2.3 }
  ]

  const cityData = [
    { city: 'Cape Town', province: 'Western Cape', count: 345, percentage: 26.5 },
    { city: 'Johannesburg', province: 'Gauteng', count: 289, percentage: 22.2 },
    { city: 'Durban', province: 'KwaZulu-Natal', count: 178, percentage: 13.7 },
    { city: 'Pretoria', province: 'Gauteng', count: 100, percentage: 7.7 },
    { city: 'Port Elizabeth', province: 'Eastern Cape', count: 89, percentage: 6.8 },
    { city: 'Bloemfontein', province: 'Free State', count: 67, percentage: 5.1 },
    { city: 'Pietermaritzburg', province: 'KwaZulu-Natal', count: 56, percentage: 4.3 },
    { city: 'East London', province: 'Eastern Cape', count: 34, percentage: 2.6 }
  ]

  const internationalData = [
    { country: 'United Kingdom', count: 45, percentage: 35, flag: '🇬🇧' },
    { country: 'United States', count: 32, percentage: 25, flag: '🇺🇸' },
    { country: 'Australia', count: 28, percentage: 22, flag: '🇦🇺' },
    { country: 'Canada', count: 15, percentage: 12, flag: '🇨🇦' },
    { country: 'Germany', count: 8, percentage: 6, flag: '🇩🇪' }
  ]

  const industryByLocation = [
    { location: 'Cape Town', industries: ['Technology', 'Finance', 'Tourism'] },
    { location: 'Johannesburg', industries: ['Finance', 'Mining', 'Manufacturing'] },
    { location: 'Durban', industries: ['Logistics', 'Manufacturing', 'Agriculture'] },
    { location: 'International', industries: ['Technology', 'Consulting', 'Academia'] }
  ]

  const totalAlumni = provinceData.reduce((sum, province) => sum + province.count, 0)
  const internationalTotal = internationalData.reduce((sum, country) => sum + country.count, 0)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">South African Alumni</CardTitle>
            <Flag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAlumni.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">
              Across {provinceData.length} provinces
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">International Alumni</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{internationalTotal}</div>
            <div className="text-xs text-muted-foreground">
              Across {internationalData.length} countries
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Cities</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{cityData.length}</div>
            <div className="text-xs text-muted-foreground">
              Major urban centers
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Provincial Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5" />
            <span>Distribution by Province</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {provinceData.map((province) => (
              <div key={province.province} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{province.province}</span>
                    <Badge variant="secondary">{province.count} alumni</Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      {province.percentage}%
                    </span>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className={`h-3 w-3 ${province.growth > 0 ? 'text-green-500' : 'text-red-500'}`} />
                      <span className={`text-xs ${province.growth > 0 ? 'text-green-500' : 'text-red-500'}`}>
                        {province.growth > 0 ? '+' : ''}{province.growth}%
                      </span>
                    </div>
                  </div>
                </div>
                <Progress value={province.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* City Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5" />
            <span>Top Cities</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {cityData.map((city, index) => (
              <div key={city.city} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-protec-navy text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium">{city.city}</div>
                    <div className="text-sm text-muted-foreground">{city.province}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{city.count} alumni</div>
                  <div className="text-sm text-muted-foreground">{city.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* International Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>International Alumni</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {internationalData.map((country) => (
              <div key={country.country} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{country.flag}</span>
                    <span className="font-medium">{country.country}</span>
                    <Badge variant="outline">{country.count} alumni</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {country.percentage}%
                  </div>
                </div>
                <Progress value={country.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Industry Clusters by Location */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Industry Clusters by Location</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {industryByLocation.map((location) => (
              <div key={location.location} className="space-y-2">
                <div className="font-medium">{location.location}</div>
                <div className="flex flex-wrap gap-2">
                  {location.industries.map((industry) => (
                    <Badge key={industry} variant="secondary" className="text-xs">
                      {industry}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Growth Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Growth Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-800">Fastest Growing</span>
              </div>
              <p className="text-sm text-green-700">
                Limpopo shows the highest growth rate at 22.1%, indicating strong regional expansion.
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Globe className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">International Presence</span>
              </div>
              <p className="text-sm text-blue-700">
                Strong presence in English-speaking countries, with UK leading international alumni base.
              </p>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex items-center space-x-2 mb-2">
                <Building className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-purple-800">Urban Concentration</span>
              </div>
              <p className="text-sm text-purple-700">
                70% of alumni are concentrated in the top 3 metropolitan areas.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
