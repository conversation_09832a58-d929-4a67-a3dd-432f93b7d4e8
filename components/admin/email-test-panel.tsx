"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  Mail, 
  Send, 
  CheckCircle, 
  AlertTriangle, 
  Loader2,
  Settings,
  TestTube,
  User,
  Sparkles
} from "lucide-react"
import { toast } from "sonner"

interface EmailConfig {
  host: string
  port: string
  secure: boolean
  from: string
  hasUser: boolean
  hasPassword: boolean
}

export function EmailTestPanel() {
  const [isLoading, setIsLoading] = useState(false)
  const [config, setConfig] = useState<EmailConfig | null>(null)
  const [testEmail, setTestEmail] = useState("")
  const [testName, setTestName] = useState("")
  const [customSubject, setCustomSubject] = useState("")
  const [customContent, setCustomContent] = useState("")
  const [results, setResults] = useState<any[]>([])

  const loadConfig = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/test-email')
      const data = await response.json()
      
      if (data.success) {
        setConfig(data.configuration)
        toast.success("Email configuration loaded")
      } else {
        toast.error(data.error || "Failed to load configuration")
      }
    } catch (error) {
      toast.error("Failed to load email configuration")
    } finally {
      setIsLoading(false)
    }
  }

  const testEmailFunction = async (type: string, additionalData?: any) => {
    if (!testEmail && type !== 'connection') {
      toast.error("Please enter a test email address")
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          email: testEmail,
          testData: additionalData
        })
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success(data.message)
        setResults(prev => [...prev, {
          type,
          timestamp: new Date().toISOString(),
          success: true,
          message: data.message,
          messageId: data.messageId
        }])
      } else {
        toast.error(data.error || "Test failed")
        setResults(prev => [...prev, {
          type,
          timestamp: new Date().toISOString(),
          success: false,
          error: data.error,
          details: data.details
        }])
      }
    } catch (error) {
      toast.error("Test failed")
      setResults(prev => [...prev, {
        type,
        timestamp: new Date().toISOString(),
        success: false,
        error: "Network error"
      }])
    } finally {
      setIsLoading(false)
    }
  }

  const clearResults = () => {
    setResults([])
    toast.success("Results cleared")
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Service Testing
          </CardTitle>
          <CardDescription>
            Test and verify email functionality for magic links and notifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Button onClick={loadConfig} disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Settings className="mr-2 h-4 w-4" />
                )}
                Load Configuration
              </Button>
              
              {config && (
                <Badge variant="outline" className="text-green-600 border-green-200">
                  <CheckCircle className="mr-1 h-3 w-3" />
                  Configuration Loaded
                </Badge>
              )}
            </div>

            {config && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <Label className="text-sm font-medium">SMTP Host</Label>
                  <p className="text-sm text-gray-600">{config.host}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Port</Label>
                  <p className="text-sm text-gray-600">{config.port} {config.secure ? '(Secure)' : '(Insecure)'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">From Address</Label>
                  <p className="text-sm text-gray-600">{config.from}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Authentication</Label>
                  <p className="text-sm text-gray-600">
                    {config.hasUser && config.hasPassword ? (
                      <span className="text-green-600">✓ Configured</span>
                    ) : (
                      <span className="text-red-600">✗ Missing credentials</span>
                    )}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="basic" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Tests</TabsTrigger>
          <TabsTrigger value="magic-link">Magic Links</TabsTrigger>
          <TabsTrigger value="welcome">Welcome Email</TabsTrigger>
          <TabsTrigger value="custom">Custom Email</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="h-4 w-4" />
                Connection Test
              </CardTitle>
              <CardDescription>
                Test the SMTP server connection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={() => testEmailFunction('connection')}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                Test Connection
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="magic-link" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Magic Link Test
              </CardTitle>
              <CardDescription>
                Send a test magic link email
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="magic-email">Test Email Address</Label>
                <Input
                  id="magic-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                />
              </div>
              
              <Button 
                onClick={() => testEmailFunction('magic-link')}
                disabled={isLoading || !testEmail}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Send Test Magic Link
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="welcome" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Welcome Email Test
              </CardTitle>
              <CardDescription>
                Send a test welcome email for new users
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="welcome-email">Test Email Address</Label>
                <Input
                  id="welcome-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="welcome-name">Test Name</Label>
                <Input
                  id="welcome-name"
                  placeholder="Test User"
                  value={testName}
                  onChange={(e) => setTestName(e.target.value)}
                />
              </div>
              
              <Button 
                onClick={() => testEmailFunction('welcome', { name: testName || 'Test User' })}
                disabled={isLoading || !testEmail}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Send Test Welcome Email
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Custom Email Test
              </CardTitle>
              <CardDescription>
                Send a custom test email
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="custom-email">Test Email Address</Label>
                <Input
                  id="custom-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="custom-subject">Subject</Label>
                <Input
                  id="custom-subject"
                  placeholder="Test Email Subject"
                  value={customSubject}
                  onChange={(e) => setCustomSubject(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="custom-content">HTML Content</Label>
                <Textarea
                  id="custom-content"
                  placeholder="<h1>Test Email</h1><p>This is a test email.</p>"
                  value={customContent}
                  onChange={(e) => setCustomContent(e.target.value)}
                  rows={6}
                />
              </div>
              
              <Button 
                onClick={() => testEmailFunction('custom', { 
                  subject: customSubject || 'Test Email',
                  content: customContent || '<h1>Test Email</h1><p>This is a test email.</p>'
                })}
                disabled={isLoading || !testEmail || !customSubject || !customContent}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Send Custom Email
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Results */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Test Results</CardTitle>
              <Button variant="outline" size="sm" onClick={clearResults}>
                Clear Results
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {results.map((result, index) => (
                <Alert key={index} variant={result.success ? "default" : "destructive"}>
                  <div className="flex items-start gap-3">
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {result.type}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {new Date(result.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <AlertDescription>
                        {result.success ? result.message : result.error}
                        {result.messageId && (
                          <div className="text-xs text-gray-500 mt-1">
                            Message ID: {result.messageId}
                          </div>
                        )}
                        {result.details && (
                          <div className="text-xs text-gray-500 mt-1">
                            Details: {result.details}
                          </div>
                        )}
                      </AlertDescription>
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}