"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { 
  CheckCircle, 
  ArrowRight, 
  Users, 
  Calendar, 
  MessageSquare,
  Heart,
  Sparkles,
  Trophy
} from "lucide-react"

interface OnboardingCompleteProps {
  alumni?: {
    name?: string
    graduationYear?: number
    programmes?: string[]
    skills?: string[]
    interests?: string[]
  }
  onComplete: () => void
  isCompleting: boolean
}

export function OnboardingComplete({ alumni, onComplete, isCompleting }: OnboardingCompleteProps) {
  const nextSteps = [
    {
      icon: Users,
      title: "Explore Alumni Directory",
      description: "Discover and connect with fellow PROTEC graduates",
      action: "Browse Alumni",
      color: "text-blue-600",
    },
    {
      icon: Calendar,
      title: "Join Upcoming Events",
      description: "Attend networking events, workshops, and reunions",
      action: "View Events",
      color: "text-green-600",
    },
    {
      icon: MessageSquare,
      title: "Start Conversations",
      description: "Send messages and join community discussions",
      action: "Start Messaging",
      color: "text-purple-600",
    },
    {
      icon: Heart,
      title: "Give Back",
      description: "Support the next generation of PROTEC students",
      action: "Make a Donation",
      color: "text-red-600",
    },
  ]

  const achievements = [
    "Profile Setup Complete",
    "PROTEC Background Added",
    "Professional Info Shared",
    "Preferences Configured",
  ]

  return (
    <div className="space-y-8 text-center">
      {/* Success Animation */}
      <div className="space-y-6">
        <div className="relative">
          <div className="w-24 h-24 mx-auto bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center animate-pulse">
            <CheckCircle className="h-12 w-12 text-white" />
          </div>
          <div className="absolute -top-2 -right-2">
            <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
              <Sparkles className="h-4 w-4 text-yellow-800" />
            </div>
          </div>
        </div>
        
        <div className="space-y-3">
          <h1 className="text-3xl font-bold text-foreground">
            Welcome to the PROTEC Family{alumni?.name && `, ${alumni.name.split(' ')[0]}`}! 🎉
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Your profile is now complete and you're ready to connect with thousands of PROTEC alumni worldwide.
          </p>
        </div>
      </div>

      {/* Achievements */}
      <div className="space-y-4">
        <div className="flex items-center justify-center space-x-2">
          <Trophy className="h-5 w-5 text-yellow-600" />
          <h3 className="font-semibold text-foreground">Setup Complete!</h3>
        </div>
        
        <div className="flex flex-wrap justify-center gap-2">
          {achievements.map((achievement, index) => (
            <Badge
              key={index}
              className="bg-green-100 text-green-800 hover:bg-green-200"
            >
              <CheckCircle className="mr-1 h-3 w-3" />
              {achievement}
            </Badge>
          ))}
        </div>
      </div>

      {/* Profile Summary */}
      <Card className="max-w-md mx-auto">
        <CardContent className="p-6">
          <h3 className="font-semibold mb-4">Your Profile Summary</h3>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Name</span>
              <span className="font-medium">{alumni?.name || "Not set"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Graduation Year</span>
              <span className="font-medium">{alumni?.graduationYear || "Not set"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Programmes</span>
              <span className="font-medium">{alumni?.programmes?.length || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Skills</span>
              <span className="font-medium">{alumni?.skills?.length || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Interests</span>
              <span className="font-medium">{alumni?.interests?.length || 0}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-foreground">What's Next?</h3>
        <div className="grid md:grid-cols-2 gap-4 max-w-4xl mx-auto">
          {nextSteps.map((step, index) => {
            const Icon = step.icon
            return (
              <Card key={index} className="text-left hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg bg-muted ${step.color}`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-foreground mb-1">{step.title}</h4>
                      <p className="text-sm text-muted-foreground mb-3">{step.description}</p>
                      <Button variant="outline" size="sm" className="text-xs">
                        {step.action}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Community Stats */}
      <div className="bg-primary/5 border border-primary/20 rounded-lg p-6 max-w-2xl mx-auto">
        <h3 className="font-semibold text-primary mb-4">You're now part of:</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">5,000+</div>
            <div className="text-sm text-muted-foreground">Alumni</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">50+</div>
            <div className="text-sm text-muted-foreground">Countries</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">100+</div>
            <div className="text-sm text-muted-foreground">Industries</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">40+</div>
            <div className="text-sm text-muted-foreground">Years</div>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <div className="space-y-4">
        <Button
          onClick={onComplete}
          disabled={isCompleting}
          size="lg"
          className="bg-primary hover:bg-primary/90 px-8"
        >
          {isCompleting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Setting up your dashboard...
            </>
          ) : (
            <>
              Enter the PROTEC Alumni Network
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
        
        <p className="text-sm text-muted-foreground">
          You can always update your profile and preferences later
        </p>
      </div>
    </div>
  )
}
