"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowRight, 
  Users, 
  Calendar, 
  Heart, 
  MessageSquare,
  Briefcase,
  GraduationCap,
  Globe,
  X
} from "lucide-react"

interface OnboardingWelcomeProps {
  alumni?: {
    name?: string
  }
  onNext: () => void
  onSkip: () => void
  isSkipping: boolean
}

export function OnboardingWelcome({ alumni, onNext, onSkip, isSkipping }: OnboardingWelcomeProps) {
  const features = [
    {
      icon: Users,
      title: "Connect with <PERSON>",
      description: "Join a network of 5,000+ PROTEC graduates worldwide",
      color: "text-blue-600",
    },
    {
      icon: Briefcase,
      title: "Career Opportunities",
      description: "Discover job opportunities and professional development",
      color: "text-green-600",
    },
    {
      icon: Calendar,
      title: "Exclusive Events",
      description: "Access networking events, workshops, and reunions",
      color: "text-purple-600",
    },
    {
      icon: Heart,
      title: "Give Back",
      description: "Support the next generation of PROTEC students",
      color: "text-red-600",
    },
    {
      icon: MessageSquare,
      title: "Stay Connected",
      description: "Private messaging and community discussions",
      color: "text-indigo-600",
    },
    {
      icon: Globe,
      title: "Global Network",
      description: "Connect across continents and industries",
      color: "text-teal-600",
    },
  ]

  return (
    <div className="space-y-8 text-center">
      {/* Welcome Header */}
      <div className="space-y-4">
        <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary to-primary/70 rounded-full flex items-center justify-center">
          <GraduationCap className="h-10 w-10 text-primary-foreground" />
        </div>
        
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-foreground">
            Welcome to PROTEC Alumni{alumni?.name && `, ${alumni.name.split(' ')[0]}`}!
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            You're now part of an incredible community of PROTEC graduates making a difference around the world.
          </p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <div className="text-2xl font-bold text-primary">5,000+</div>
          <div className="text-sm text-muted-foreground">Alumni</div>
        </div>
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <div className="text-2xl font-bold text-primary">50+</div>
          <div className="text-sm text-muted-foreground">Countries</div>
        </div>
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <div className="text-2xl font-bold text-primary">100+</div>
          <div className="text-sm text-muted-foreground">Industries</div>
        </div>
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <div className="text-2xl font-bold text-primary">40+</div>
          <div className="text-sm text-muted-foreground">Years</div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
        {features.map((feature, index) => {
          const Icon = feature.icon
          return (
            <div key={index} className="text-left p-6 bg-background border rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-start space-x-4">
                <div className={`p-2 rounded-lg bg-muted ${feature.color}`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-foreground mb-1">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Setup Benefits */}
      <div className="bg-primary/5 border border-primary/20 rounded-lg p-6 max-w-2xl mx-auto">
        <h3 className="font-semibold text-primary mb-3">Complete your profile to unlock:</h3>
        <div className="grid md:grid-cols-2 gap-3 text-sm">
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="w-2 h-2 p-0 rounded-full bg-green-500"></Badge>
            <span>Personalized alumni recommendations</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="w-2 h-2 p-0 rounded-full bg-green-500"></Badge>
            <span>Targeted job opportunities</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="w-2 h-2 p-0 rounded-full bg-green-500"></Badge>
            <span>Industry-specific events</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="w-2 h-2 p-0 rounded-full bg-green-500"></Badge>
            <span>Enhanced networking features</span>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <Button
          onClick={onNext}
          size="lg"
          className="bg-primary hover:bg-primary/90 px-8"
        >
          Let's get started
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          onClick={onSkip}
          disabled={isSkipping}
          className="text-muted-foreground hover:text-foreground"
        >
          {isSkipping ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
              Skipping...
            </>
          ) : (
            <>
              <X className="mr-2 h-4 w-4" />
              Skip for now
            </>
          )}
        </Button>
      </div>

      {/* Time Estimate */}
      <div className="text-center text-sm text-muted-foreground">
        <p>Setup takes about 5 minutes • You can always update your profile later</p>
      </div>
    </div>
  )
}
