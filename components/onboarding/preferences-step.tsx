"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { api } from "@/components/providers/trpc-provider"
import { toast } from "sonner"
import { 
  ArrowLeft, 
  ArrowRight, 
  Settings, 
  Shield, 
  Bell, 
  Eye, 
  EyeOff,
  Linkedin,
  Twitter,
  Github,
  Globe
} from "lucide-react"

const preferencesSchema = z.object({
  socialLinks: z.object({
    linkedin: z.string().url().optional().or(z.literal("")),
    twitter: z.string().url().optional().or(z.literal("")),
    github: z.string().url().optional().or(z.literal("")),
    website: z.string().url().optional().or(z.literal("")),
  }).optional(),
  privacy: z.object({
    showEmail: z.boolean().default(false),
    showPhone: z.boolean().default(false),
    showLocation: z.boolean().default(true),
    showConnections: z.boolean().default(true),
    showCareerHistory: z.boolean().default(true),
    showEducation: z.boolean().default(true),
    showProtecInvolvement: z.boolean().default(true),
  }),
  notifications: z.object({
    emailUpdates: z.boolean().default(true),
    eventReminders: z.boolean().default(true),
    connectionRequests: z.boolean().default(true),
    weeklyDigest: z.boolean().default(true),
  }).optional(),
})

type PreferencesFormData = z.infer<typeof preferencesSchema>

interface PreferencesStepProps {
  alumni?: {
    socialLinks?: {
      linkedin?: string
      twitter?: string
      github?: string
      website?: string
    }
    privacy?: any
  }
  onNext: () => void
  onPrevious: () => void
}

export function PreferencesStep({ alumni, onNext, onPrevious }: PreferencesStepProps) {
  const privacy = alumni?.privacy || {}
  
  const form = useForm<PreferencesFormData>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: {
      socialLinks: {
        linkedin: alumni?.socialLinks?.linkedin || "",
        twitter: alumni?.socialLinks?.twitter || "",
        github: alumni?.socialLinks?.github || "",
        website: alumni?.socialLinks?.website || "",
      },
      privacy: {
        showEmail: privacy.showEmail ?? false,
        showPhone: privacy.showPhone ?? false,
        showLocation: privacy.showLocation ?? true,
        showConnections: privacy.showConnections ?? true,
        showCareerHistory: privacy.showCareerHistory ?? true,
        showEducation: privacy.showEducation ?? true,
        showProtecInvolvement: privacy.showProtecInvolvement ?? true,
      },
      notifications: {
        emailUpdates: true,
        eventReminders: true,
        connectionRequests: true,
        weeklyDigest: true,
      },
    },
  })

  const updatePreferencesMutation = api.onboarding.updatePreferences.useMutation({
    onSuccess: () => {
      toast.success("Preferences saved!")
      onNext()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to save preferences")
    },
  })

  const onSubmit = async (data: PreferencesFormData) => {
    await updatePreferencesMutation.mutateAsync(data)
  }

  const privacyOptions = [
    {
      key: 'showEmail',
      label: 'Email Address',
      description: 'Allow other alumni to see your email address',
    },
    {
      key: 'showPhone',
      label: 'Phone Number',
      description: 'Allow other alumni to see your phone number',
    },
    {
      key: 'showLocation',
      label: 'Location',
      description: 'Show your city and province to other alumni',
    },
    {
      key: 'showConnections',
      label: 'Connections',
      description: 'Allow others to see your alumni connections',
    },
    {
      key: 'showCareerHistory',
      label: 'Career History',
      description: 'Display your work experience to other alumni',
    },
    {
      key: 'showEducation',
      label: 'Education',
      description: 'Show your educational background',
    },
    {
      key: 'showProtecInvolvement',
      label: 'PROTEC Involvement',
      description: 'Display your PROTEC programme participation',
    },
  ]

  const notificationOptions = [
    {
      key: 'emailUpdates',
      label: 'Email Updates',
      description: 'Receive important updates and announcements',
    },
    {
      key: 'eventReminders',
      label: 'Event Reminders',
      description: 'Get notified about upcoming events you\'re interested in',
    },
    {
      key: 'connectionRequests',
      label: 'Connection Requests',
      description: 'Receive notifications when someone wants to connect',
    },
    {
      key: 'weeklyDigest',
      label: 'Weekly Digest',
      description: 'Get a summary of network activity and opportunities',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
          <Settings className="h-6 w-6 text-primary" />
        </div>
        <h2 className="text-2xl font-bold text-foreground">Set Your Preferences</h2>
        <p className="text-muted-foreground">
          Customize your privacy settings and notification preferences
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Social Links */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Globe className="mr-2 h-5 w-5" />
                Social Links
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="socialLinks.linkedin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        <Linkedin className="mr-2 h-4 w-4" />
                        LinkedIn
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://linkedin.com/in/yourprofile" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="socialLinks.twitter"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        <Twitter className="mr-2 h-4 w-4" />
                        Twitter
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://twitter.com/yourusername" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="socialLinks.github"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        <Github className="mr-2 h-4 w-4" />
                        GitHub
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://github.com/yourusername" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="socialLinks.website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        <Globe className="mr-2 h-4 w-4" />
                        Website
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://yourwebsite.com" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Privacy Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Shield className="mr-2 h-5 w-5" />
                Privacy Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Control what information is visible to other alumni in the network.
              </p>
              
              {privacyOptions.map((option) => (
                <FormField
                  key={option.key}
                  control={form.control}
                  name={`privacy.${option.key}` as any}
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between space-x-4">
                      <div className="flex-1">
                        <FormLabel className="font-medium cursor-pointer">
                          {option.label}
                        </FormLabel>
                        <FormDescription className="mt-1">
                          {option.description}
                        </FormDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        {field.value ? (
                          <Eye className="h-4 w-4 text-green-600" />
                        ) : (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        )}
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </div>
                    </FormItem>
                  )}
                />
              ))}
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Bell className="mr-2 h-5 w-5" />
                Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Choose how you'd like to stay updated with the PROTEC alumni community.
              </p>
              
              {notificationOptions.map((option) => (
                <FormField
                  key={option.key}
                  control={form.control}
                  name={`notifications.${option.key}` as any}
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between space-x-4">
                      <div className="flex-1">
                        <FormLabel className="font-medium cursor-pointer">
                          {option.label}
                        </FormLabel>
                        <FormDescription className="mt-1">
                          {option.description}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              ))}
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onPrevious}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <Button
              type="submit"
              disabled={updatePreferencesMutation.isPending}
              className="bg-primary hover:bg-primary/90"
            >
              {updatePreferencesMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
