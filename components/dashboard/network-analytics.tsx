"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Users, 
  TrendingUp, 
  MessageCircle, 
  UserPlus,
  Network,
  Target,
  Award,
  Calendar
} from "lucide-react"

interface NetworkAnalyticsProps {
  className?: string
}

export function NetworkAnalytics({ className }: NetworkAnalyticsProps) {
  // Mock data - replace with real API calls
  const networkStats = {
    totalConnections: 156,
    connectionGrowth: 12.5,
    mutualConnections: 23,
    messagesSent: 89,
    messagesReceived: 134,
    networkReach: 2847, // 2nd degree connections
    influenceScore: 78
  }

  const recentConnections = [
    {
      id: '1',
      name: '<PERSON>',
      photoUrl: null,
      role: 'Software Engineer',
      company: 'TechCorp',
      mutualConnections: 5,
      connectedDate: '2024-03-15'
    },
    {
      id: '2',
      name: '<PERSON>',
      photoUrl: null,
      role: 'Product Manager',
      company: 'InnovateCo',
      mutualConnections: 3,
      connectedDate: '2024-03-14'
    },
    {
      id: '3',
      name: '<PERSON>',
      photoUrl: null,
      role: 'Data Scientist',
      company: 'DataFlow',
      mutualConnections: 7,
      connectedDate: '2024-03-13'
    }
  ]

  const topConnectors = [
    {
      id: '1',
      name: 'David Wilson',
      photoUrl: null,
      connections: 234,
      industry: 'Technology',
      mutualWith: 12
    },
    {
      id: '2',
      name: 'Lisa Anderson',
      photoUrl: null,
      connections: 189,
      industry: 'Finance',
      mutualWith: 8
    },
    {
      id: '3',
      name: 'Robert Taylor',
      photoUrl: null,
      connections: 167,
      industry: 'Consulting',
      mutualWith: 15
    }
  ]

  const networkInsights = [
    {
      title: 'Strong Tech Network',
      description: '45% of your connections work in technology',
      percentage: 45,
      color: 'bg-blue-500'
    },
    {
      title: 'Cape Town Cluster',
      description: '38% of connections are based in Cape Town',
      percentage: 38,
      color: 'bg-green-500'
    },
    {
      title: 'Recent Graduates',
      description: '22% graduated in the last 5 years',
      percentage: 22,
      color: 'bg-purple-500'
    }
  ]

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Network Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Connections</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{networkStats.totalConnections}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-green-500">+{networkStats.connectionGrowth}%</span>
              <span>this month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Network Reach</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{networkStats.networkReach.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">
              2nd degree connections
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Messages</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{networkStats.messagesSent + networkStats.messagesReceived}</div>
            <div className="text-xs text-muted-foreground">
              {networkStats.messagesSent} sent • {networkStats.messagesReceived} received
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Influence Score</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{networkStats.influenceScore}</div>
            <div className="text-xs text-muted-foreground">
              Based on network activity
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Connections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <UserPlus className="h-5 w-5" />
            <span>Recent Connections</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentConnections.map((connection) => (
              <div key={connection.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={connection.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(connection.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{connection.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {connection.role} at {connection.company}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {connection.mutualConnections} mutual
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(connection.connectedDate).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Connectors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Network className="h-5 w-5" />
            <span>Top Connectors in Your Network</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topConnectors.map((connector, index) => (
              <div key={connector.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-protec-navy text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={connector.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(connector.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{connector.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {connector.industry} • {connector.mutualWith} mutual connections
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{connector.connections} connections</div>
                  <Badge variant="secondary" className="text-xs">
                    Influencer
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Network Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Network Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {networkInsights.map((insight, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{insight.title}</div>
                    <div className="text-sm text-muted-foreground">{insight.description}</div>
                  </div>
                  <div className="text-sm font-medium">{insight.percentage}%</div>
                </div>
                <Progress value={insight.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Network Growth Chart Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Network Growth</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Network Growth Chart</h3>
              <p className="text-muted-foreground">
                Chart showing connection growth over time would be displayed here
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Networking Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Networking Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <UserPlus className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Expand Your Network</span>
              </div>
              <p className="text-sm text-blue-700">
                Connect with 5 more alumni in the technology sector to strengthen your professional network.
              </p>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <MessageCircle className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-800">Stay Active</span>
              </div>
              <p className="text-sm text-green-700">
                Send messages to recent connections to maintain relationships and build stronger ties.
              </p>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-purple-800">Attend Events</span>
              </div>
              <p className="text-sm text-purple-700">
                Join upcoming networking events to meet alumni from different industries and graduation years.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
