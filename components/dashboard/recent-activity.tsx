"use client"

import <PERSON> from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Users, 
  Calendar, 
  MessageSquare, 
  Heart, 
  ArrowRight,
  Clock
} from "lucide-react"

// Mock data - in real app, this would come from tRPC
const activities = [
  {
    id: "1",
    type: "connection",
    user: {
      name: "<PERSON>",
      avatar: "/avatars/sarah.jpg",
      initials: "<PERSON><PERSON>",
      role: "Software Engineer at Google"
    },
    action: "connected with you",
    time: "2 hours ago",
    icon: Users,
    color: "text-blue-600"
  },
  {
    id: "2",
    type: "event",
    user: {
      name: "PROTEC Events",
      avatar: "/avatars/protec.jpg",
      initials: "P<PERSON>",
      role: "Official"
    },
    action: "invited you to 'Tech Career Fair 2024'",
    time: "5 hours ago",
    icon: Calendar,
    color: "text-green-600"
  },
  {
    id: "3",
    type: "post",
    user: {
      name: "<PERSON>",
      avatar: "/avatars/michael.jpg",
      initials: "<PERSON>",
      role: "Data Scientist at Amazon"
    },
    action: "liked your post about machine learning",
    time: "1 day ago",
    icon: Heart,
    color: "text-red-600"
  },
  {
    id: "4",
    type: "comment",
    user: {
      name: "Priya Patel",
      avatar: "/avatars/priya.jpg",
      initials: "PP",
      role: "Product Manager at Microsoft"
    },
    action: "commented on your career update",
    time: "2 days ago",
    icon: MessageSquare,
    color: "text-purple-600"
  },
  {
    id: "5",
    type: "connection",
    user: {
      name: "David Williams",
      avatar: "/avatars/david.jpg",
      initials: "DW",
      role: "Engineering Manager at Meta"
    },
    action: "connected with you",
    time: "3 days ago",
    icon: Users,
    color: "text-blue-600"
  }
]

export function RecentActivity() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Recent Activity
        </CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/activity">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity) => {
          const Icon = activity.icon
          return (
            <div key={activity.id} className="flex items-start space-x-3 group">
              <div className="relative">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                  <AvatarFallback className="bg-protec-navy text-white text-sm">
                    {activity.user.initials}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-white flex items-center justify-center border-2 border-white`}>
                  <Icon className={`h-3 w-3 ${activity.color}`} />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-protec-navy truncate">
                    {activity.user.name}
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    {activity.user.role.split(' ')[0]}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {activity.action}
                </p>
                <div className="flex items-center space-x-1 mt-1">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {activity.time}
                  </span>
                </div>
              </div>
            </div>
          )
        })}

        {activities.length === 0 && (
          <div className="text-center py-8">
            <div className="text-muted-foreground mb-2">No recent activity</div>
            <p className="text-sm text-muted-foreground">
              Start connecting with alumni to see activity here
            </p>
            <Button variant="outline" size="sm" className="mt-3" asChild>
              <Link href="/directory">Browse Alumni</Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
