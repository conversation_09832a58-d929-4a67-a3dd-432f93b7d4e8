"use client"

import <PERSON> from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MessageSquare, Heart, ArrowRight, TrendingUp } from "lucide-react"

// Mock data - in real app, this would come from tRPC
const recentPosts = [
  {
    id: "1",
    author: {
      name: "<PERSON>",
      photoUrl: "/avatars/sarah.jpg",
      role: "Software Engineer at Google"
    },
    content: "Just completed my first year at Google! Grateful for the foundation PROTEC provided. The problem-solving skills and technical knowledge have been invaluable. Looking forward to mentoring current students! 🚀",
    createdAt: "2024-01-20T10:30:00Z",
    likes: 24,
    comments: 8,
    tags: ["career", "mentorship", "google"]
  },
  {
    id: "2",
    author: {
      name: "<PERSON>",
      photoUrl: "/avatars/michael.jpg",
      role: "Data Scientist at Amazon"
    },
    content: "Excited to announce that our team's ML model is now helping optimize delivery routes across South Africa! It's amazing how the mathematical foundations from PROTEC continue to shape my work.",
    createdAt: "2024-01-19T15:45:00Z",
    likes: 31,
    comments: 12,
    tags: ["machinelearning", "amazon", "innovation"]
  },
  {
    id: "3",
    author: {
      name: "Priya Patel",
      photoUrl: "/avatars/priya.jpg",
      role: "Product Manager at Microsoft"
    },
    content: "Leading a diverse team of 15 engineers on Microsoft Teams features. The leadership and communication skills I developed during PROTEC group projects are paying off every day!",
    createdAt: "2024-01-18T09:15:00Z",
    likes: 18,
    comments: 5,
    tags: ["leadership", "microsoft", "teamwork"]
  }
]

export function RecentPosts() {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    return `${Math.floor(diffInHours / 24)}d ago`
  }

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength) + "..."
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
          <TrendingUp className="mr-2 h-5 w-5" />
          Community Highlights
        </CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/feed">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {recentPosts.map((post) => (
          <div key={post.id} className="space-y-3 pb-6 border-b last:border-b-0 last:pb-0">
            {/* Author Info */}
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={post.author.photoUrl} alt={post.author.name} />
                <AvatarFallback className="bg-protec-navy text-white">
                  {getInitials(post.author.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <Link 
                  href={`/profile/${post.id}`}
                  className="font-medium text-protec-navy hover:text-protec-red transition-colors"
                >
                  {post.author.name}
                </Link>
                <p className="text-sm text-muted-foreground">{post.author.role}</p>
              </div>
              <span className="text-xs text-muted-foreground">
                {formatTimeAgo(post.createdAt)}
              </span>
            </div>

            {/* Post Content */}
            <div className="space-y-3">
              <p className="text-sm leading-relaxed">
                {truncateContent(post.content)}
              </p>

              {/* Tags */}
              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {post.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Engagement Stats */}
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Heart className="h-4 w-4" />
                  <span>{post.likes}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MessageSquare className="h-4 w-4" />
                  <span>{post.comments}</span>
                </div>
                <Button variant="ghost" size="sm" className="h-auto p-0 text-xs">
                  Read more
                </Button>
              </div>
            </div>
          </div>
        ))}

        {/* Engagement Summary */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-protec-navy">156</div>
              <p className="text-xs text-muted-foreground">Posts This Week</p>
            </div>
            <div>
              <div className="text-lg font-bold text-protec-navy">2.3K</div>
              <p className="text-xs text-muted-foreground">Total Likes</p>
            </div>
            <div>
              <div className="text-lg font-bold text-protec-navy">89%</div>
              <p className="text-xs text-muted-foreground">Engagement Rate</p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="pt-4 border-t">
          <Button variant="outline" className="w-full" asChild>
            <Link href="/feed">
              <MessageSquare className="mr-2 h-4 w-4" />
              Join the Conversation
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
