"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Users, Target, Calendar } from "lucide-react"

// Mock data - in real app, this would come from tRPC
const networkData = {
  totalConnections: 127,
  monthlyGrowth: 12,
  growthPercentage: 10.4,
  weeklyConnections: 3,
  goalProgress: 75, // Progress towards yearly connection goal
  yearlyGoal: 200,
  recentConnections: [
    { name: "<PERSON>", role: "Software Engineer", timeAgo: "2 hours ago" },
    { name: "<PERSON>", role: "Data Scientist", timeAgo: "1 day ago" },
    { name: "<PERSON><PERSON>", role: "Product Manager", timeAgo: "3 days ago" },
  ]
}

export function NetworkGrowth() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Network Growth
        </CardTitle>
        <TrendingUp className="h-5 w-5 text-green-600" />
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Stats */}
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold text-protec-navy">
              {networkData.totalConnections}
            </div>
            <p className="text-sm text-muted-foreground">Total Connections</p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-1">
              <span className="text-sm font-medium text-green-600">
                +{networkData.monthlyGrowth}
              </span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                +{networkData.growthPercentage}%
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">This month</p>
          </div>
        </div>

        {/* Goal Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center space-x-1">
              <Target className="h-4 w-4 text-protec-navy" />
              <span>2024 Goal Progress</span>
            </span>
            <span className="text-muted-foreground">
              {networkData.totalConnections}/{networkData.yearlyGoal}
            </span>
          </div>
          <Progress value={networkData.goalProgress} className="h-2" />
          <p className="text-xs text-muted-foreground">
            {networkData.yearlyGoal - networkData.totalConnections} connections to reach your goal
          </p>
        </div>

        {/* Weekly Activity */}
        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">This Week</span>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-blue-900">
              {networkData.weeklyConnections}
            </div>
            <p className="text-xs text-blue-700">New connections</p>
          </div>
        </div>

        {/* Recent Connections */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-protec-navy">Recent Connections</h4>
          <div className="space-y-2">
            {networkData.recentConnections.map((connection, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div>
                  <p className="font-medium">{connection.name}</p>
                  <p className="text-xs text-muted-foreground">{connection.role}</p>
                </div>
                <span className="text-xs text-muted-foreground">
                  {connection.timeAgo}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Growth Insights */}
        <div className="pt-3 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-protec-navy">85%</div>
              <p className="text-xs text-muted-foreground">Response Rate</p>
            </div>
            <div>
              <div className="text-lg font-bold text-protec-navy">4.2</div>
              <p className="text-xs text-muted-foreground">Avg. Connections/Week</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
