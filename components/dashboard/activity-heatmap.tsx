"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Activity, 
  Calendar, 
  TrendingUp,
  Clock,
  Target
} from "lucide-react"

interface ActivityHeatmapProps {
  className?: string
}

export function ActivityHeatmap({ className }: ActivityHeatmapProps) {
  // Mock data - replace with real API calls
  // Generate activity data for the last 365 days
  const generateActivityData = () => {
    const data = []
    const today = new Date()
    
    for (let i = 364; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      
      // Generate random activity level (0-4)
      const activityLevel = Math.floor(Math.random() * 5)
      
      data.push({
        date: date.toISOString().split('T')[0],
        level: activityLevel,
        count: activityLevel * Math.floor(Math.random() * 5) + activityLevel
      })
    }
    
    return data
  }

  const activityData = generateActivityData()
  
  const getActivityColor = (level: number) => {
    switch (level) {
      case 0:
        return 'bg-gray-100'
      case 1:
        return 'bg-green-100'
      case 2:
        return 'bg-green-200'
      case 3:
        return 'bg-green-400'
      case 4:
        return 'bg-green-600'
      default:
        return 'bg-gray-100'
    }
  }

  const getActivityLabel = (level: number) => {
    switch (level) {
      case 0:
        return 'No activity'
      case 1:
        return 'Low activity'
      case 2:
        return 'Moderate activity'
      case 3:
        return 'High activity'
      case 4:
        return 'Very high activity'
      default:
        return 'No activity'
    }
  }

  // Group data by weeks
  const weeks = []
  for (let i = 0; i < activityData.length; i += 7) {
    weeks.push(activityData.slice(i, i + 7))
  }

  // Calculate statistics
  const totalActiveDays = activityData.filter(d => d.level > 0).length
  const averageActivity = activityData.reduce((sum, d) => sum + d.count, 0) / activityData.length
  const maxStreak = calculateMaxStreak(activityData)
  const currentStreak = calculateCurrentStreak(activityData)

  function calculateMaxStreak(data: any[]) {
    let maxStreak = 0
    let currentStreak = 0
    
    for (const day of data) {
      if (day.level > 0) {
        currentStreak++
        maxStreak = Math.max(maxStreak, currentStreak)
      } else {
        currentStreak = 0
      }
    }
    
    return maxStreak
  }

  function calculateCurrentStreak(data: any[]) {
    let streak = 0
    
    for (let i = data.length - 1; i >= 0; i--) {
      if (data[i].level > 0) {
        streak++
      } else {
        break
      }
    }
    
    return streak
  }

  const months = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ]

  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Activity Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-2xl font-bold">{totalActiveDays}</div>
                <div className="text-sm text-muted-foreground">Active Days</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-2xl font-bold">{averageActivity.toFixed(1)}</div>
                <div className="text-sm text-muted-foreground">Avg Daily Activity</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-2xl font-bold">{maxStreak}</div>
                <div className="text-sm text-muted-foreground">Longest Streak</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-2xl font-bold">{currentStreak}</div>
                <div className="text-sm text-muted-foreground">Current Streak</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Heatmap */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Activity Heatmap</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Month labels */}
            <div className="flex justify-between text-xs text-muted-foreground px-4">
              {months.map((month, index) => (
                <span key={month} className={index % 2 === 0 ? '' : 'opacity-0'}>
                  {month}
                </span>
              ))}
            </div>

            {/* Heatmap grid */}
            <div className="flex">
              {/* Day labels */}
              <div className="flex flex-col justify-between text-xs text-muted-foreground pr-2">
                {days.map((day, index) => (
                  <span key={day} className={index % 2 === 0 ? '' : 'opacity-0'}>
                    {day}
                  </span>
                ))}
              </div>

              {/* Activity grid */}
              <div className="flex flex-col space-y-1">
                {[0, 1, 2, 3, 4, 5, 6].map((dayOfWeek) => (
                  <div key={dayOfWeek} className="flex space-x-1">
                    {weeks.map((week, weekIndex) => {
                      const dayData = week[dayOfWeek]
                      if (!dayData) return <div key={weekIndex} className="w-3 h-3" />
                      
                      return (
                        <div
                          key={weekIndex}
                          className={`w-3 h-3 rounded-sm cursor-pointer transition-all hover:ring-2 hover:ring-protec-red hover:ring-opacity-50 ${getActivityColor(dayData.level)}`}
                          title={`${dayData.date}: ${dayData.count} activities (${getActivityLabel(dayData.level)})`}
                        />
                      )
                    })}
                  </div>
                ))}
              </div>
            </div>

            {/* Legend */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <span>Less</span>
                <div className="flex space-x-1">
                  {[0, 1, 2, 3, 4].map((level) => (
                    <div
                      key={level}
                      className={`w-3 h-3 rounded-sm ${getActivityColor(level)}`}
                    />
                  ))}
                </div>
                <span>More</span>
              </div>
              
              <div className="text-sm text-muted-foreground">
                Past 365 days
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-800">Great Consistency!</span>
              </div>
              <p className="text-sm text-green-700">
                You've been active for {Math.round((totalActiveDays / 365) * 100)}% of the past year. 
                Keep up the great work!
              </p>
            </div>
            
            {currentStreak > 7 && (
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-800">Impressive Streak!</span>
                </div>
                <p className="text-sm text-blue-700">
                  You're on a {currentStreak}-day activity streak. Try to reach {currentStreak + 7} days!
                </p>
              </div>
            )}
            
            {averageActivity < 2 && (
              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Activity className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium text-yellow-800">Room for Improvement</span>
                </div>
                <p className="text-sm text-yellow-700">
                  Consider increasing your daily activity. Try setting a goal of 3-5 activities per day.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Activity Types Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Types</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-lg font-semibold text-blue-600">45%</div>
              <div className="text-sm text-blue-700">Messages</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-lg font-semibold text-green-600">25%</div>
              <div className="text-sm text-green-700">Profile Updates</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-lg font-semibold text-purple-600">20%</div>
              <div className="text-sm text-purple-700">Event Interactions</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-lg font-semibold text-orange-600">10%</div>
              <div className="text-sm text-orange-700">Other</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
