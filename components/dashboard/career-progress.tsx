"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { 
  Briefcase, 
  TrendingUp, 
  Target, 
  Award,
  Calendar,
  MapPin,
  Building,
  Users,
  Star,
  Plus
} from "lucide-react"

interface CareerProgressProps {
  className?: string
}

export function CareerProgress({ className }: CareerProgressProps) {
  // Mock data - replace with real API calls
  const currentRole = {
    title: 'Senior Software Engineer',
    company: 'TechCorp',
    startDate: '2022-01-15',
    location: 'Cape Town, South Africa',
    salary: 'R850,000',
    skills: ['React', 'Node.js', 'TypeScript', 'AWS']
  }

  const careerGoals = [
    {
      title: 'Become Tech Lead',
      target: '2024-12-31',
      progress: 65,
      skills: ['Leadership', 'Architecture', 'Mentoring'],
      status: 'on-track'
    },
    {
      title: 'Complete AWS Certification',
      target: '2024-06-30',
      progress: 40,
      skills: ['AWS', 'Cloud Architecture'],
      status: 'behind'
    },
    {
      title: 'Increase Salary by 20%',
      target: '2024-12-31',
      progress: 80,
      skills: ['Negotiation', 'Performance'],
      status: 'ahead'
    }
  ]

  const careerHistory = [
    {
      title: 'Senior Software Engineer',
      company: 'TechCorp',
      period: '2022 - Present',
      location: 'Cape Town',
      type: 'current'
    },
    {
      title: 'Software Engineer',
      company: 'StartupCo',
      period: '2020 - 2022',
      location: 'Cape Town',
      type: 'previous'
    },
    {
      title: 'Junior Developer',
      company: 'DevAgency',
      period: '2018 - 2020',
      location: 'Cape Town',
      type: 'previous'
    },
    {
      title: 'Intern',
      company: 'TechStart',
      period: '2017 - 2018',
      location: 'Cape Town',
      type: 'previous'
    }
  ]

  const skillsProgress = [
    { skill: 'Technical Leadership', current: 75, target: 90, category: 'Leadership' },
    { skill: 'System Architecture', current: 68, target: 85, category: 'Technical' },
    { skill: 'Team Management', current: 45, target: 80, category: 'Leadership' },
    { skill: 'Cloud Technologies', current: 82, target: 95, category: 'Technical' },
    { skill: 'Public Speaking', current: 35, target: 70, category: 'Soft Skills' }
  ]

  const industryInsights = {
    averageSalary: 'R750,000',
    salaryPercentile: 85,
    marketDemand: 'High',
    growthRate: 12.5
  }

  const recommendations = [
    {
      type: 'skill',
      title: 'Develop Leadership Skills',
      description: 'Consider taking a leadership course to reach your Tech Lead goal',
      priority: 'high'
    },
    {
      type: 'network',
      title: 'Connect with Tech Leads',
      description: 'Network with current Tech Leads to learn from their experience',
      priority: 'medium'
    },
    {
      type: 'certification',
      title: 'AWS Solutions Architect',
      description: 'This certification aligns with your cloud technology goals',
      priority: 'high'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track':
        return 'bg-blue-100 text-blue-800'
      case 'ahead':
        return 'bg-green-100 text-green-800'
      case 'behind':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Current Role Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Briefcase className="h-5 w-5" />
            <span>Current Role</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-semibold">{currentRole.title}</h3>
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Building className="h-4 w-4" />
                  <span>{currentRole.company}</span>
                </div>
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                  <span>{currentRole.location}</span>
                </div>
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>Since {new Date(currentRole.startDate).toLocaleDateString()}</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold text-green-600">{currentRole.salary}</div>
                <div className="text-sm text-muted-foreground">Annual</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Key Skills</h4>
              <div className="flex flex-wrap gap-2">
                {currentRole.skills.map((skill) => (
                  <Badge key={skill} variant="secondary">
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Career Goals */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Career Goals</span>
            </CardTitle>
            <Button size="sm" variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Goal
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {careerGoals.map((goal, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{goal.title}</span>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getStatusColor(goal.status)}`}
                    >
                      {goal.status.replace('-', ' ')}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Target: {new Date(goal.target).toLocaleDateString()}
                  </div>
                </div>
                <Progress value={goal.progress} className="h-2" />
                <div className="flex items-center justify-between text-sm">
                  <div className="flex flex-wrap gap-1">
                    {goal.skills.map((skill) => (
                      <Badge key={skill} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                  <span className="text-muted-foreground">{goal.progress}% complete</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Skills Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5" />
            <span>Skills Development</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {skillsProgress.map((skill, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{skill.skill}</span>
                    <Badge variant="outline" className="text-xs">
                      {skill.category}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {skill.current}% / {skill.target}%
                  </div>
                </div>
                <Progress value={(skill.current / skill.target) * 100} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Career History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Career History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {careerHistory.map((role, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full ${role.type === 'current' ? 'bg-green-500' : 'bg-gray-300'}`} />
                <div className="flex-1">
                  <div className="font-medium">{role.title}</div>
                  <div className="text-sm text-muted-foreground">
                    {role.company} • {role.location}
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  {role.period}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Industry Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Industry Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-lg font-semibold">{industryInsights.averageSalary}</div>
              <div className="text-sm text-muted-foreground">Industry Average</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600">{industryInsights.salaryPercentile}th</div>
              <div className="text-sm text-muted-foreground">Salary Percentile</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{industryInsights.marketDemand}</div>
              <div className="text-sm text-muted-foreground">Market Demand</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-purple-600">+{industryInsights.growthRate}%</div>
              <div className="text-sm text-muted-foreground">Annual Growth</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Career Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recommendations.map((rec, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium">{rec.title}</h4>
                  <Badge 
                    variant="secondary" 
                    className={`text-xs ${getPriorityColor(rec.priority)}`}
                  >
                    {rec.priority} priority
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-3">{rec.description}</p>
                <Button size="sm" variant="outline">
                  Learn More
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
