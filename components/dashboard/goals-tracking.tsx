"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { 
  Target, 
  Plus, 
  Calendar, 
  TrendingUp,
  CheckCircle,
  Clock,
  AlertCircle,
  Edit,
  Trash2
} from "lucide-react"

interface GoalsTrackingProps {
  className?: string
}

export function GoalsTracking({ className }: GoalsTrackingProps) {
  // Mock data - replace with real API calls
  const goals = [
    {
      id: '1',
      title: 'Complete AWS Certification',
      description: 'Obtain AWS Solutions Architect certification to advance cloud skills',
      category: 'Professional Development',
      progress: 65,
      targetDate: '2024-06-30',
      status: 'in-progress',
      milestones: [
        { title: 'Complete online course', completed: true },
        { title: 'Take practice exams', completed: true },
        { title: 'Schedule certification exam', completed: false },
        { title: 'Pass certification exam', completed: false }
      ]
    },
    {
      id: '2',
      title: 'Expand Professional Network',
      description: 'Connect with 50 new alumni in the technology sector',
      category: 'Networking',
      progress: 80,
      targetDate: '2024-12-31',
      status: 'on-track',
      milestones: [
        { title: 'Attend 3 networking events', completed: true },
        { title: 'Connect with 25 alumni', completed: true },
        { title: 'Connect with 40 alumni', completed: true },
        { title: 'Connect with 50 alumni', completed: false }
      ]
    },
    {
      id: '3',
      title: 'Mentor Junior Developers',
      description: 'Provide mentorship to 3 junior developers through PROTEC program',
      category: 'Giving Back',
      progress: 33,
      targetDate: '2024-09-30',
      status: 'behind',
      milestones: [
        { title: 'Complete mentor training', completed: true },
        { title: 'Match with first mentee', completed: false },
        { title: 'Match with second mentee', completed: false },
        { title: 'Match with third mentee', completed: false }
      ]
    },
    {
      id: '4',
      title: 'Increase Annual Salary',
      description: 'Negotiate a 20% salary increase or find new role',
      category: 'Career Growth',
      progress: 90,
      targetDate: '2024-04-30',
      status: 'ahead',
      milestones: [
        { title: 'Research market rates', completed: true },
        { title: 'Document achievements', completed: true },
        { title: 'Schedule performance review', completed: true },
        { title: 'Negotiate salary increase', completed: false }
      ]
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'on-track':
        return 'bg-blue-100 text-blue-800'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'behind':
        return 'bg-red-100 text-red-800'
      case 'ahead':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'on-track':
        return <TrendingUp className="h-4 w-4 text-blue-600" />
      case 'in-progress':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'behind':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'ahead':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      default:
        return <Target className="h-4 w-4 text-gray-600" />
    }
  }

  const getDaysUntilTarget = (targetDate: string) => {
    const target = new Date(targetDate)
    const today = new Date()
    const diffTime = target.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getCompletedMilestones = (milestones: any[]) => {
    return milestones.filter(m => m.completed).length
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Goals Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Goals Tracking</span>
            </CardTitle>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Goal
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{goals.length}</div>
              <div className="text-sm text-blue-700">Total Goals</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {goals.filter(g => g.status === 'completed' || g.status === 'ahead').length}
              </div>
              <div className="text-sm text-green-700">On Track</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {goals.filter(g => g.status === 'in-progress').length}
              </div>
              <div className="text-sm text-yellow-700">In Progress</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {goals.filter(g => g.status === 'behind').length}
              </div>
              <div className="text-sm text-red-700">Behind</div>
            </div>
          </div>

          <div className="space-y-4">
            {goals.map((goal) => {
              const daysUntilTarget = getDaysUntilTarget(goal.targetDate)
              const completedMilestones = getCompletedMilestones(goal.milestones)
              
              return (
                <Card key={goal.id} className="border-l-4 border-l-protec-red">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Goal Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold">{goal.title}</h3>
                            <Badge 
                              variant="secondary" 
                              className={`text-xs ${getStatusColor(goal.status)}`}
                            >
                              <div className="flex items-center space-x-1">
                                {getStatusIcon(goal.status)}
                                <span>{goal.status.replace('-', ' ')}</span>
                              </div>
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">{goal.description}</p>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4" />
                              <span>Due: {new Date(goal.targetDate).toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-4 w-4" />
                              <span>
                                {daysUntilTarget > 0 
                                  ? `${daysUntilTarget} days left`
                                  : daysUntilTarget === 0 
                                    ? 'Due today'
                                    : `${Math.abs(daysUntilTarget)} days overdue`
                                }
                              </span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {goal.category}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="ghost">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Progress */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Overall Progress</span>
                          <span className="text-sm text-muted-foreground">{goal.progress}%</span>
                        </div>
                        <Progress value={goal.progress} className="h-2" />
                      </div>

                      {/* Milestones */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Milestones</span>
                          <span className="text-sm text-muted-foreground">
                            {completedMilestones} of {goal.milestones.length} completed
                          </span>
                        </div>
                        <div className="space-y-2">
                          {goal.milestones.map((milestone, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                                milestone.completed 
                                  ? 'bg-green-500 border-green-500' 
                                  : 'border-gray-300'
                              }`}>
                                {milestone.completed && (
                                  <CheckCircle className="w-3 h-3 text-white" />
                                )}
                              </div>
                              <span className={`text-sm ${
                                milestone.completed 
                                  ? 'text-green-700 line-through' 
                                  : 'text-gray-700'
                              }`}>
                                {milestone.title}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Goal Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Goals by Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {['Professional Development', 'Networking', 'Giving Back', 'Career Growth'].map((category) => {
              const categoryGoals = goals.filter(g => g.category === category)
              const avgProgress = categoryGoals.length > 0 
                ? categoryGoals.reduce((sum, g) => sum + g.progress, 0) / categoryGoals.length 
                : 0
              
              return (
                <div key={category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{category}</span>
                    <div className="text-sm text-muted-foreground">
                      {categoryGoals.length} goals • {Math.round(avgProgress)}% avg progress
                    </div>
                  </div>
                  <Progress value={avgProgress} className="h-2" />
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
