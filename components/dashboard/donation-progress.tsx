"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Heart, TrendingUp, Target, Gift } from "lucide-react"

// Mock data - in real app, this would come from tRPC
const donationData = {
  personalTotal: 2500,
  personalGoal: 5000,
  thisYearDonations: 1200,
  lastDonation: {
    amount: 500,
    date: "2024-01-15",
    purpose: "Scholarships"
  },
  communityTotal: 2150000,
  communityGoal: 3000000,
  rank: 23, // User's rank among donors
  impact: {
    studentsHelped: 12,
    scholarshipsAwarded: 3
  }
}

export function DonationProgress() {
  const personalProgress = (donationData.personalTotal / donationData.personalGoal) * 100
  const communityProgress = (donationData.communityTotal / donationData.communityGoal) * 100

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Giving Impact
        </CardTitle>
        <Heart className="h-5 w-5 text-protec-red" />
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Personal Giving */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-protec-navy">
                {formatCurrency(donationData.personalTotal)}
              </div>
              <p className="text-sm text-muted-foreground">Your total giving</p>
            </div>
            <Badge variant="secondary" className="bg-protec-red/10 text-protec-red">
              Rank #{donationData.rank}
            </Badge>
          </div>

          {/* Personal Goal Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center space-x-1">
                <Target className="h-4 w-4 text-protec-red" />
                <span>Personal Goal</span>
              </span>
              <span className="text-muted-foreground">
                {formatCurrency(donationData.personalTotal)}/{formatCurrency(donationData.personalGoal)}
              </span>
            </div>
            <Progress value={personalProgress} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {formatCurrency(donationData.personalGoal - donationData.personalTotal)} remaining to reach your goal
            </p>
          </div>
        </div>

        {/* Last Donation */}
        <div className="p-3 bg-green-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-900">Last Donation</p>
              <p className="text-xs text-green-700">
                {formatCurrency(donationData.lastDonation.amount)} • {donationData.lastDonation.purpose}
              </p>
            </div>
            <span className="text-xs text-green-600">
              {formatDate(donationData.lastDonation.date)}
            </span>
          </div>
        </div>

        {/* Impact Stats */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-protec-navy flex items-center">
            <Gift className="mr-2 h-4 w-4" />
            Your Impact
          </h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-2 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-900">
                {donationData.impact.studentsHelped}
              </div>
              <p className="text-xs text-blue-700">Students Helped</p>
            </div>
            <div className="text-center p-2 bg-purple-50 rounded-lg">
              <div className="text-lg font-bold text-purple-900">
                {donationData.impact.scholarshipsAwarded}
              </div>
              <p className="text-xs text-purple-700">Scholarships</p>
            </div>
          </div>
        </div>

        {/* Community Progress */}
        <div className="space-y-2 pt-3 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center space-x-1">
              <TrendingUp className="h-4 w-4 text-protec-navy" />
              <span>Community Goal 2024</span>
            </span>
            <span className="text-muted-foreground">
              {Math.round(communityProgress)}%
            </span>
          </div>
          <Progress value={communityProgress} className="h-2" />
          <p className="text-xs text-muted-foreground">
            {formatCurrency(donationData.communityTotal)} raised of {formatCurrency(donationData.communityGoal)} goal
          </p>
        </div>

        {/* Action Button */}
        <Button 
          className="w-full bg-protec-red hover:bg-protec-red/90" 
          asChild
        >
          <Link href="/donations">
            <Heart className="mr-2 h-4 w-4" />
            Make a Donation
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}
