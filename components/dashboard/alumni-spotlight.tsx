"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import { Star, Award, TrendingUp, ExternalLink } from "lucide-react"

export function AlumniSpotlight() {
  const { data: spotlightData, isLoading } = api.alumni.getSpotlightAlumni.useQuery()

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
          <Skeleton className="h-24 w-full" />
          <div className="flex justify-between">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
          </div>
        </CardContent>
      </Card>
    )
  }

  // Use real data or fallback to default
  const spotlightAlumni = spotlightData || {
  id: "spotlight-1",
  name: "Dr. Nomsa Mbeki",
  photoUrl: "/avatars/nomsa.jpg",
  currentRole: "Chief Technology Officer",
  company: "Standard Bank",
  graduationYear: 2008,
  programme: "Electrical Engineering",
  achievement: "Led digital transformation initiative that improved banking services for 12 million customers",
  bio: "Passionate about using technology to solve real-world problems and creating opportunities for the next generation of African tech leaders.",
  stats: {
    yearsExperience: 16,
    teamSize: 150,
    projectsLed: 25
  },
  recentNews: "Featured in Forbes Africa 30 Under 30 Tech Leaders",
  socialLinks: {
    linkedin: "https://linkedin.com/in/nomsa-mbeki",
    twitter: "https://twitter.com/nomsa_mbeki"
  }
}

const featuredAlumni = [
  {
    id: "1",
    name: "James Okoye",
    role: "Senior Software Engineer at Meta",
    graduationYear: 2015,
    achievement: "Developed AR features used by 2B+ users",
    photoUrl: "/avatars/james.jpg"
  },
  {
    id: "2",
    name: "Aisha Patel",
    role: "Renewable Energy Consultant",
    graduationYear: 2012,
    achievement: "Led solar projects powering 50,000 homes",
    photoUrl: "/avatars/aisha.jpg"
  },
  {
    id: "3",
    name: "Thabo Mthembu",
    role: "Biomedical Engineer at UCT",
    graduationYear: 2017,
    achievement: "Developed low-cost medical devices for rural clinics",
    photoUrl: "/avatars/thabo.jpg"
  }
]

export function AlumniSpotlight() {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
          <Star className="mr-2 h-5 w-5 text-yellow-500" />
          Alumni Spotlight
        </CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/directory">
            View All
            <ExternalLink className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Featured Alumni */}
        <div className="space-y-4">
          <div className="relative p-4 bg-gradient-to-br from-protec-navy to-protec-navy/80 text-white rounded-lg">
            <div className="absolute top-2 right-2">
              <Badge variant="secondary" className="bg-yellow-500 text-yellow-900">
                <Award className="mr-1 h-3 w-3" />
                Featured
              </Badge>
            </div>
            
            <div className="flex items-start space-x-4">
              <Avatar className="h-16 w-16 border-2 border-white/20">
                <AvatarImage src={spotlightAlumni.photoUrl} alt={spotlightAlumni.name} />
                <AvatarFallback className="bg-white text-protec-navy text-lg">
                  {getInitials(spotlightAlumni.name)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-lg">{spotlightAlumni.name}</h3>
                <p className="text-blue-100 text-sm">
                  {spotlightAlumni.currentRole} at {spotlightAlumni.company}
                </p>
                <p className="text-blue-200 text-xs">
                  Class of {spotlightAlumni.graduationYear} • {spotlightAlumni.programme}
                </p>
              </div>
            </div>
            
            <div className="mt-4 space-y-3">
              <p className="text-sm text-blue-100 leading-relaxed">
                {spotlightAlumni.achievement}
              </p>
              
              <div className="grid grid-cols-3 gap-3 text-center">
                <div>
                  <div className="text-lg font-bold">{spotlightAlumni.stats.yearsExperience}</div>
                  <p className="text-xs text-blue-200">Years Exp.</p>
                </div>
                <div>
                  <div className="text-lg font-bold">{spotlightAlumni.stats.teamSize}</div>
                  <p className="text-xs text-blue-200">Team Size</p>
                </div>
                <div>
                  <div className="text-lg font-bold">{spotlightAlumni.stats.projectsLed}</div>
                  <p className="text-xs text-blue-200">Projects Led</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between pt-2">
                <Badge variant="outline" className="border-white/30 text-white text-xs">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  {spotlightAlumni.recentNews}
                </Badge>
                <Button variant="ghost" size="sm" className="text-white hover:bg-white/20" asChild>
                  <Link href={`/profile/${spotlightAlumni.id}`}>
                    View Profile
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Other Featured Alumni */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-protec-navy">More Success Stories</h4>
          <div className="space-y-3">
            {featuredAlumni.map((alumni) => (
              <div key={alumni.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={alumni.photoUrl} alt={alumni.name} />
                  <AvatarFallback className="bg-protec-navy text-white text-sm">
                    {getInitials(alumni.name)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <Link 
                    href={`/profile/${alumni.id}`}
                    className="font-medium text-sm text-protec-navy hover:text-protec-red transition-colors"
                  >
                    {alumni.name}
                  </Link>
                  <p className="text-xs text-muted-foreground">{alumni.role}</p>
                  <p className="text-xs text-blue-600 mt-1">{alumni.achievement}</p>
                  <Badge variant="outline" className="text-xs mt-1">
                    Class of {alumni.graduationYear}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="pt-4 border-t">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              Want to be featured in Alumni Spotlight?
            </p>
            <Button variant="outline" size="sm" className="w-full">
              Share Your Story
            </Button>
          </div>
        </div>

        {/* Alumni Stats */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-protec-navy">5,000+</div>
              <p className="text-xs text-muted-foreground">Alumni Worldwide</p>
            </div>
            <div>
              <div className="text-lg font-bold text-protec-navy">85%</div>
              <p className="text-xs text-muted-foreground">Employment Rate</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
