"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import {
  Calendar,
  MapPin,
  Users,
  ArrowRight,
  Clock,
  Video
} from "lucide-react"

export function UpcomingEvents() {
  const { data: dashboardData, isLoading } = api.analytics.getUserDashboardStats.useQuery()

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  const upcomingEvents = dashboardData?.upcomingEvents || []

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Upcoming Events
        </CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/events">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {upcomingEvents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="h-8 w-8 mx-auto mb-2" />
            <p>No upcoming events</p>
            <Button variant="outline" size="sm" className="mt-3" asChild>
              <Link href="/events">Browse Events</Link>
            </Button>
          </div>
        ) : (
          upcomingEvents.map((event) => (
            <div key={event.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium text-protec-navy">
                    <Link href={`/events/${event.id}`} className="hover:underline">
                      {event.title}
                    </Link>
                  </h3>
                  <div className="flex items-center mt-1 text-sm text-muted-foreground">
                    <Calendar className="h-3.5 w-3.5 mr-1" />
                    <span>
                      {new Date(event.date).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                      })}
                      {' • '}
                      {event.time}
                    </span>
                  </div>
                  <div className="flex items-center mt-1 text-sm text-muted-foreground">
                    <MapPin className="h-3.5 w-3.5 mr-1" />
                    <span>
                      {typeof event.location === 'string'
                        ? event.location
                        : event.location?.venue || event.location?.platform || 'Location TBD'}
                    </span>
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {event.organizer}
                </Badge>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  )
}
