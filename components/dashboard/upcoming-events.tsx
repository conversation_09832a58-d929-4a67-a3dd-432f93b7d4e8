"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  MapPin, 
  Users, 
  ArrowRight,
  Clock,
  Video
} from "lucide-react"

// Mock data - in real app, this would come from tRPC
const upcomingEvents = [
  {
    id: "1",
    title: "Tech Career Fair 2024",
    date: "2024-02-15",
    time: "09:00",
    location: {
      type: "physical" as const,
      venue: "Sandton Convention Centre",
      city: "Johannesburg"
    },
    attendees: 156,
    category: "career",
    organizer: "PROTEC Events Team"
  },
  {
    id: "2",
    title: "Alumni Networking Mixer",
    date: "2024-02-20",
    time: "18:00",
    location: {
      type: "physical" as const,
      venue: "The Venue, Green Point",
      city: "Cape Town"
    },
    attendees: 89,
    category: "networking",
    organizer: "Cape Town Chapter"
  },
  {
    id: "3",
    title: "AI & Machine Learning Workshop",
    date: "2024-02-25",
    time: "14:00",
    location: {
      type: "virtual" as const,
      platform: "Zoom"
    },
    attendees: 234,
    category: "workshop",
    organizer: "Dr. <PERSON>"
  }
]

const categoryColors = {
  career: "bg-blue-100 text-blue-800",
  networking: "bg-green-100 text-green-800",
  workshop: "bg-purple-100 text-purple-800",
  social: "bg-orange-100 text-orange-800",
  conference: "bg-red-100 text-red-800",
  mentorship: "bg-yellow-100 text-yellow-800"
}

export function UpcomingEvents() {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
  }

  const formatTime = (timeString: string) => {
    return timeString
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Upcoming Events
        </CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/events">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {upcomingEvents.map((event) => (
          <div 
            key={event.id} 
            className="border rounded-lg p-4 hover:shadow-md transition-shadow group cursor-pointer"
          >
            <Link href={`/events/${event.id}`}>
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-protec-navy group-hover:text-protec-red transition-colors">
                      {event.title}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      by {event.organizer}
                    </p>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={categoryColors[event.category as keyof typeof categoryColors]}
                  >
                    {event.category}
                  </Badge>
                </div>

                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(event.date)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{formatTime(event.time)}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                    {event.location.type === 'virtual' ? (
                      <>
                        <Video className="h-4 w-4" />
                        <span>Virtual Event</span>
                      </>
                    ) : (
                      <>
                        <MapPin className="h-4 w-4" />
                        <span>{event.location.city}</span>
                      </>
                    )}
                  </div>
                  <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    <span>{event.attendees} attending</span>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        ))}

        {upcomingEvents.length === 0 && (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <div className="text-muted-foreground mb-2">No upcoming events</div>
            <p className="text-sm text-muted-foreground mb-4">
              Check back later for new events or create your own
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/events/new">Create Event</Link>
            </Button>
          </div>
        )}

        <div className="pt-4 border-t">
          <Button variant="outline" className="w-full" asChild>
            <Link href="/events">
              <Calendar className="mr-2 h-4 w-4" />
              View All Events
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
