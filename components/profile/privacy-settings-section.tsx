"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { api } from "@/components/providers/trpc-provider"
import { toast } from "sonner"
import { Shield, Save, Eye, EyeOff } from "lucide-react"

interface PrivacySettingsSectionProps {
  profile: {
    privacy: any
  }
  isCompact?: boolean
  onUpdate: () => void
}

export function PrivacySettingsSection({ profile, isCompact = false, onUpdate }: PrivacySettingsSectionProps) {
  const privacy = profile.privacy || {}
  
  const [settings, setSettings] = useState({
    showEmail: privacy.showEmail ?? false,
    showPhone: privacy.showPhone ?? false,
    showLocation: privacy.showLocation ?? true,
    showConnections: privacy.showConnections ?? true,
    showCareerHistory: privacy.showCareerHistory ?? true,
    showEducation: privacy.showEducation ?? true,
    showProtecInvolvement: privacy.showProtecInvolvement ?? true,
  })

  const updatePrivacyMutation = api.profile.updatePrivacy.useMutation({
    onSuccess: () => {
      toast.success("Privacy settings updated!")
      onUpdate()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update privacy settings")
    },
  })

  const handleSettingChange = (key: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const saveSettings = async () => {
    await updatePrivacyMutation.mutateAsync(settings)
  }

  const privacyOptions = [
    {
      key: 'showEmail',
      label: 'Email Address',
      description: 'Allow other alumni to see your email address',
    },
    {
      key: 'showPhone',
      label: 'Phone Number',
      description: 'Allow other alumni to see your phone number',
    },
    {
      key: 'showLocation',
      label: 'Location',
      description: 'Show your city and province to other alumni',
    },
    {
      key: 'showConnections',
      label: 'Connections',
      description: 'Allow others to see your alumni connections',
    },
    {
      key: 'showCareerHistory',
      label: 'Career History',
      description: 'Display your work experience to other alumni',
    },
    {
      key: 'showEducation',
      label: 'Education',
      description: 'Show your educational background',
    },
    {
      key: 'showProtecInvolvement',
      label: 'PROTEC Involvement',
      description: 'Display your PROTEC programme participation',
    },
  ]

  if (isCompact) {
    const visibleCount = Object.values(settings).filter(Boolean).length
    const totalCount = Object.keys(settings).length

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Visibility</span>
          <span className="font-medium">{visibleCount}/{totalCount} visible</span>
        </div>
        
        <div className="space-y-2">
          {privacyOptions.slice(0, 4).map((option) => (
            <div key={option.key} className="flex items-center justify-between">
              <Label htmlFor={option.key} className="text-xs cursor-pointer">
                {option.label}
              </Label>
              <div className="flex items-center space-x-1">
                {settings[option.key as keyof typeof settings] ? (
                  <Eye className="h-3 w-3 text-green-600" />
                ) : (
                  <EyeOff className="h-3 w-3 text-muted-foreground" />
                )}
                <Switch
                  id={option.key}
                  checked={settings[option.key as keyof typeof settings]}
                  onCheckedChange={(checked) => handleSettingChange(option.key, checked)}
                  size="sm"
                />
              </div>
            </div>
          ))}
        </div>
        
        <Button 
          onClick={saveSettings} 
          disabled={updatePrivacyMutation.isLoading}
          size="sm"
          className="w-full"
        >
          {updatePrivacyMutation.isLoading ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-3 w-3" />
              Save Settings
            </>
          )}
        </Button>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          Privacy Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Control what information is visible to other alumni in the network.
          </p>
          
          {privacyOptions.map((option) => (
            <div key={option.key} className="flex items-center justify-between space-x-4">
              <div className="flex-1">
                <Label htmlFor={option.key} className="font-medium cursor-pointer">
                  {option.label}
                </Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {option.description}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                {settings[option.key as keyof typeof settings] ? (
                  <Eye className="h-4 w-4 text-green-600" />
                ) : (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                )}
                <Switch
                  id={option.key}
                  checked={settings[option.key as keyof typeof settings]}
                  onCheckedChange={(checked) => handleSettingChange(option.key, checked)}
                />
              </div>
            </div>
          ))}
        </div>
        
        <div className="pt-4 border-t">
          <Button 
            onClick={saveSettings} 
            disabled={updatePrivacyMutation.isLoading}
            className="w-full"
          >
            {updatePrivacyMutation.isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Privacy Settings
              </>
            )}
          </Button>
        </div>
        
        <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
          <p className="font-medium mb-1">Privacy Notice</p>
          <p>
            Your privacy is important to us. These settings control visibility within the PROTEC alumni network only. 
            Your information is never shared with external parties without your explicit consent.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
