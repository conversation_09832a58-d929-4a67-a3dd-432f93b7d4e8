"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { api } from "@/components/providers/trpc-provider"
import { toast } from "sonner"
import { 
  Save, 
  Plus, 
  X, 
  Loader2,
  User,
  Briefcase,
  GraduationCap,
  Heart,
  Settings,
  MapPin,
  Calendar,
  Building,
  Award
} from "lucide-react"

interface ProfileEditFormProps {
  profile: any
  section: 'personal' | 'professional' | 'education' | 'protec' | 'preferences'
  onSave: () => void
}

// Schema definitions for different sections
const personalInfoSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  bio: z.string().max(500).optional(),
  photoUrl: z.string().url().optional().or(z.literal("")),
  currentRole: z.string().max(100).optional(),
  company: z.string().max(100).optional(),
  industry: z.string().max(50).optional(),
  province: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  country: z.string().max(50).optional(),
  skills: z.array(z.string()).optional(),
  interests: z.array(z.string()).optional(),
  socialLinks: z.object({
    linkedin: z.string().url().optional().or(z.literal("")),
    twitter: z.string().url().optional().or(z.literal("")),
    github: z.string().url().optional().or(z.literal("")),
    website: z.string().url().optional().or(z.literal("")),
  }).optional(),
})

const preferencesSchema = z.object({
  privacy: z.object({
    showEmail: z.boolean().optional(),
    showPhone: z.boolean().optional(),
    showLocation: z.boolean().optional(),
    showConnections: z.boolean().optional(),
  }).optional(),
  notifications: z.object({
    emailUpdates: z.boolean().optional(),
    eventReminders: z.boolean().optional(),
    connectionRequests: z.boolean().optional(),
    weeklyDigest: z.boolean().optional(),
  }).optional(),
})

export function ProfileEditForm({ profile, section, onSave }: ProfileEditFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [skillsInput, setSkillsInput] = useState("")
  const [interestsInput, setInterestsInput] = useState("")

  const updateProfile = api.alumni.update.useMutation({
    onSuccess: () => {
      toast.success("Profile updated successfully!")
      onSave()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update profile")
    }
  })

  const form = useForm({
    resolver: zodResolver(section === 'preferences' ? preferencesSchema : personalInfoSchema),
    defaultValues: getDefaultValues(profile, section)
  })

  function getDefaultValues(profile: any, section: string) {
    switch (section) {
      case 'personal':
        return {
          name: profile?.name || "",
          bio: profile?.bio || "",
          photoUrl: profile?.photoUrl || "",
          currentRole: profile?.currentRole || "",
          company: profile?.company || "",
          industry: profile?.industry || "",
          province: profile?.province || "",
          city: profile?.city || "",
          country: profile?.country || "",
          skills: profile?.skills || [],
          interests: profile?.interests || [],
          socialLinks: {
            linkedin: profile?.socialLinks?.linkedin || "",
            twitter: profile?.socialLinks?.twitter || "",
            github: profile?.socialLinks?.github || "",
            website: profile?.socialLinks?.website || "",
          }
        }
      case 'preferences':
        return {
          privacy: {
            showEmail: profile?.privacy?.showEmail ?? true,
            showPhone: profile?.privacy?.showPhone ?? false,
            showLocation: profile?.privacy?.showLocation ?? true,
            showConnections: profile?.privacy?.showConnections ?? true,
          },
          notifications: {
            emailUpdates: profile?.notifications?.emailUpdates ?? true,
            eventReminders: profile?.notifications?.eventReminders ?? true,
            connectionRequests: profile?.notifications?.connectionRequests ?? true,
            weeklyDigest: profile?.notifications?.weeklyDigest ?? true,
          }
        }
      default:
        return {}
    }
  }

  const onSubmit = async (data: any) => {
    setIsSubmitting(true)
    try {
      await updateProfile.mutateAsync(data)
    } finally {
      setIsSubmitting(false)
    }
  }

  const addSkill = () => {
    if (skillsInput.trim()) {
      const currentSkills = form.getValues("skills") || []
      const newSkills = [...currentSkills, skillsInput.trim()]
      form.setValue("skills", newSkills)
      setSkillsInput("")
    }
  }

  const removeSkill = (skillToRemove: string) => {
    const currentSkills = form.getValues("skills") || []
    const newSkills = currentSkills.filter(skill => skill !== skillToRemove)
    form.setValue("skills", newSkills)
  }

  const addInterest = () => {
    if (interestsInput.trim()) {
      const currentInterests = form.getValues("interests") || []
      const newInterests = [...currentInterests, interestsInput.trim()]
      form.setValue("interests", newInterests)
      setInterestsInput("")
    }
  }

  const removeInterest = (interestToRemove: string) => {
    const currentInterests = form.getValues("interests") || []
    const newInterests = currentInterests.filter(interest => interest !== interestToRemove)
    form.setValue("interests", newInterests)
  }

  const renderPersonalSection = () => (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name *</FormLabel>
              <FormControl>
                <Input placeholder="Enter your full name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="photoUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Profile Photo URL</FormLabel>
              <FormControl>
                <Input placeholder="https://example.com/photo.jpg" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="bio"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Bio</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="Tell us about yourself..."
                className="min-h-[100px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="currentRole"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Current Role</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Software Engineer" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="company"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Google" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <FormField
          control={form.control}
          name="industry"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Industry</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Technology" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="city"
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Cape Town" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="province"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Province</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="western-cape">Western Cape</SelectItem>
                    <SelectItem value="gauteng">Gauteng</SelectItem>
                    <SelectItem value="kwazulu-natal">KwaZulu-Natal</SelectItem>
                    <SelectItem value="eastern-cape">Eastern Cape</SelectItem>
                    <SelectItem value="free-state">Free State</SelectItem>
                    <SelectItem value="limpopo">Limpopo</SelectItem>
                    <SelectItem value="mpumalanga">Mpumalanga</SelectItem>
                    <SelectItem value="north-west">North West</SelectItem>
                    <SelectItem value="northern-cape">Northern Cape</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Skills Section */}
      <div className="space-y-3">
        <Label>Skills</Label>
        <div className="flex gap-2">
          <Input
            placeholder="Add a skill..."
            value={skillsInput}
            onChange={(e) => setSkillsInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
          />
          <Button type="button" onClick={addSkill} size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {(form.watch("skills") || []).map((skill: string, index: number) => (
            <Badge key={index} variant="secondary" className="flex items-center gap-1">
              {skill}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => removeSkill(skill)}
              />
            </Badge>
          ))}
        </div>
      </div>

      {/* Interests Section */}
      <div className="space-y-3">
        <Label>Interests</Label>
        <div className="flex gap-2">
          <Input
            placeholder="Add an interest..."
            value={interestsInput}
            onChange={(e) => setInterestsInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addInterest())}
          />
          <Button type="button" onClick={addInterest} size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {(form.watch("interests") || []).map((interest: string, index: number) => (
            <Badge key={index} variant="secondary" className="flex items-center gap-1">
              {interest}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => removeInterest(interest)}
              />
            </Badge>
          ))}
        </div>
      </div>

      {/* Social Links */}
      <div className="space-y-4">
        <Label>Social Links</Label>
        <div className="grid gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="socialLinks.linkedin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>LinkedIn</FormLabel>
                <FormControl>
                  <Input placeholder="https://linkedin.com/in/username" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="socialLinks.twitter"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Twitter</FormLabel>
                <FormControl>
                  <Input placeholder="https://twitter.com/username" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="socialLinks.github"
            render={({ field }) => (
              <FormItem>
                <FormLabel>GitHub</FormLabel>
                <FormControl>
                  <Input placeholder="https://github.com/username" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="socialLinks.website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input placeholder="https://yourwebsite.com" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  )

  const renderPreferencesSection = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Privacy Settings
          </CardTitle>
          <CardDescription>
            Control what information is visible to other alumni
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="privacy.showEmail"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Show Email Address</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Allow other alumni to see your email address
                  </div>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="privacy.showLocation"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Show Location</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Display your city and province on your profile
                  </div>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="privacy.showConnections"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Show Connections</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Allow others to see your connection count and mutual connections
                  </div>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Choose what notifications you'd like to receive
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="notifications.emailUpdates"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Email Updates</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Receive important updates and announcements via email
                  </div>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notifications.eventReminders"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Event Reminders</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Get notified about upcoming events you've RSVP'd to
                  </div>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notifications.connectionRequests"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Connection Requests</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Be notified when someone wants to connect with you
                  </div>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notifications.weeklyDigest"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Weekly Digest</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Receive a weekly summary of community activity
                  </div>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  )

  const renderPlaceholderSection = (sectionName: string, icon: any) => {
    const Icon = icon
    return (
      <div className="text-center py-12">
        <Icon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {sectionName} Section
        </h3>
        <p className="text-gray-600 mb-4">
          This section is coming soon. You'll be able to manage your {sectionName.toLowerCase()} information here.
        </p>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {section === 'personal' && renderPersonalSection()}
        {section === 'professional' && renderPlaceholderSection('Professional', Briefcase)}
        {section === 'education' && renderPlaceholderSection('Education', GraduationCap)}
        {section === 'protec' && renderPlaceholderSection('PROTEC Involvement', Heart)}
        {section === 'preferences' && renderPreferencesSection()}

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-protec-red hover:bg-protec-red/90"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}
