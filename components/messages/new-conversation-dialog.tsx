"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { api } from "@/components/providers/trpc-provider"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { Search, Users, X, MessageSquare } from "lucide-react"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface NewConversationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConversationCreated: (conversationId: string) => void
}

export function NewConversationDialog({
  open,
  onOpenChange,
  onConversationCreated,
}: NewConversationDialogProps) {
  const { data: session } = useSession()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>([])
  const [conversationTitle, setConversationTitle] = useState("")
  const [isGroup, setIsGroup] = useState(false)

  const { data: alumniData, isLoading } = api.alumni.getAll.useQuery({
    search: searchTerm || undefined,
    limit: 50,
  })

  const createConversationMutation = api.messages.createConversation.useMutation({
    onSuccess: (conversation) => {
      toast.success("Conversation created successfully")
      onConversationCreated(conversation.id)
      handleClose()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to create conversation")
    },
  })

  const handleClose = () => {
    setSearchTerm("")
    setSelectedParticipants([])
    setConversationTitle("")
    setIsGroup(false)
    onOpenChange(false)
  }

  const handleParticipantToggle = (participantId: string) => {
    setSelectedParticipants(prev => {
      const newSelection = prev.includes(participantId)
        ? prev.filter(id => id !== participantId)
        : [...prev, participantId]
      
      // Auto-enable group mode if more than 1 participant selected
      setIsGroup(newSelection.length > 1)
      
      return newSelection
    })
  }

  const handleCreateConversation = async () => {
    if (selectedParticipants.length === 0) {
      toast.error("Please select at least one participant")
      return
    }

    if (isGroup && !conversationTitle.trim()) {
      toast.error("Please enter a title for the group conversation")
      return
    }

    try {
      await createConversationMutation.mutateAsync({
        participantIds: selectedParticipants,
        title: isGroup ? conversationTitle.trim() : undefined,
        isGroup,
      })
    } catch (error) {
      // Error handled in mutation
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const alumni = alumniData?.alumni.filter(a => a.id !== session?.user?.id) || []

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <MessageSquare className="mr-2 h-6 w-6 text-primary" />
            New Conversation
          </DialogTitle>
          <DialogDescription className="text-base">
            Connect with fellow PROTEC alumni privately and securely
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Search Alumni</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, role, or company..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rounded-lg border-2 focus:border-primary"
              />
            </div>
          </div>

          {/* Selected Participants */}
          {selectedParticipants.length > 0 && (
            <div className="space-y-2">
              <Label>Selected ({selectedParticipants.length})</Label>
              <div className="flex flex-wrap gap-2">
                {selectedParticipants.map(participantId => {
                  const participant = alumni.find(a => a.id === participantId)
                  if (!participant) return null
                  
                  return (
                    <Badge key={participantId} variant="secondary" className="flex items-center gap-1">
                      {participant.name}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleParticipantToggle(participantId)}
                      />
                    </Badge>
                  )
                })}
              </div>
            </div>
          )}

          {/* Group Options */}
          {selectedParticipants.length > 1 && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isGroup"
                  checked={isGroup}
                  onCheckedChange={(checked) => setIsGroup(checked as boolean)}
                />
                <Label htmlFor="isGroup" className="flex items-center">
                  <Users className="mr-1 h-4 w-4" />
                  Create as group conversation
                </Label>
              </div>
              
              {isGroup && (
                <div className="space-y-2">
                  <Label htmlFor="title">Group Title</Label>
                  <Input
                    id="title"
                    placeholder="Enter group title..."
                    value={conversationTitle}
                    onChange={(e) => setConversationTitle(e.target.value)}
                  />
                </div>
              )}
            </div>
          )}

          {/* Alumni List */}
          <div className="space-y-2">
            <Label>Available Alumni</Label>
            <ScrollArea className="h-64 border rounded-md">
              {isLoading ? (
                <div className="p-4 space-y-3">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : alumni.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  {searchTerm ? (
                    <p>No alumni found for "{searchTerm}"</p>
                  ) : (
                    <p>No alumni available</p>
                  )}
                </div>
              ) : (
                <div className="p-2">
                  {alumni.map((alumnus) => {
                    const isSelected = selectedParticipants.includes(alumnus.id)
                    
                    return (
                      <div
                        key={alumnus.id}
                        onClick={() => handleParticipantToggle(alumnus.id)}
                        className={cn(
                          "flex items-center space-x-3 p-2 rounded-md cursor-pointer transition-colors hover:bg-accent",
                          isSelected && "bg-protec-red/10"
                        )}
                      >
                        <Checkbox
                          checked={isSelected}
                          onChange={() => {}} // Handled by parent click
                        />
                        
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={alumnus.photoUrl || ""} alt={alumnus.name} />
                          <AvatarFallback className="bg-protec-navy text-white">
                            {getInitials(alumnus.name)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">{alumnus.name}</p>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            {alumnus.currentRole && (
                              <span className="truncate">{alumnus.currentRole}</span>
                            )}
                            {alumnus.currentRole && alumnus.city && (
                              <span>•</span>
                            )}
                            {alumnus.city && (
                              <span>{alumnus.city}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateConversation}
            disabled={
              selectedParticipants.length === 0 || 
              createConversationMutation.isLoading ||
              (isGroup && !conversationTitle.trim())
            }
            className="bg-protec-red hover:bg-protec-red/90"
          >
            {createConversationMutation.isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Creating...
              </>
            ) : (
              <>
                <MessageSquare className="mr-2 h-4 w-4" />
                Create Conversation
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
