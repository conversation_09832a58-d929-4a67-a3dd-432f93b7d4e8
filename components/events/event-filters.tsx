"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar, MapPin, Filter, X } from "lucide-react"

interface EventFilters {
  category?: string
  startDate?: Date
  endDate?: Date
  location?: string
}

interface EventFiltersProps {
  filters: EventFilters
  onFiltersChange: (filters: EventFilters) => void
}

const categories = [
  { value: "networking", label: "Networking" },
  { value: "workshop", label: "Workshop" },
  { value: "conference", label: "Conference" },
  { value: "social", label: "Social" },
  { value: "career", label: "Career" },
  { value: "mentorship", label: "Mentorship" }
]

const timeRanges = [
  { value: "today", label: "Today" },
  { value: "tomorrow", label: "Tomorrow" },
  { value: "this-week", label: "This Week" },
  { value: "next-week", label: "Next Week" },
  { value: "this-month", label: "This Month" },
  { value: "next-month", label: "Next Month" }
]

export function EventFilters({ filters, onFiltersChange }: EventFiltersProps) {
  const [localFilters, setLocalFilters] = useState<EventFilters>(filters)

  const handleFilterChange = (key: keyof EventFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const handleTimeRangeChange = (range: string) => {
    const now = new Date()
    let startDate: Date | undefined
    let endDate: Date | undefined

    switch (range) {
      case "today":
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
        break
      case "tomorrow":
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 2)
        break
      case "this-week":
        const startOfWeek = new Date(now)
        startOfWeek.setDate(now.getDate() - now.getDay())
        const endOfWeek = new Date(startOfWeek)
        endOfWeek.setDate(startOfWeek.getDate() + 7)
        startDate = startOfWeek
        endDate = endOfWeek
        break
      case "next-week":
        const nextWeekStart = new Date(now)
        nextWeekStart.setDate(now.getDate() - now.getDay() + 7)
        const nextWeekEnd = new Date(nextWeekStart)
        nextWeekEnd.setDate(nextWeekStart.getDate() + 7)
        startDate = nextWeekStart
        endDate = nextWeekEnd
        break
      case "this-month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1)
        break
      case "next-month":
        startDate = new Date(now.getFullYear(), now.getMonth() + 1, 1)
        endDate = new Date(now.getFullYear(), now.getMonth() + 2, 1)
        break
    }

    const newFilters = { ...localFilters, startDate, endDate }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const clearFilters = () => {
    const emptyFilters = {}
    setLocalFilters(emptyFilters)
    onFiltersChange(emptyFilters)
  }

  const hasActiveFilters = Object.keys(localFilters).length > 0

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
          <Filter className="mr-2 h-5 w-5" />
          Filters
        </CardTitle>
        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="mr-1 h-4 w-4" />
            Clear
          </Button>
        )}
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Category Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Category</Label>
          <Select
            value={localFilters.category || ""}
            onValueChange={(value) => handleFilterChange("category", value || undefined)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Time Range Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">When</Label>
          <Select onValueChange={handleTimeRangeChange}>
            <SelectTrigger>
              <SelectValue placeholder="Any time" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Any time</SelectItem>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Custom Date Range */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Custom Date Range</Label>
          <div className="space-y-2">
            <div>
              <Label className="text-xs text-muted-foreground">From</Label>
              <Input
                type="date"
                value={localFilters.startDate?.toISOString().split('T')[0] || ""}
                onChange={(e) => handleFilterChange("startDate", e.target.value ? new Date(e.target.value) : undefined)}
              />
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">To</Label>
              <Input
                type="date"
                value={localFilters.endDate?.toISOString().split('T')[0] || ""}
                onChange={(e) => handleFilterChange("endDate", e.target.value ? new Date(e.target.value) : undefined)}
              />
            </div>
          </div>
        </div>

        {/* Location Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Location</Label>
          <div className="relative">
            <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="City or venue"
              className="pl-10"
              value={localFilters.location || ""}
              onChange={(e) => handleFilterChange("location", e.target.value || undefined)}
            />
          </div>
        </div>

        {/* Active Filters */}
        {hasActiveFilters && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Active Filters</Label>
            <div className="flex flex-wrap gap-2">
              {localFilters.category && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  {categories.find(c => c.value === localFilters.category)?.label}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange("category", undefined)}
                  />
                </Badge>
              )}
              {localFilters.startDate && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  From {localFilters.startDate.toLocaleDateString()}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange("startDate", undefined)}
                  />
                </Badge>
              )}
              {localFilters.endDate && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  To {localFilters.endDate.toLocaleDateString()}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange("endDate", undefined)}
                  />
                </Badge>
              )}
              {localFilters.location && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  {localFilters.location}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange("location", undefined)}
                  />
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="space-y-3 pt-4 border-t">
          <Label className="text-sm font-medium">Quick Filters</Label>
          <div className="grid grid-cols-1 gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="justify-start"
              onClick={() => handleTimeRangeChange("this-week")}
            >
              <Calendar className="mr-2 h-4 w-4" />
              This Week
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="justify-start"
              onClick={() => handleFilterChange("category", "networking")}
            >
              Networking Events
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="justify-start"
              onClick={() => handleFilterChange("location", "virtual")}
            >
              Virtual Events
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
