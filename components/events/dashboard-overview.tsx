"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  Users, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  Target
} from "lucide-react"

// Mock data - in real app, this would come from tRPC
const eventsData = {
  totalEvents: {
    current: 12,
    upcoming: 5,
    past: 7,
    draft: 2
  },
  totalAttendees: {
    current: 1247,
    thisMonth: 234,
    avgPerEvent: 104
  },
  rsvpRate: {
    current: 78.5,
    previous: 72.3,
    change: 8.6
  },
  attendanceRate: {
    current: 85.2,
    previous: 81.7,
    change: 4.3
  },
  satisfaction: {
    current: 4.6,
    previous: 4.3,
    change: 7.0
  },
  revenue: {
    current: 45000,
    previous: 38000,
    change: 18.4
  }
}

interface EventMetricCardProps {
  title: string
  value: string | number
  subtitle?: string
  change?: number
  icon: React.ElementType
  color?: string
  badge?: string
}

function EventMetricCard({ 
  title, 
  value, 
  subtitle, 
  change, 
  icon: Icon, 
  color = "text-protec-navy",
  badge 
}: EventMetricCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={`h-4 w-4 ${color}`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-protec-navy">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        {subtitle && (
          <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
        )}
        {change !== undefined && (
          <div className="flex items-center space-x-2 mt-2">
            <TrendingUp className={`h-4 w-4 ${change >= 0 ? 'text-green-600' : 'text-red-600'}`} />
            <Badge 
              variant="secondary" 
              className={change >= 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
            >
              {change >= 0 ? "+" : ""}{change.toFixed(1)}%
            </Badge>
          </div>
        )}
        {badge && (
          <Badge variant="secondary" className="mt-2">
            {badge}
          </Badge>
        )}
      </CardContent>
    </Card>
  )
}

export function EventsDashboardOverview() {
  return (
    <div className="space-y-6">
      {/* Main Metrics Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <EventMetricCard
          title="Total Events"
          value={eventsData.totalEvents.current}
          subtitle={`${eventsData.totalEvents.upcoming} upcoming, ${eventsData.totalEvents.past} completed`}
          icon={Calendar}
          color="text-blue-600"
        />
        
        <EventMetricCard
          title="Total Attendees"
          value={eventsData.totalAttendees.current}
          subtitle={`${eventsData.totalAttendees.thisMonth} this month`}
          icon={Users}
          color="text-green-600"
        />
        
        <EventMetricCard
          title="RSVP Rate"
          value={`${eventsData.rsvpRate.current}%`}
          change={eventsData.rsvpRate.change}
          icon={CheckCircle}
          color="text-purple-600"
        />
        
        <EventMetricCard
          title="Attendance Rate"
          value={`${eventsData.attendanceRate.current}%`}
          change={eventsData.attendanceRate.change}
          icon={Target}
          color="text-orange-600"
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid gap-6 md:grid-cols-3">
        <EventMetricCard
          title="Avg. Satisfaction"
          value={`${eventsData.satisfaction.current}/5.0`}
          change={eventsData.satisfaction.change}
          icon={Star}
          color="text-yellow-600"
          badge="Excellent"
        />
        
        <EventMetricCard
          title="Event Revenue"
          value={`R${eventsData.revenue.current.toLocaleString()}`}
          change={eventsData.revenue.change}
          icon={TrendingUp}
          color="text-green-600"
          subtitle="From paid events"
        />
        
        <EventMetricCard
          title="Avg. per Event"
          value={eventsData.totalAttendees.avgPerEvent}
          subtitle="Attendees per event"
          icon={Users}
          color="text-blue-600"
        />
      </div>

      {/* Event Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-protec-navy">
            Event Status Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-2 bg-blue-100 rounded-full">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-blue-900">
                {eventsData.totalEvents.upcoming}
              </div>
              <p className="text-sm text-blue-700">Upcoming Events</p>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-2 bg-green-100 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-900">
                {eventsData.totalEvents.past}
              </div>
              <p className="text-sm text-green-700">Completed Events</p>
            </div>
            
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-2 bg-yellow-100 rounded-full">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-yellow-900">
                {eventsData.totalEvents.draft}
              </div>
              <p className="text-sm text-yellow-700">Draft Events</p>
            </div>
            
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-2 bg-red-100 rounded-full">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-red-900">2</div>
              <p className="text-sm text-red-700">Need Attention</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-protec-navy">
            Performance Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="p-3 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Best Performing</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Top
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                "Tech Career Fair 2024" - 95% attendance
              </p>
            </div>
            
            <div className="p-3 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Most Popular</span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  234 RSVPs
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                "Alumni Networking Mixer"
              </p>
            </div>
            
            <div className="p-3 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Highest Rated</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  4.9/5.0
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                "AI Workshop Series"
              </p>
            </div>
            
            <div className="p-3 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Growth Trend</span>
                <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                  +23%
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Attendance vs last quarter
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
