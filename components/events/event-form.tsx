"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { api } from "@/components/providers/trpc-provider"
import { Loader2, Calendar, MapPin, Video } from "lucide-react"
import { toast } from "sonner"

const eventSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().min(1, "Description is required").max(2000, "Description too long"),
  category: z.enum(['networking', 'workshop', 'conference', 'social', 'career', 'mentorship']),
  startDate: z.string().min(1, "Start date is required"),
  startTime: z.string().min(1, "Start time is required"),
  endDate: z.string().min(1, "End date is required"),
  endTime: z.string().min(1, "End time is required"),
  locationType: z.enum(['physical', 'virtual', 'hybrid']),
  address: z.string().optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  virtualLink: z.string().url().optional(),
})

type EventFormData = z.infer<typeof eventSchema>

const categories = [
  { value: "networking", label: "Networking" },
  { value: "workshop", label: "Workshop" },
  { value: "conference", label: "Conference" },
  { value: "social", label: "Social" },
  { value: "career", label: "Career" },
  { value: "mentorship", label: "Mentorship" }
]

const provinces = [
  "Eastern Cape",
  "Free State",
  "Gauteng",
  "KwaZulu-Natal",
  "Limpopo",
  "Mpumalanga",
  "Northern Cape",
  "North West",
  "Western Cape"
]

export function EventForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const createEventMutation = api.events.create.useMutation()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<EventFormData>({
    resolver: zodResolver(eventSchema),
    defaultValues: {
      locationType: 'physical'
    }
  })

  const locationType = watch('locationType')

  const onSubmit = async (data: EventFormData) => {
    setIsSubmitting(true)
    
    try {
      // Combine date and time
      const startDateTime = new Date(`${data.startDate}T${data.startTime}`)
      const endDateTime = new Date(`${data.endDate}T${data.endTime}`)

      // Validate dates
      if (endDateTime <= startDateTime) {
        toast.error("End time must be after start time")
        setIsSubmitting(false)
        return
      }

      if (startDateTime <= new Date()) {
        toast.error("Event must be scheduled for a future date")
        setIsSubmitting(false)
        return
      }

      // Prepare location data
      const location = {
        type: data.locationType,
        ...(data.locationType !== 'virtual' && {
          address: data.address,
          city: data.city,
          province: data.province,
        }),
        ...(data.locationType !== 'physical' && {
          virtualLink: data.virtualLink,
        }),
      }

      const event = await createEventMutation.mutateAsync({
        title: data.title,
        description: data.description,
        category: data.category,
        startTime: startDateTime,
        endTime: endDateTime,
        location,
      })

      toast.success("Event created successfully!")
      router.push(`/events/${event.id}`)
    } catch (error) {
      console.error("Error creating event:", error)
      toast.error("Failed to create event. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-protec-navy">Event Details</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Event Title *</Label>
              <Input
                id="title"
                {...register("title")}
                placeholder="e.g. Alumni Networking Mixer"
                className={errors.title ? "border-red-500" : ""}
              />
              {errors.title && (
                <p className="text-sm text-red-500 mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder="Describe your event, what attendees can expect, and any special requirements..."
                rows={4}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">{errors.description.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="category">Category *</Label>
              <Select onValueChange={(value) => setValue("category", value as any)}>
                <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select event category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-500 mt-1">{errors.category.message}</p>
              )}
            </div>
          </div>

          {/* Date and Time */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-protec-navy flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Date & Time
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date *</Label>
                <Input
                  id="startDate"
                  type="date"
                  {...register("startDate")}
                  min={new Date().toISOString().split('T')[0]}
                  className={errors.startDate ? "border-red-500" : ""}
                />
                {errors.startDate && (
                  <p className="text-sm text-red-500 mt-1">{errors.startDate.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="startTime">Start Time *</Label>
                <Input
                  id="startTime"
                  type="time"
                  {...register("startTime")}
                  className={errors.startTime ? "border-red-500" : ""}
                />
                {errors.startTime && (
                  <p className="text-sm text-red-500 mt-1">{errors.startTime.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="endDate">End Date *</Label>
                <Input
                  id="endDate"
                  type="date"
                  {...register("endDate")}
                  min={new Date().toISOString().split('T')[0]}
                  className={errors.endDate ? "border-red-500" : ""}
                />
                {errors.endDate && (
                  <p className="text-sm text-red-500 mt-1">{errors.endDate.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="endTime">End Time *</Label>
                <Input
                  id="endTime"
                  type="time"
                  {...register("endTime")}
                  className={errors.endTime ? "border-red-500" : ""}
                />
                {errors.endTime && (
                  <p className="text-sm text-red-500 mt-1">{errors.endTime.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-protec-navy flex items-center">
              <MapPin className="mr-2 h-5 w-5" />
              Location
            </h3>

            <div>
              <Label>Location Type *</Label>
              <RadioGroup
                value={locationType}
                onValueChange={(value) => setValue("locationType", value as any)}
                className="flex flex-col space-y-2 mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="physical" id="physical" />
                  <Label htmlFor="physical">Physical Location</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="virtual" id="virtual" />
                  <Label htmlFor="virtual">Virtual Event</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="hybrid" id="hybrid" />
                  <Label htmlFor="hybrid">Hybrid (Physical + Virtual)</Label>
                </div>
              </RadioGroup>
            </div>

            {(locationType === 'physical' || locationType === 'hybrid') && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="address">Venue Address</Label>
                  <Input
                    id="address"
                    {...register("address")}
                    placeholder="e.g. 123 Main Street, Conference Center"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      {...register("city")}
                      placeholder="e.g. Cape Town"
                    />
                  </div>

                  <div>
                    <Label htmlFor="province">Province</Label>
                    <Select onValueChange={(value) => setValue("province", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select province" />
                      </SelectTrigger>
                      <SelectContent>
                        {provinces.map((province) => (
                          <SelectItem key={province} value={province}>
                            {province}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {(locationType === 'virtual' || locationType === 'hybrid') && (
              <div>
                <Label htmlFor="virtualLink" className="flex items-center">
                  <Video className="mr-2 h-4 w-4" />
                  Virtual Meeting Link
                </Label>
                <Input
                  id="virtualLink"
                  {...register("virtualLink")}
                  placeholder="https://zoom.us/j/..."
                  type="url"
                  className={errors.virtualLink ? "border-red-500" : ""}
                />
                {errors.virtualLink && (
                  <p className="text-sm text-red-500 mt-1">{errors.virtualLink.message}</p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Zoom, Google Meet, Teams, or any other video conferencing link
                </p>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-protec-red hover:bg-protec-red/90"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Event...
                </>
              ) : (
                "Create Event"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
