"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  MapPin,
  Users
} from "lucide-react"
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths } from "date-fns"

interface Event {
  id: string
  title: string
  date: Date
  time: string
  location: string
  attendees: number
  status: 'upcoming' | 'ongoing' | 'completed'
  type: 'workshop' | 'gala' | 'networking' | 'seminar'
}

interface EventsCalendarViewProps {
  className?: string
}

export function EventsCalendarView({ className }: EventsCalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  
  // Get events from API
  const { data: eventsData, isLoading } = api.events.getAll.useQuery({
    startDate: startOfMonth(currentDate),
    endDate: endOfMonth(currentDate),
    limit: 50
  })

  const events: Event[] = (eventsData?.events || []).map(event => ({
    id: event.id,
    title: event.title,
    date: new Date(event.startTime),
    time: new Date(event.startTime).toTimeString().slice(0, 5),
    location: typeof event.location === 'string' ? event.location :
              event.location?.venue || event.location?.platform || 'Location TBD',
    attendees: event._count?.rsvps || 0,
    status: new Date(event.startTime) > new Date() ? 'upcoming' : 'completed',
    type: 'workshop' // Default type, could be enhanced with event categories
  }))

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-lg font-semibold text-protec-navy">
            <Skeleton className="h-6 w-32" />
          </CardTitle>
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-8 rounded" />
            <Skeleton className="h-8 w-8 rounded" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-1">
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="text-center py-2">
                <Skeleton className="h-4 w-8 mx-auto" />
              </div>
            ))}
            {Array.from({ length: 35 }).map((_, i) => (
              <div key={i} className="aspect-square border rounded-md p-1">
                <Skeleton className="h-4 w-4 mb-1" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(currentDate)
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd })

  const getEventsForDay = (day: Date) => {
    return events.filter(event => isSameDay(event.date, day))
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'gala':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'workshop':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'networking':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'seminar':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const previousMonth = () => {
    setCurrentDate(subMonths(currentDate, 1))
  }

  const nextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1))
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-protec-navy flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Events Calendar
          </CardTitle>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={previousMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium min-w-[120px] text-center">
              {format(currentDate, 'MMMM yyyy')}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={nextMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1 text-xs">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-2 text-center font-medium text-muted-foreground">
              {day}
            </div>
          ))}
          
          {calendarDays.map(day => {
            const dayEvents = getEventsForDay(day)
            const isCurrentMonth = isSameMonth(day, currentDate)
            const isToday = isSameDay(day, new Date())
            
            return (
              <div
                key={day.toISOString()}
                className={`
                  min-h-[60px] p-1 border border-gray-100 rounded-sm
                  ${isCurrentMonth ? 'bg-white' : 'bg-gray-50'}
                  ${isToday ? 'ring-2 ring-protec-red ring-opacity-50' : ''}
                `}
              >
                <div className={`
                  text-xs font-medium mb-1
                  ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                  ${isToday ? 'text-protec-red' : ''}
                `}>
                  {format(day, 'd')}
                </div>
                
                {dayEvents.map(event => (
                  <div
                    key={event.id}
                    className={`
                      text-xs p-1 rounded mb-1 truncate cursor-pointer
                      ${getEventTypeColor(event.type)}
                      hover:opacity-80 transition-opacity
                    `}
                    title={`${event.title} - ${event.time} at ${event.location}`}
                  >
                    {event.title}
                  </div>
                ))}
              </div>
            )
          })}
        </div>

        {/* Upcoming Events List */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-protec-navy">Upcoming This Month</h4>
          {events
            .filter(event => isSameMonth(event.date, currentDate))
            .sort((a, b) => a.date.getTime() - b.date.getTime())
            .slice(0, 3)
            .map(event => (
              <div key={event.id} className="flex items-center gap-3 p-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {event.title}
                  </p>
                  <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {format(event.date, 'MMM d')}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {event.time}
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {event.attendees}
                    </span>
                  </div>
                </div>
                <Badge variant="outline" className={getEventTypeColor(event.type)}>
                  {event.type}
                </Badge>
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  )
}
