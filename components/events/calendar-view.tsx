"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight,
  Clock,
  MapPin,
  Users
} from "lucide-react"
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths } from "date-fns"

interface Event {
  id: string
  title: string
  date: Date
  time: string
  location: string
  attendees: number
  status: 'upcoming' | 'ongoing' | 'completed'
  type: 'workshop' | 'gala' | 'networking' | 'seminar'
}

interface EventsCalendarViewProps {
  className?: string
}

export function EventsCalendarView({ className }: EventsCalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  
  // Mock data - replace with real API calls
  const events: Event[] = [
    {
      id: '1',
      title: 'Annual Alumni Gala',
      date: new Date(2024, 3, 15), // April 15, 2024
      time: '18:00',
      location: 'Cape Town Convention Centre',
      attendees: 156,
      status: 'upcoming',
      type: 'gala'
    },
    {
      id: '2',
      title: 'Tech Career Workshop',
      date: new Date(2024, 3, 20), // April 20, 2024
      time: '14:00',
      location: 'PROTEC Campus',
      attendees: 45,
      status: 'upcoming',
      type: 'workshop'
    },
    {
      id: '3',
      title: 'Networking Mixer',
      date: new Date(2024, 3, 25), // April 25, 2024
      time: '17:30',
      location: 'V&A Waterfront',
      attendees: 78,
      status: 'upcoming',
      type: 'networking'
    }
  ]

  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(currentDate)
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd })

  const getEventsForDay = (day: Date) => {
    return events.filter(event => isSameDay(event.date, day))
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'gala':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'workshop':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'networking':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'seminar':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const previousMonth = () => {
    setCurrentDate(subMonths(currentDate, 1))
  }

  const nextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1))
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-protec-navy flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Events Calendar
          </CardTitle>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={previousMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium min-w-[120px] text-center">
              {format(currentDate, 'MMMM yyyy')}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={nextMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1 text-xs">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-2 text-center font-medium text-muted-foreground">
              {day}
            </div>
          ))}
          
          {calendarDays.map(day => {
            const dayEvents = getEventsForDay(day)
            const isCurrentMonth = isSameMonth(day, currentDate)
            const isToday = isSameDay(day, new Date())
            
            return (
              <div
                key={day.toISOString()}
                className={`
                  min-h-[60px] p-1 border border-gray-100 rounded-sm
                  ${isCurrentMonth ? 'bg-white' : 'bg-gray-50'}
                  ${isToday ? 'ring-2 ring-protec-red ring-opacity-50' : ''}
                `}
              >
                <div className={`
                  text-xs font-medium mb-1
                  ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                  ${isToday ? 'text-protec-red' : ''}
                `}>
                  {format(day, 'd')}
                </div>
                
                {dayEvents.map(event => (
                  <div
                    key={event.id}
                    className={`
                      text-xs p-1 rounded mb-1 truncate cursor-pointer
                      ${getEventTypeColor(event.type)}
                      hover:opacity-80 transition-opacity
                    `}
                    title={`${event.title} - ${event.time} at ${event.location}`}
                  >
                    {event.title}
                  </div>
                ))}
              </div>
            )
          })}
        </div>

        {/* Upcoming Events List */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-protec-navy">Upcoming This Month</h4>
          {events
            .filter(event => isSameMonth(event.date, currentDate))
            .sort((a, b) => a.date.getTime() - b.date.getTime())
            .slice(0, 3)
            .map(event => (
              <div key={event.id} className="flex items-center gap-3 p-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {event.title}
                  </p>
                  <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {format(event.date, 'MMM d')}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {event.time}
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {event.attendees}
                    </span>
                  </div>
                </div>
                <Badge variant="outline" className={getEventTypeColor(event.type)}>
                  {event.type}
                </Badge>
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  )
}
