"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { AlumniCard } from "@/components/alumni/alumni-card"
import {
  Search,
  Filter,
  Users,
  Loader2
} from "lucide-react"

interface AlumniFilters {
  query?: string
  graduationYear?: number
  programme?: string
  province?: string
  city?: string
  skills?: string[]
}

interface AlumniDirectoryProps {
  initialFilters?: AlumniFilters
}

import { api } from "@/components/providers/trpc-provider"

export function AlumniDirectory({ initialFilters = {} }: AlumniDirectoryProps) {
  const [filters, setFilters] = useState<AlumniFilters>(initialFilters)
  const [searchQuery, setSearchQuery] = useState(filters.query || "")
  const [showFilters, setShowFilters] = useState(false)

  // Use real API data
  const {
    data: alumniData,
    isLoading,
    error,
    refetch
  } = api.alumni.search.useQuery({
    query: filters.query,
    graduationYear: filters.graduationYear,
    programme: filters.programme,
    province: filters.province,
    city: filters.city,
    skills: filters.skills,
    limit: 20
  })

  const allAlumni = alumniData?.alumni || []

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setFilters(prev => ({ ...prev, query: searchQuery }))
  }

  const handleFilterChange = (newFilters: Partial<AlumniFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">Failed to load alumni directory</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
            Alumni Directory
          </h1>
          <p className="text-muted-foreground">
            Connect with 5,000+ PROTEC graduates worldwide
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name, company, or skills..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-protec-red hover:bg-protec-red/90"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Search className="mr-2 h-4 w-4" />
                )}
                Search
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white"
              >
                <Filter className="mr-2 h-4 w-4" />
                Filters
              </Button>
            </div>
          </form>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Graduation Year</label>
                  <Input
                    type="number"
                    placeholder="e.g. 2020"
                    min="1982"
                    max={new Date().getFullYear()}
                    value={filters.graduationYear || ""}
                    onChange={(e) => handleFilterChange({ 
                      graduationYear: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Province</label>
                  <Input
                    placeholder="e.g. Western Cape"
                    value={filters.province || ""}
                    onChange={(e) => handleFilterChange({ province: e.target.value || undefined })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">City</label>
                  <Input
                    placeholder="e.g. Cape Town"
                    value={filters.city || ""}
                    onChange={(e) => handleFilterChange({ city: e.target.value || undefined })}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-16 w-16 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <Skeleton className="h-3 w-1/3" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
                <div className="flex space-x-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-14" />
                </div>
                <div className="flex justify-between items-center">
                  <Skeleton className="h-3 w-1/3" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : allAlumni.length > 0 ? (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {allAlumni.map((person) => (
              <AlumniCard key={person.id} alumni={person} />
            ))}
          </div>

          {/* Results Summary */}
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Showing {allAlumni.length} alumni
              {filters.query && ` matching "${filters.query}"`}
            </p>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-protec-navy mb-2">No alumni found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or filters
          </p>
          <Button 
            variant="outline" 
            onClick={() => {
              setFilters({})
              setSearchQuery("")
            }}
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}


