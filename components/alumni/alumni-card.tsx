"use client"

import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  MapPin, 
  Briefcase, 
  GraduationCap, 
  Users, 
  MessageCircle,
  ExternalLink
} from "lucide-react"

interface AlumniCardProps {
  alumni: {
    id: string
    name: string
    photoUrl?: string
    bio?: string
    graduationYear: number
    programmes: string[]
    currentRole?: string
    skills: string[]
    province?: string
    city?: string
    _count: {
      connections: number
    }
  }
}

export function AlumniCard({ alumni }: AlumniCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatLocation = () => {
    if (alumni.city && alumni.province) {
      return `${alumni.city}, ${alumni.province}`
    }
    return alumni.city || alumni.province || "Location not specified"
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-protec-red/20 hover:border-l-protec-red">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header with Avatar and Basic Info */}
          <div className="flex items-start space-x-4">
            <Avatar className="h-16 w-16 border-2 border-protec-gray">
              <AvatarImage src={alumni.photoUrl} alt={alumni.name} />
              <AvatarFallback className="bg-protec-navy text-white text-lg font-semibold">
                {getInitials(alumni.name)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <Link 
                href={`/profile/${alumni.id}`}
                className="block group-hover:text-protec-red transition-colors"
              >
                <h3 className="text-lg font-semibold text-protec-navy truncate">
                  {alumni.name}
                </h3>
              </Link>
              
              {alumni.currentRole && (
                <div className="flex items-center text-sm text-muted-foreground mt-1">
                  <Briefcase className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="truncate">{alumni.currentRole}</span>
                </div>
              )}
              
              <div className="flex items-center text-sm text-muted-foreground mt-1">
                <GraduationCap className="h-4 w-4 mr-1 flex-shrink-0" />
                <span>Class of {alumni.graduationYear}</span>
                {alumni.programmes.length > 0 && (
                  <span className="ml-2 text-xs">• {alumni.programmes[0]}</span>
                )}
              </div>
              
              <div className="flex items-center text-sm text-muted-foreground mt-1">
                <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                <span className="truncate">{formatLocation()}</span>
              </div>
            </div>
          </div>

          {/* Bio */}
          {alumni.bio && (
            <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
              {alumni.bio}
            </p>
          )}

          {/* Skills */}
          {alumni.skills.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-protec-navy uppercase tracking-wide">
                Skills
              </h4>
              <div className="flex flex-wrap gap-1">
                {alumni.skills.slice(0, 4).map((skill) => (
                  <Badge 
                    key={skill} 
                    variant="secondary" 
                    className="text-xs bg-protec-gray text-protec-navy hover:bg-protec-navy hover:text-white transition-colors"
                  >
                    {skill}
                  </Badge>
                ))}
                {alumni.skills.length > 4 && (
                  <Badge variant="outline" className="text-xs">
                    +{alumni.skills.length - 4} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Stats and Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{alumni._count.connections}</span>
                <span className="hidden sm:inline">connections</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button 
                size="sm" 
                variant="outline"
                className="border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white"
              >
                <MessageCircle className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Connect</span>
              </Button>
              
              <Button 
                size="sm" 
                variant="ghost"
                className="text-protec-red hover:bg-protec-red hover:text-white"
                asChild
              >
                <Link href={`/profile/${alumni.id}`}>
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
