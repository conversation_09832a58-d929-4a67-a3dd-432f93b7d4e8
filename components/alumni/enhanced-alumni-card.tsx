"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  MapPin,
  Calendar,
  Users,
  MessageCircle,
  ExternalLink,
  Briefcase,
  MoreVertical,
  UserPlus,
  Star,
  Share2,
  Eye,
  Building,
  GraduationCap,
  Heart,
  CheckCircle,
  Clock,
  Globe,
  Linkedin,
  Twitter,
  Github,
  Mail
} from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

interface EnhancedAlumniCardProps {
  alumni: {
    id: string
    name: string
    photoUrl?: string | null
    bio?: string | null
    graduationYear: number
    programmes: string[]
    currentRole?: string | null
    company?: string | null
    industry?: string | null
    skills: string[]
    interests?: string[]
    province: string
    city: string
    country?: string
    socialLinks?: {
      linkedin?: string
      twitter?: string
      github?: string
      website?: string
    }
    _count: {
      connections: number
    }
    careerHistory?: Array<{
      company: string
      position: string
      isCurrent: boolean
    }>
    lastLoginAt?: Date | null
  }
  isConnected?: boolean
  connectionStatus?: 'none' | 'pending' | 'connected' | 'blocked'
  onConnect?: (alumniId: string) => void
  onMessage?: (alumniId: string) => void
  onSave?: (alumniId: string) => void
  showActions?: boolean
  variant?: 'default' | 'compact' | 'detailed'
}

export function EnhancedAlumniCard({ 
  alumni, 
  isConnected = false,
  connectionStatus = 'none',
  onConnect,
  onMessage,
  onSave,
  showActions = true,
  variant = 'default'
}: EnhancedAlumniCardProps) {
  const [isSaved, setIsSaved] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatLocation = () => {
    const parts = [alumni.city, alumni.province, alumni.country].filter(Boolean)
    return parts.join(', ') || "Location not specified"
  }

  const getConnectionButtonText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected'
      case 'pending':
        return 'Pending'
      case 'blocked':
        return 'Blocked'
      default:
        return 'Connect'
    }
  }

  const getConnectionButtonIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'blocked':
        return <ExternalLink className="h-4 w-4" />
      default:
        return <UserPlus className="h-4 w-4" />
    }
  }

  const handleConnect = () => {
    if (onConnect && connectionStatus === 'none') {
      onConnect(alumni.id)
      toast.success(`Connection request sent to ${alumni.name}`)
    }
  }

  const handleMessage = () => {
    if (onMessage) {
      onMessage(alumni.id)
    }
  }

  const handleSave = () => {
    if (onSave) {
      onSave(alumni.id)
      setIsSaved(!isSaved)
      toast.success(isSaved ? 'Removed from saved' : 'Added to saved')
    }
  }

  const handleShare = async () => {
    const url = `${window.location.origin}/profile/${alumni.id}`
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${alumni.name} - PROTEC Alumni`,
          text: `Check out ${alumni.name}'s profile on PROTEC Alumni Platform`,
          url
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      navigator.clipboard.writeText(url)
      toast.success('Profile link copied to clipboard')
    }
  }

  const isOnline = alumni.lastLoginAt && 
    new Date().getTime() - new Date(alumni.lastLoginAt).getTime() < 5 * 60 * 1000 // 5 minutes

  if (variant === 'compact') {
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        className="flex items-center space-x-3 p-3 border rounded-lg hover:shadow-md transition-all"
      >
        <div className="relative">
          <Avatar className="h-12 w-12">
            <AvatarImage src={alumni.photoUrl || undefined} alt={alumni.name} />
            <AvatarFallback className="bg-protec-navy text-white">
              {getInitials(alumni.name)}
            </AvatarFallback>
          </Avatar>
          {isOnline && (
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full" />
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <Link href={`/profile/${alumni.id}`} className="hover:text-protec-red">
            <h4 className="font-medium truncate">{alumni.name}</h4>
          </Link>
          <p className="text-sm text-muted-foreground truncate">
            {alumni.currentRole || 'PROTEC Alumni'}
          </p>
        </div>
        
        {showActions && (
          <Button
            size="sm"
            variant="ghost"
            onClick={handleConnect}
            disabled={connectionStatus !== 'none'}
          >
            {getConnectionButtonIcon()}
          </Button>
        )}
      </motion.div>
    )
  }

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -5 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className="group"
      >
        <Card className="hover:shadow-xl transition-all duration-300 border-l-4 border-l-protec-red/20 hover:border-l-protec-red overflow-hidden">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header with Avatar and Basic Info */}
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="relative">
                    <Avatar className="h-16 w-16 border-2 border-protec-gray">
                      <AvatarImage src={alumni.photoUrl || undefined} alt={alumni.name} />
                      <AvatarFallback className="bg-protec-navy text-white text-lg font-semibold">
                        {getInitials(alumni.name)}
                      </AvatarFallback>
                    </Avatar>
                    {isOnline && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Online now</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <Link 
                      href={`/profile/${alumni.id}`}
                      className="block group-hover:text-protec-red transition-colors"
                    >
                      <h3 className="text-lg font-semibold text-protec-navy truncate">
                        {alumni.name}
                      </h3>
                    </Link>
                    
                    {alumni.currentRole && (
                      <div className="flex items-center text-sm text-muted-foreground mt-1">
                        <Briefcase className="h-4 w-4 mr-1 flex-shrink-0" />
                        <span className="truncate">{alumni.currentRole}</span>
                        {alumni.company && (
                          <>
                            <span className="mx-1">at</span>
                            <Building className="h-3 w-3 mr-1" />
                            <span className="truncate font-medium">{alumni.company}</span>
                          </>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center text-sm text-muted-foreground mt-1">
                      <GraduationCap className="h-4 w-4 mr-1 flex-shrink-0" />
                      <span>Class of {alumni.graduationYear}</span>
                      {alumni.programmes.length > 0 && (
                        <span className="ml-2 text-xs">• {alumni.programmes[0]}</span>
                      )}
                    </div>
                    
                    <div className="flex items-center text-sm text-muted-foreground mt-1">
                      <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                      <span className="truncate">{formatLocation()}</span>
                    </div>
                  </div>
                </div>

                {showActions && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={handleSave}>
                        <Heart className={`h-4 w-4 mr-2 ${isSaved ? 'fill-current text-red-500' : ''}`} />
                        {isSaved ? 'Remove from saved' : 'Save profile'}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleShare}>
                        <Share2 className="h-4 w-4 mr-2" />
                        Share profile
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/profile/${alumni.id}`}>
                          <Eye className="h-4 w-4 mr-2" />
                          View full profile
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>

              {/* Bio */}
              {alumni.bio && (
                <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
                  {alumni.bio}
                </p>
              )}

              {/* Industry Badge */}
              {alumni.industry && (
                <Badge variant="outline" className="w-fit">
                  {alumni.industry}
                </Badge>
              )}

              {/* Skills */}
              {alumni.skills.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-xs font-medium text-protec-navy uppercase tracking-wide">
                    Skills
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {alumni.skills.slice(0, 4).map((skill) => (
                      <Badge 
                        key={skill} 
                        variant="secondary" 
                        className="text-xs bg-protec-gray text-protec-navy hover:bg-protec-navy hover:text-white transition-colors"
                      >
                        {skill}
                      </Badge>
                    ))}
                    {alumni.skills.length > 4 && (
                      <Badge variant="outline" className="text-xs">
                        +{alumni.skills.length - 4} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Social Links */}
              {alumni.socialLinks && Object.keys(alumni.socialLinks).length > 0 && (
                <div className="flex items-center space-x-2">
                  {alumni.socialLinks.linkedin && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" asChild>
                          <a href={alumni.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                            <Linkedin className="h-4 w-4" />
                          </a>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>LinkedIn Profile</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                  {alumni.socialLinks.twitter && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" asChild>
                          <a href={alumni.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                            <Twitter className="h-4 w-4" />
                          </a>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Twitter Profile</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                  {alumni.socialLinks.github && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" asChild>
                          <a href={alumni.socialLinks.github} target="_blank" rel="noopener noreferrer">
                            <Github className="h-4 w-4" />
                          </a>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>GitHub Profile</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                  {alumni.socialLinks.website && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" asChild>
                          <a href={alumni.socialLinks.website} target="_blank" rel="noopener noreferrer">
                            <Globe className="h-4 w-4" />
                          </a>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Personal Website</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
              )}

              {/* Stats and Actions */}
              {showActions && (
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{alumni._count.connections}</span>
                      <span className="hidden sm:inline">connections</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={handleConnect}
                      disabled={connectionStatus !== 'none'}
                      className={`border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white ${
                        connectionStatus === 'connected' ? 'bg-green-50 border-green-500 text-green-700' :
                        connectionStatus === 'pending' ? 'bg-yellow-50 border-yellow-500 text-yellow-700' : ''
                      }`}
                    >
                      {getConnectionButtonIcon()}
                      <span className="hidden sm:inline ml-1">{getConnectionButtonText()}</span>
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={handleMessage}
                      className="text-protec-red hover:bg-protec-red hover:text-white"
                    >
                      <MessageCircle className="h-4 w-4" />
                      <span className="hidden sm:inline ml-1">Message</span>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </TooltipProvider>
  )
}
