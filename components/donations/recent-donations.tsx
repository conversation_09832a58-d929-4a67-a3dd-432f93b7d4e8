"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Heart, 
  Calendar, 
  DollarSign, 
  Users,
  TrendingUp,
  Filter,
  Download,
  Eye
} from "lucide-react"

interface RecentDonationsProps {
  className?: string
}

export function RecentDonations({ className }: RecentDonationsProps) {
  // Mock data - replace with real API calls
  const recentDonations = [
    {
      id: '1',
      donor: {
        name: '<PERSON>',
        photoUrl: null,
        isAnonymous: false
      },
      amount: 5000,
      purpose: 'Student Scholarships',
      type: 'one-time',
      date: '2024-03-15T10:30:00Z',
      message: 'Happy to support the next generation of engineers!',
      isRecurring: false
    },
    {
      id: '2',
      donor: {
        name: 'Anonymous <PERSON>',
        photoUrl: null,
        isAnonymous: true
      },
      amount: 2500,
      purpose: 'Infrastructure Development',
      type: 'one-time',
      date: '2024-03-15T09:15:00Z',
      message: null,
      isRecurring: false
    },
    {
      id: '3',
      donor: {
        name: '<PERSON>',
        photoUrl: null,
        isAnonymous: false
      },
      amount: 1000,
      purpose: 'General Fund',
      type: 'recurring',
      date: '2024-03-15T08:45:00Z',
      message: 'Monthly contribution to support PROTEC programs.',
      isRecurring: true
    },
    {
      id: '4',
      donor: {
        name: '<PERSON>',
        photoUrl: null,
        isAnonymous: false
      },
      amount: 3500,
      purpose: 'Technology Access',
      type: 'one-time',
      date: '2024-03-14T16:20:00Z',
      message: 'Ensuring students have access to modern technology.',
      isRecurring: false
    },
    {
      id: '5',
      donor: {
        name: 'Emily Davis',
        photoUrl: null,
        isAnonymous: false
      },
      amount: 750,
      purpose: 'Mentorship Programs',
      type: 'recurring',
      date: '2024-03-14T14:10:00Z',
      message: 'Supporting mentorship initiatives.',
      isRecurring: true
    },
    {
      id: '6',
      donor: {
        name: 'Anonymous Donor',
        photoUrl: null,
        isAnonymous: true
      },
      amount: 10000,
      purpose: 'Emergency Student Fund',
      type: 'one-time',
      date: '2024-03-14T11:30:00Z',
      message: 'For students facing financial hardship.',
      isRecurring: false
    },
    {
      id: '7',
      donor: {
        name: 'David Wilson',
        photoUrl: null,
        isAnonymous: false
      },
      amount: 500,
      purpose: 'General Fund',
      type: 'recurring',
      date: '2024-03-14T09:00:00Z',
      message: null,
      isRecurring: true
    },
    {
      id: '8',
      donor: {
        name: 'Lisa Anderson',
        photoUrl: null,
        isAnonymous: false
      },
      amount: 2000,
      purpose: 'Student Scholarships',
      type: 'one-time',
      date: '2024-03-13T15:45:00Z',
      message: 'Proud to support deserving students.',
      isRecurring: false
    }
  ]

  const donationStats = {
    totalAmount: recentDonations.reduce((sum, donation) => sum + donation.amount, 0),
    totalDonors: new Set(recentDonations.map(d => d.donor.name)).size,
    recurringDonations: recentDonations.filter(d => d.isRecurring).length,
    averageDonation: recentDonations.reduce((sum, donation) => sum + donation.amount, 0) / recentDonations.length
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  const getPurposeColor = (purpose: string) => {
    switch (purpose) {
      case 'Student Scholarships':
        return 'bg-blue-100 text-blue-800'
      case 'Infrastructure Development':
        return 'bg-green-100 text-green-800'
      case 'Technology Access':
        return 'bg-purple-100 text-purple-800'
      case 'Mentorship Programs':
        return 'bg-orange-100 text-orange-800'
      case 'Emergency Student Fund':
        return 'bg-red-100 text-red-800'
      case 'General Fund':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Donation Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">R{donationStats.totalAmount.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Raised</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{donationStats.totalDonors}</div>
                <div className="text-sm text-muted-foreground">Unique Donors</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{donationStats.recurringDonations}</div>
                <div className="text-sm text-muted-foreground">Recurring</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Heart className="h-4 w-4 text-red-500" />
              <div>
                <div className="text-2xl font-bold">R{Math.round(donationStats.averageDonation).toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Average</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Donations List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Heart className="h-5 w-5" />
              <span>Recent Donations</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentDonations.map((donation) => (
              <div key={donation.id} className="flex items-start space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <Avatar className="h-12 w-12">
                  {!donation.donor.isAnonymous ? (
                    <>
                      <AvatarImage src={donation.donor.photoUrl || undefined} />
                      <AvatarFallback className="bg-protec-navy text-white">
                        {getInitials(donation.donor.name)}
                      </AvatarFallback>
                    </>
                  ) : (
                    <AvatarFallback className="bg-gray-400 text-white">
                      <Users className="h-6 w-6" />
                    </AvatarFallback>
                  )}
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium">
                      {donation.donor.isAnonymous ? 'Anonymous Donor' : donation.donor.name}
                    </span>
                    {donation.isRecurring && (
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                        Recurring
                      </Badge>
                    )}
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getPurposeColor(donation.purpose)}`}
                    >
                      {donation.purpose}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 mb-2">
                    <div className="flex items-center space-x-1">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-semibold text-green-600">
                        R{donation.amount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {formatTimeAgo(donation.date)}
                      </span>
                    </div>
                  </div>
                  
                  {donation.message && (
                    <p className="text-sm text-muted-foreground italic">
                      "{donation.message}"
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="ghost">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 text-center">
            <Button variant="outline">
              Load More Donations
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Donation Purposes Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Donations by Purpose</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {['Student Scholarships', 'Infrastructure Development', 'Technology Access', 'Mentorship Programs', 'Emergency Student Fund', 'General Fund'].map((purpose) => {
              const purposeDonations = recentDonations.filter(d => d.purpose === purpose)
              const totalAmount = purposeDonations.reduce((sum, d) => sum + d.amount, 0)
              const percentage = (totalAmount / donationStats.totalAmount) * 100
              
              if (totalAmount === 0) return null
              
              return (
                <div key={purpose} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{purpose}</span>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getPurposeColor(purpose)}`}
                      >
                        {purposeDonations.length} donations
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      R{totalAmount.toLocaleString()} ({percentage.toFixed(1)}%)
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-protec-red h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Thank You Message */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-protec-red rounded-full flex items-center justify-center mx-auto">
              <Heart className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Thank You to Our Donors!</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Every donation, no matter the size, makes a meaningful difference in the lives of our students. 
              Your generosity helps us continue our mission of providing quality education and opportunities 
              for the next generation of leaders.
            </p>
            <div className="flex justify-center space-x-4">
              <Button>
                <Heart className="h-4 w-4 mr-2" />
                Make a Donation
              </Button>
              <Button variant="outline">
                Learn More About Impact
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
