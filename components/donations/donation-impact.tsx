"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Heart, 
  Users, 
  GraduationCap, 
  Building,
  TrendingUp,
  Award,
  Target,
  Calendar
} from "lucide-react"

interface DonationImpactProps {
  className?: string
}

export function DonationImpact({ className }: DonationImpactProps) {
  // Mock data - replace with real API calls
  const impactStats = {
    totalDonated: 125000,
    studentsSupported: 45,
    scholarshipsAwarded: 12,
    programsSupported: 8,
    donationGrowth: 18.5
  }

  const impactAreas = [
    {
      title: 'Student Scholarships',
      description: 'Supporting deserving students with financial assistance',
      raised: 75000,
      target: 100000,
      beneficiaries: 25,
      icon: GraduationCap,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Infrastructure Development',
      description: 'Improving facilities and learning environments',
      raised: 30000,
      target: 50000,
      beneficiaries: 500,
      icon: Building,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Mentorship Programs',
      description: 'Connecting students with industry professionals',
      raised: 15000,
      target: 25000,
      beneficiaries: 80,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Technology Access',
      description: 'Providing computers and internet access',
      raised: 5000,
      target: 20000,
      beneficiaries: 35,
      icon: Award,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ]

  const recentImpacts = [
    {
      title: 'New Computer Lab Opened',
      description: 'Thanks to donations, we opened a new computer lab serving 50 students',
      date: '2024-03-10',
      amount: 25000,
      beneficiaries: 50
    },
    {
      title: 'Scholarship Recipients Announced',
      description: '5 new scholarship recipients selected for the 2024 academic year',
      date: '2024-03-05',
      amount: 15000,
      beneficiaries: 5
    },
    {
      title: 'Mentorship Program Launch',
      description: 'New mentorship program connecting 20 students with alumni',
      date: '2024-02-28',
      amount: 8000,
      beneficiaries: 20
    }
  ]

  const donationGoals = [
    {
      title: 'Emergency Student Fund',
      description: 'Support students facing financial hardship',
      target: 50000,
      raised: 32000,
      deadline: '2024-06-30',
      urgency: 'high'
    },
    {
      title: 'Library Expansion',
      description: 'Expand library resources and study spaces',
      target: 75000,
      raised: 45000,
      deadline: '2024-12-31',
      urgency: 'medium'
    },
    {
      title: 'STEM Equipment Fund',
      description: 'Purchase modern equipment for science labs',
      target: 100000,
      raised: 25000,
      deadline: '2024-09-30',
      urgency: 'low'
    }
  ]

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getDaysUntilDeadline = (deadline: string) => {
    const target = new Date(deadline)
    const today = new Date()
    const diffTime = target.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Impact Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Heart className="h-4 w-4 text-red-500" />
              <div>
                <div className="text-2xl font-bold">R{impactStats.totalDonated.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Raised</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{impactStats.studentsSupported}</div>
                <div className="text-sm text-muted-foreground">Students Supported</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <GraduationCap className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{impactStats.scholarshipsAwarded}</div>
                <div className="text-sm text-muted-foreground">Scholarships</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Building className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{impactStats.programsSupported}</div>
                <div className="text-sm text-muted-foreground">Programs</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">+{impactStats.donationGrowth}%</div>
                <div className="text-sm text-muted-foreground">Growth</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Impact Areas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Impact Areas</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {impactAreas.map((area) => {
              const IconComponent = area.icon
              const progressPercentage = (area.raised / area.target) * 100
              
              return (
                <div key={area.title} className={`p-6 rounded-lg ${area.bgColor}`}>
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg bg-white ${area.color}`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">{area.title}</h3>
                      <p className="text-sm text-muted-foreground mb-4">{area.description}</p>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            R{area.raised.toLocaleString()} raised
                          </span>
                          <span className="text-sm text-muted-foreground">
                            R{area.target.toLocaleString()} goal
                          </span>
                        </div>
                        <Progress value={progressPercentage} className="h-2" />
                        <div className="text-sm text-muted-foreground">
                          {area.beneficiaries} beneficiaries • {Math.round(progressPercentage)}% complete
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Impact Stories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Recent Impact Stories</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentImpacts.map((impact, index) => (
              <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg">
                <div className="w-12 h-12 bg-protec-red rounded-lg flex items-center justify-center">
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold mb-1">{impact.title}</h4>
                  <p className="text-sm text-muted-foreground mb-2">{impact.description}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(impact.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Heart className="h-4 w-4 text-red-500" />
                      <span>R{impact.amount.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{impact.beneficiaries} beneficiaries</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Donation Goals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Current Donation Goals</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {donationGoals.map((goal, index) => {
              const progressPercentage = (goal.raised / goal.target) * 100
              const daysLeft = getDaysUntilDeadline(goal.deadline)
              
              return (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-semibold">{goal.title}</h4>
                      <p className="text-sm text-muted-foreground">{goal.description}</p>
                    </div>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getUrgencyColor(goal.urgency)}`}
                    >
                      {goal.urgency} priority
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        R{goal.raised.toLocaleString()} raised
                      </span>
                      <span className="text-sm text-muted-foreground">
                        R{goal.target.toLocaleString()} goal
                      </span>
                    </div>
                    <Progress value={progressPercentage} className="h-2" />
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>{Math.round(progressPercentage)}% complete</span>
                      <span>
                        {daysLeft > 0 
                          ? `${daysLeft} days left`
                          : daysLeft === 0 
                            ? 'Deadline today'
                            : `${Math.abs(daysLeft)} days overdue`
                        }
                      </span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Impact Testimonial */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-protec-red rounded-full flex items-center justify-center mx-auto">
              <Heart className="h-8 w-8 text-white" />
            </div>
            <blockquote className="text-lg italic text-muted-foreground">
              "Thanks to the scholarship I received, I was able to complete my engineering degree 
              and now work at a leading tech company. The support from PROTEC alumni changed my life."
            </blockquote>
            <div className="text-sm">
              <div className="font-semibold">Thabo Mthembu</div>
              <div className="text-muted-foreground">Scholarship Recipient, Class of 2023</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
