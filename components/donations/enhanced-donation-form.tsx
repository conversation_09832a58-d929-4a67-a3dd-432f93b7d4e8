"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { api } from "@/components/providers/trpc-provider"
import { 
  Heart, 
  CreditCard, 
  Smartphone, 
  Loader2, 
  Shield, 
  ArrowLeft, 
  ArrowRight,
  Check,
  DollarSign,
  Repeat,
  Globe,
  Zap
} from "lucide-react"
import { toast } from "sonner"

const donationSchema = z.object({
  amount: z.number().min(10, "Minimum donation is R10").max(100000, "Maximum donation is R100,000"),
  currency: z.enum(['ZAR', 'USD']).default('ZAR'),
  gateway: z.enum(['payfast', 'paypal']),
  purpose: z.enum(['general', 'scholarship', 'infrastructure', 'events', 'mentorship']).default('general'),
  frequency: z.enum(['ONE_TIME', 'MONTHLY', 'QUARTERLY', 'ANNUALLY']).default('ONE_TIME'),
  isRecurring: z.boolean().default(false),
  agreeToTerms: z.boolean().refine(val => val === true, "You must agree to the terms"),
})

type DonationFormData = z.infer<typeof donationSchema>

const steps = [
  { id: 1, title: "Amount", description: "Choose your donation amount" },
  { id: 2, title: "Purpose", description: "Select donation purpose" },
  { id: 3, title: "Payment", description: "Choose payment method" },
  { id: 4, title: "Review", description: "Confirm your donation" }
]

const predefinedAmounts = [50, 100, 250, 500, 1000, 2500]

const purposes = [
  { value: 'general', label: 'General Fund', description: 'Support overall PROTEC operations' },
  { value: 'scholarship', label: 'Scholarships', description: 'Fund student scholarships and bursaries' },
  { value: 'infrastructure', label: 'Infrastructure', description: 'Improve facilities and equipment' },
  { value: 'events', label: 'Events', description: 'Support alumni events and networking' },
  { value: 'mentorship', label: 'Mentorship', description: 'Fund mentorship programs' }
]

export function EnhancedDonationForm() {
  const { data: session } = useSession()
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [isCustomAmount, setIsCustomAmount] = useState(false)

  const createDonationMutation = api.donations.create.useMutation()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid }
  } = useForm<DonationFormData>({
    resolver: zodResolver(donationSchema),
    defaultValues: {
      currency: 'ZAR',
      gateway: 'payfast',
      purpose: 'general',
      frequency: 'ONE_TIME',
      isRecurring: false,
      agreeToTerms: false
    },
    mode: 'onChange'
  })

  const watchedValues = watch()
  const progress = (currentStep / steps.length) * 100

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount)
    setIsCustomAmount(false)
    setValue('amount', amount, { shouldValidate: true })
  }

  const handleCustomAmount = () => {
    setIsCustomAmount(true)
    setSelectedAmount(null)
  }

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const onSubmit = async (data: DonationFormData) => {
    if (!session) {
      toast.error("Please sign in to make a donation")
      return
    }

    try {
      const donation = await createDonationMutation.mutateAsync(data)
      
      if (donation.paymentUrl) {
        toast.success("Redirecting to payment...")
        window.location.href = donation.paymentUrl
      } else {
        toast.error("Payment URL not received")
      }
      
    } catch (error) {
      console.error("Donation error:", error)
      toast.error("Failed to process donation. Please try again.")
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return watchedValues.amount && watchedValues.amount >= 10
      case 2:
        return watchedValues.purpose
      case 3:
        return watchedValues.gateway
      case 4:
        return watchedValues.agreeToTerms
      default:
        return false
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-protec-navy">
          Make a Donation
        </CardTitle>
        <div className="space-y-4">
          <Progress value={progress} className="w-full" />
          <div className="flex justify-between text-sm text-muted-foreground">
            {steps.map((step) => (
              <div
                key={step.id}
                className={`flex flex-col items-center ${
                  step.id <= currentStep ? 'text-protec-red' : 'text-muted-foreground'
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                    step.id < currentStep
                      ? 'bg-protec-red text-white'
                      : step.id === currentStep
                      ? 'bg-protec-red text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {step.id < currentStep ? <Check className="w-4 h-4" /> : step.id}
                </div>
                <span className="mt-1 text-xs">{step.title}</span>
              </div>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <AnimatePresence mode="wait">
            {/* Step 1: Amount Selection */}
            {currentStep === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Choose Your Donation Amount</h3>
                  <p className="text-muted-foreground">Every contribution makes a difference</p>
                </div>

                {/* Currency Selection */}
                <div className="space-y-2">
                  <Label>Currency</Label>
                  <RadioGroup
                    value={watchedValues.currency}
                    onValueChange={(value) => setValue('currency', value as 'ZAR' | 'USD', { shouldValidate: true })}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="ZAR" id="zar" />
                      <Label htmlFor="zar" className="flex items-center space-x-2">
                        <span>🇿🇦</span>
                        <span>ZAR (South African Rand)</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="USD" id="usd" />
                      <Label htmlFor="usd" className="flex items-center space-x-2">
                        <Globe className="w-4 h-4" />
                        <span>USD (US Dollar)</span>
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                {/* Predefined Amounts */}
                <div className="grid grid-cols-3 gap-3">
                  {predefinedAmounts.map((amount) => (
                    <Button
                      key={amount}
                      type="button"
                      variant={selectedAmount === amount ? "default" : "outline"}
                      className={`h-16 ${
                        selectedAmount === amount
                          ? 'bg-protec-red hover:bg-protec-red/90'
                          : 'hover:border-protec-red'
                      }`}
                      onClick={() => handleAmountSelect(amount)}
                    >
                      <div className="text-center">
                        <div className="font-bold">
                          {watchedValues.currency === 'ZAR' ? 'R' : '$'}{amount}
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>

                {/* Custom Amount */}
                <div className="space-y-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={handleCustomAmount}
                  >
                    Enter Custom Amount
                  </Button>
                  
                  {isCustomAmount && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="space-y-2"
                    >
                      <Label htmlFor="customAmount">Custom Amount</Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          id="customAmount"
                          type="number"
                          placeholder="Enter amount"
                          className="pl-10"
                          {...register('amount', { valueAsNumber: true })}
                        />
                      </div>
                      {errors.amount && (
                        <p className="text-sm text-red-500">{errors.amount.message}</p>
                      )}
                    </motion.div>
                  )}
                </div>

                {/* Recurring Option */}
                <div className="flex items-center space-x-2 p-4 bg-blue-50 rounded-lg">
                  <Checkbox
                    id="recurring"
                    checked={watchedValues.isRecurring}
                    onCheckedChange={(checked) => {
                      setValue('isRecurring', checked as boolean)
                      if (!checked) {
                        setValue('frequency', 'ONE_TIME')
                      }
                    }}
                  />
                  <Label htmlFor="recurring" className="flex items-center space-x-2">
                    <Repeat className="w-4 h-4" />
                    <span>Make this a recurring donation</span>
                  </Label>
                </div>

                {watchedValues.isRecurring && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="space-y-2"
                  >
                    <Label>Frequency</Label>
                    <Select
                      value={watchedValues.frequency}
                      onValueChange={(value) => setValue('frequency', value as any)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MONTHLY">Monthly</SelectItem>
                        <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                        <SelectItem value="ANNUALLY">Annually</SelectItem>
                      </SelectContent>
                    </Select>
                  </motion.div>
                )}
              </motion.div>
            )}

            {/* Step 2: Purpose Selection */}
            {currentStep === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Choose Donation Purpose</h3>
                  <p className="text-muted-foreground">Where would you like your donation to make an impact?</p>
                </div>

                <div className="space-y-3">
                  {purposes.map((purpose) => (
                    <div
                      key={purpose.value}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        watchedValues.purpose === purpose.value
                          ? 'border-protec-red bg-red-50'
                          : 'border-gray-200 hover:border-protec-red'
                      }`}
                      onClick={() => setValue('purpose', purpose.value as any)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{purpose.label}</h4>
                          <p className="text-sm text-muted-foreground">{purpose.description}</p>
                        </div>
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          watchedValues.purpose === purpose.value
                            ? 'border-protec-red bg-protec-red'
                            : 'border-gray-300'
                        }`}>
                          {watchedValues.purpose === purpose.value && (
                            <Check className="w-2 h-2 text-white m-0.5" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Step 3: Payment Method Selection */}
            {currentStep === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Choose Payment Method</h3>
                  <p className="text-muted-foreground">Select your preferred payment gateway</p>
                </div>

                <div className="space-y-4">
                  {/* PayFast Option */}
                  <div
                    className={`p-6 border rounded-lg cursor-pointer transition-all ${
                      watchedValues.gateway === 'payfast'
                        ? 'border-protec-red bg-red-50'
                        : 'border-gray-200 hover:border-protec-red'
                    } ${watchedValues.currency === 'USD' ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => {
                      if (watchedValues.currency === 'ZAR') {
                        setValue('gateway', 'payfast')
                      }
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                          <CreditCard className="w-6 h-6 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">PayFast</h4>
                          <p className="text-sm text-muted-foreground">
                            South African payment gateway - ZAR only
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="secondary" className="text-xs">Visa</Badge>
                            <Badge variant="secondary" className="text-xs">Mastercard</Badge>
                            <Badge variant="secondary" className="text-xs">EFT</Badge>
                          </div>
                        </div>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        watchedValues.gateway === 'payfast'
                          ? 'border-protec-red bg-protec-red'
                          : 'border-gray-300'
                      }`}>
                        {watchedValues.gateway === 'payfast' && (
                          <Check className="w-2 h-2 text-white m-0.5" />
                        )}
                      </div>
                    </div>
                    {watchedValues.currency === 'USD' && (
                      <p className="text-xs text-red-500 mt-2">
                        PayFast only supports ZAR payments
                      </p>
                    )}
                  </div>

                  {/* PayPal Option */}
                  <div
                    className={`p-6 border rounded-lg cursor-pointer transition-all ${
                      watchedValues.gateway === 'paypal'
                        ? 'border-protec-red bg-red-50'
                        : 'border-gray-200 hover:border-protec-red'
                    }`}
                    onClick={() => setValue('gateway', 'paypal')}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Globe className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">PayPal</h4>
                          <p className="text-sm text-muted-foreground">
                            International payments - USD and ZAR
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="secondary" className="text-xs">PayPal</Badge>
                            <Badge variant="secondary" className="text-xs">Credit Card</Badge>
                            <Badge variant="secondary" className="text-xs">Debit Card</Badge>
                          </div>
                        </div>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        watchedValues.gateway === 'paypal'
                          ? 'border-protec-red bg-protec-red'
                          : 'border-gray-300'
                      }`}>
                        {watchedValues.gateway === 'paypal' && (
                          <Check className="w-2 h-2 text-white m-0.5" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Security Notice */}
                <div className="flex items-center space-x-2 p-4 bg-green-50 rounded-lg">
                  <Shield className="w-5 h-5 text-green-600" />
                  <div className="text-sm">
                    <p className="font-medium text-green-800">Secure Payment</p>
                    <p className="text-green-600">Your payment information is encrypted and secure</p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Step 4: Review and Confirm */}
            {currentStep === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Review Your Donation</h3>
                  <p className="text-muted-foreground">Please confirm your donation details</p>
                </div>

                {/* Donation Summary */}
                <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Amount:</span>
                    <span className="text-xl font-bold text-protec-red">
                      {watchedValues.currency === 'ZAR' ? 'R' : '$'}{watchedValues.amount}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="font-medium">Purpose:</span>
                    <span>{purposes.find(p => p.value === watchedValues.purpose)?.label}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="font-medium">Payment Method:</span>
                    <span className="capitalize">{watchedValues.gateway}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="font-medium">Frequency:</span>
                    <span>
                      {watchedValues.isRecurring
                        ? watchedValues.frequency.toLowerCase().replace('_', ' ')
                        : 'One-time'
                      }
                    </span>
                  </div>

                  {watchedValues.isRecurring && (
                    <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded">
                      <Repeat className="w-4 h-4 text-blue-600" />
                      <span className="text-sm text-blue-800">
                        This will be a recurring donation charged {watchedValues.frequency.toLowerCase()}
                      </span>
                    </div>
                  )}
                </div>

                {/* Terms and Conditions */}
                <div className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={watchedValues.agreeToTerms}
                      onCheckedChange={(checked) => setValue('agreeToTerms', checked as boolean)}
                    />
                    <Label htmlFor="terms" className="text-sm leading-relaxed">
                      I agree to the{' '}
                      <a href="/terms" className="text-protec-red hover:underline" target="_blank">
                        Terms and Conditions
                      </a>{' '}
                      and{' '}
                      <a href="/privacy" className="text-protec-red hover:underline" target="_blank">
                        Privacy Policy
                      </a>
                      . I understand that my donation will be processed securely.
                    </Label>
                  </div>

                  {watchedValues.isRecurring && (
                    <div className="text-xs text-muted-foreground p-3 bg-yellow-50 rounded">
                      <p>
                        <strong>Recurring Donation Notice:</strong> You can cancel your recurring donation
                        at any time through your donor dashboard or by contacting us directly.
                      </p>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Previous</span>
            </Button>

            {currentStep < steps.length ? (
              <Button
                type="button"
                onClick={nextStep}
                disabled={!canProceed()}
                className="flex items-center space-x-2 bg-protec-red hover:bg-protec-red/90"
              >
                <span>Next</span>
                <ArrowRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={!session || createDonationMutation.isLoading || !canProceed()}
                className="flex items-center space-x-2 bg-protec-red hover:bg-protec-red/90"
              >
                {createDonationMutation.isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <Heart className="w-4 h-4" />
                    <span>Donate Now</span>
                  </>
                )}
              </Button>
            )}
          </div>

          {!session && (
            <p className="text-center text-sm text-muted-foreground">
              Please <a href="/auth/signin" className="text-protec-red hover:underline">sign in</a> to make a donation
            </p>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
