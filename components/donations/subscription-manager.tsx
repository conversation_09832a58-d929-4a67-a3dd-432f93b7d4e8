"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { api } from "@/components/providers/trpc-provider"
import { 
  Calendar, 
  CreditCard, 
  Pause, 
  Play, 
  Settings, 
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Repeat,
  X
} from "lucide-react"
import { toast } from "sonner"

interface SubscriptionManagerProps {
  className?: string
}

export function SubscriptionManager({ className }: SubscriptionManagerProps) {
  const { data: session } = useSession()
  const [selectedSubscription, setSelectedSubscription] = useState<string | null>(null)

  const { data: subscriptions, isLoading, refetch } = api.donations.getMyDonations.useQuery(
    undefined,
    { 
      enabled: !!session,
      select: (data) => data.filter(donation => donation.isRecurring)
    }
  )

  const updateSubscriptionMutation = api.donations.updateSubscription.useMutation({
    onSuccess: () => {
      toast.success("Subscription updated successfully")
      refetch()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update subscription")
    }
  })

  const cancelSubscriptionMutation = api.donations.cancelSubscription.useMutation({
    onSuccess: () => {
      toast.success("Subscription cancelled successfully")
      refetch()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to cancel subscription")
    }
  })

  const handlePauseResume = async (subscriptionId: string, isPaused: boolean) => {
    try {
      await updateSubscriptionMutation.mutateAsync({
        id: subscriptionId,
        status: isPaused ? 'paused' : 'active'
      })
    } catch (error) {
      console.error('Error updating subscription:', error)
    }
  }

  const handleFrequencyChange = async (subscriptionId: string, frequency: string) => {
    try {
      await updateSubscriptionMutation.mutateAsync({
        id: subscriptionId,
        frequency: frequency as any
      })
    } catch (error) {
      console.error('Error updating frequency:', error)
    }
  }

  const handleCancel = async (subscriptionId: string) => {
    if (window.confirm('Are you sure you want to cancel this subscription? This action cannot be undone.')) {
      try {
        await cancelSubscriptionMutation.mutateAsync({ id: subscriptionId })
      } catch (error) {
        console.error('Error cancelling subscription:', error)
      }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'paused':
        return <Pause className="w-4 h-4 text-yellow-500" />
      case 'cancelled':
        return <X className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="pt-6 text-center">
          <p className="text-muted-foreground">Please sign in to manage your subscriptions</p>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!subscriptions || subscriptions.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Repeat className="w-5 h-5" />
            <span>Recurring Donations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Repeat className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Recurring Donations</h3>
            <p className="text-muted-foreground mb-4">
              You don't have any active recurring donations yet.
            </p>
            <Button asChild className="bg-protec-red hover:bg-protec-red/90">
              <a href="/donations">Set Up Recurring Donation</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Repeat className="w-5 h-5" />
          <span>Recurring Donations</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {subscriptions.map((subscription) => (
          <motion.div
            key={subscription.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="border rounded-lg p-6 space-y-4"
          >
            {/* Subscription Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-protec-red/10 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-protec-red" />
                </div>
                <div>
                  <h4 className="font-medium">
                    R{subscription.amountZAR.toLocaleString()} {subscription.frequency.toLowerCase()}
                  </h4>
                  <p className="text-sm text-muted-foreground capitalize">
                    {subscription.purpose} donation
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusIcon(subscription.status)}
                <Badge className={getStatusColor(subscription.status)}>
                  {subscription.status}
                </Badge>
              </div>
            </div>

            {/* Subscription Details */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Next Payment</p>
                <p className="font-medium">
                  {subscription.nextPaymentDate 
                    ? new Date(subscription.nextPaymentDate).toLocaleDateString()
                    : 'Not scheduled'
                  }
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Payment Method</p>
                <div className="flex items-center space-x-1">
                  <CreditCard className="w-3 h-3" />
                  <span className="capitalize">{subscription.gateway}</span>
                </div>
              </div>
            </div>

            {/* Subscription Controls */}
            {subscription.status !== 'cancelled' && (
              <div className="space-y-4 pt-4 border-t">
                {/* Pause/Resume */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor={`pause-${subscription.id}`}>
                      {subscription.status === 'paused' ? 'Resume' : 'Pause'} subscription
                    </Label>
                  </div>
                  <Switch
                    id={`pause-${subscription.id}`}
                    checked={subscription.status === 'active'}
                    onCheckedChange={(checked) => 
                      handlePauseResume(subscription.id, !checked)
                    }
                    disabled={updateSubscriptionMutation.isLoading}
                  />
                </div>

                {/* Frequency Change */}
                <div className="flex items-center justify-between">
                  <Label>Frequency</Label>
                  <Select
                    value={subscription.frequency}
                    onValueChange={(value) => 
                      handleFrequencyChange(subscription.id, value)
                    }
                    disabled={updateSubscriptionMutation.isLoading}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MONTHLY">Monthly</SelectItem>
                      <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                      <SelectItem value="ANNUALLY">Annually</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Cancel Button */}
                <div className="pt-2">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleCancel(subscription.id)}
                    disabled={cancelSubscriptionMutation.isLoading}
                    className="w-full"
                  >
                    {cancelSubscriptionMutation.isLoading ? (
                      <>
                        <Clock className="w-4 h-4 mr-2 animate-spin" />
                        Cancelling...
                      </>
                    ) : (
                      <>
                        <X className="w-4 h-4 mr-2" />
                        Cancel Subscription
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Failed Payment Notice */}
            {subscription.failedAttempts > 0 && subscription.status === 'active' && (
              <div className="flex items-center space-x-2 p-3 bg-yellow-50 rounded-lg">
                <AlertCircle className="w-4 h-4 text-yellow-600" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">Payment Issue</p>
                  <p className="text-yellow-600">
                    {subscription.failedAttempts} failed attempt(s). Please update your payment method.
                  </p>
                </div>
              </div>
            )}
          </motion.div>
        ))}

        {/* Add New Subscription */}
        <div className="text-center pt-4 border-t">
          <Button asChild variant="outline" className="w-full">
            <a href="/donations">
              <Repeat className="w-4 h-4 mr-2" />
              Set Up New Recurring Donation
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
