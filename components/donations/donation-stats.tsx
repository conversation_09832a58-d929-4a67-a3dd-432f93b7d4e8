"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import { 
  Heart, 
  Target, 
  TrendingUp, 
  Users,
  Calendar,
  DollarSign
} from "lucide-react"

export function DonationStats() {
  const { data: stats, isLoading } = api.donations.getStats.useQuery()

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-20 mb-2" />
              <Skeleton className="h-4 w-16" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const displayStats = stats || {
    totalDonations: 0,
    totalAmount: 0,
    recentDonations: 0,
    topDonors: []
  }

  // Annual goal (could be configurable)
  const annualGoal = 3000000
  const progressPercentage = (displayStats.totalAmount / annualGoal) * 100

  return (
    <div className="space-y-8">
      {/* Main Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-protec-red">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Raised</CardTitle>
            <DollarSign className="h-4 w-4 text-protec-red" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              R{displayStats.totalAmount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              From {displayStats.totalDonations} donations
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Donors</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              {displayStats.totalDonations}
            </div>
            <p className="text-xs text-muted-foreground">
              Alumni supporters
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              {displayStats.recentDonations}
            </div>
            <p className="text-xs text-muted-foreground">
              New donations
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Gift</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              R{Math.round(displayStats.totalAmount / displayStats.totalDonations).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Per donation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Annual Goal Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="text-protec-navy flex items-center">
            <Target className="mr-2 h-5 w-5" />
            2024 Annual Goal Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              R{displayStats.totalAmount.toLocaleString()} raised
            </span>
            <span className="text-sm text-muted-foreground">
              Goal: R{annualGoal.toLocaleString()}
            </span>
          </div>
          
          <Progress value={progressPercentage} className="h-3" />
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-protec-red font-medium">
              {progressPercentage.toFixed(1)}% complete
            </span>
            <span className="text-muted-foreground">
              R{(annualGoal - displayStats.totalAmount).toLocaleString()} remaining
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-lg font-bold text-protec-navy">R500K</div>
              <div className="text-xs text-muted-foreground">Scholarships</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-protec-navy">R300K</div>
              <div className="text-xs text-muted-foreground">Infrastructure</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-protec-navy">R200K</div>
              <div className="text-xs text-muted-foreground">Programs</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Impact Metrics */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="text-center">
          <CardContent className="p-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-protec-red/10 rounded-full flex items-center justify-center">
              <Heart className="h-8 w-8 text-protec-red" />
            </div>
            <div className="text-2xl font-bold text-protec-navy mb-2">150</div>
            <div className="text-sm text-muted-foreground">
              Scholarships Awarded This Year
            </div>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="p-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-protec-navy mb-2">2,500</div>
            <div className="text-sm text-muted-foreground">
              Students Supported Since 1982
            </div>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="p-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-protec-navy mb-2">85%</div>
            <div className="text-sm text-muted-foreground">
              Graduate Employment Rate
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
