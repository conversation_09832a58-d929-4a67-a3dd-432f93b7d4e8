import { Card, CardContent } from "@/components/ui/card"

const stats = [
  {
    value: "40+",
    label: "Years of Excellence",
    description: "Empowering STEM careers since 1982"
  },
  {
    value: "5,000+",
    label: "Alumni Network",
    description: "Graduates making impact worldwide"
  },
  {
    value: "85%",
    label: "Employment Rate",
    description: "Within 6 months of graduation"
  },
  {
    value: "R2M+",
    label: "Donated Back",
    description: "Supporting future generations"
  },
  {
    value: "200+",
    label: "Annual Events",
    description: "Networking and professional development"
  },
  {
    value: "50+",
    label: "Countries",
    description: "Global alumni presence"
  }
]

export function LandingStats() {
  return (
    <section className="bg-protec-gray py-20 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
            Impact by the numbers
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Four decades of transforming lives and building careers in STEM fields across South Africa and beyond.
          </p>
        </div>

        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {stats.map((stat, index) => (
            <Card key={stat.label} className="group relative overflow-hidden border-0 bg-white shadow-sm transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
              <CardContent className="p-8 text-center">
                <div className="text-4xl font-bold text-protec-red sm:text-5xl">
                  {stat.value}
                </div>
                <div className="mt-2 text-xl font-semibold text-protec-navy">
                  {stat.label}
                </div>
                <div className="mt-2 text-sm text-gray-600">
                  {stat.description}
                </div>
              </CardContent>
              
              {/* Animated background on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-protec-navy/5 to-protec-red/5 opacity-0 transition-opacity group-hover:opacity-100" />
            </Card>
          ))}
        </div>

        {/* Call to action */}
        <div className="mt-16 text-center">
          <p className="text-lg text-gray-600">
            Ready to be part of this incredible community?
          </p>
        </div>
      </div>
    </section>
  )
}
