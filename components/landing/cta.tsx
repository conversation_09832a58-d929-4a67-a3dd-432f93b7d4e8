import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Users, Calendar, Heart } from "lucide-react"

export function LandingCTA() {
  return (
    <section className="relative overflow-hidden bg-protec-navy py-20 sm:py-32">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Ready to reconnect with your PROTEC family?
          </h2>
          <p className="mt-6 text-lg leading-8 text-blue-100">
            Join thousands of alumni who are already networking, growing their careers, 
            and giving back to the next generation of STEM leaders.
          </p>

          {/* CTA Buttons */}
          <div className="mt-10 flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-6">
            <Button 
              size="lg" 
              className="bg-protec-red hover:bg-protec-red/90 text-white px-8 py-3 text-lg w-full sm:w-auto"
              asChild
            >
              <Link href="/auth/signup">
                Join the Network
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-white text-white hover:bg-white hover:text-protec-navy px-8 py-3 text-lg w-full sm:w-auto"
              asChild
            >
              <Link href="/auth/signin">
                Sign In
              </Link>
            </Button>
          </div>

          {/* Quick benefits */}
          <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3">
            <div className="flex flex-col items-center text-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/10">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="mt-4 text-lg font-semibold text-white">
                Connect
              </h3>
              <p className="mt-2 text-sm text-blue-100">
                Find and connect with alumni in your field or location
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/10">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <h3 className="mt-4 text-lg font-semibold text-white">
                Engage
              </h3>
              <p className="mt-2 text-sm text-blue-100">
                Attend exclusive events and networking opportunities
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/10">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <h3 className="mt-4 text-lg font-semibold text-white">
                Give Back
              </h3>
              <p className="mt-2 text-sm text-blue-100">
                Support current students and future generations
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
