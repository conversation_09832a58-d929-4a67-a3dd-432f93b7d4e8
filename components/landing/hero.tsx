"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Users, Calendar, Heart, Briefcase, Play, Star, Award, Globe } from "lucide-react"

export function LandingHero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-red-50/30 py-20 sm:py-32">
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-protec-red/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute top-40 right-20 w-32 h-32 bg-protec-navy/10 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-protec-red/10 rounded-full blur-xl animate-pulse delay-500" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          {/* Enhanced Badge */}
          <Badge variant="outline" className="mb-6 border-protec-red/20 bg-protec-red/5 text-protec-red animate-fade-in">
            <Award className="mr-2 h-4 w-4" />
            40 Years of STEM Excellence Since 1982
          </Badge>

          {/* Enhanced Heading with Animation */}
          <h1 className="text-4xl font-bold tracking-tight text-protec-navy sm:text-6xl lg:text-7xl animate-fade-in-up">
            Connect with{" "}
            <span className="relative text-protec-red">
              PROTEC
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-protec-red to-protec-navy rounded-full" />
            </span>{" "}
            Alumni Worldwide
          </h1>

          {/* Enhanced Description */}
          <p className="mt-6 text-xl leading-8 text-gray-600 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Join thousands of PROTEC alumni building successful STEM careers. 
            Network, discover opportunities, and give back to the next generation 
            of South African innovators.
          </p>

          {/* Trust Indicators */}
          <div className="mt-8 flex items-center justify-center gap-6 text-sm text-gray-500 animate-fade-in-up delay-300">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span>Trusted by 5,000+ alumni</span>
            </div>
            <div className="flex items-center gap-1">
              <Globe className="h-4 w-4 text-protec-navy" />
              <span>50+ countries</span>
            </div>
          </div>

          {/* Enhanced CTA Buttons */}
          <div className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 animate-fade-in-up delay-400">
            <Button 
              size="lg" 
              className="bg-protec-red hover:bg-protec-red/90 text-white px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full sm:w-auto"
              asChild
            >
              <Link href="/auth/signup">
                Join the Network
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full sm:w-auto"
              asChild
            >
              <Link href="/directory">
                Explore Alumni
              </Link>
            </Button>
            <Button 
              variant="ghost" 
              size="lg"
              className="text-protec-navy hover:text-protec-red px-6 py-4 text-lg group w-full sm:w-auto"
              asChild
            >
              <Link href="#demo">
                <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                Watch Demo
              </Link>
            </Button>
          </div>

          {/* Enhanced Stats with Animation */}
          <div className="mt-20 grid grid-cols-2 gap-8 sm:grid-cols-4 animate-fade-in-up delay-500">
            <div className="flex flex-col items-center group cursor-pointer">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-protec-navy/10 group-hover:bg-protec-navy/20 transition-all duration-300 group-hover:scale-110">
                <Users className="h-8 w-8 text-protec-navy" />
              </div>
              <div className="mt-4 text-3xl font-bold text-protec-navy">5,000+</div>
              <div className="text-sm text-gray-600 font-medium">Alumni Network</div>
            </div>
            <div className="flex flex-col items-center group cursor-pointer">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-protec-red/10 group-hover:bg-protec-red/20 transition-all duration-300 group-hover:scale-110">
                <Briefcase className="h-8 w-8 text-protec-red" />
              </div>
              <div className="mt-4 text-3xl font-bold text-protec-navy">85%</div>
              <div className="text-sm text-gray-600 font-medium">Employment Rate</div>
            </div>
            <div className="flex flex-col items-center group cursor-pointer">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-protec-navy/10 group-hover:bg-protec-navy/20 transition-all duration-300 group-hover:scale-110">
                <Calendar className="h-8 w-8 text-protec-navy" />
              </div>
              <div className="mt-4 text-3xl font-bold text-protec-navy">200+</div>
              <div className="text-sm text-gray-600 font-medium">Events Yearly</div>
            </div>
            <div className="flex flex-col items-center group cursor-pointer">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-protec-red/10 group-hover:bg-protec-red/20 transition-all duration-300 group-hover:scale-110">
                <Heart className="h-8 w-8 text-protec-red" />
              </div>
              <div className="mt-4 text-3xl font-bold text-protec-navy">R2M+</div>
              <div className="text-sm text-gray-600 font-medium">Donated Back</div>
            </div>
          </div>
        </div>

        {/* Enhanced Hero Visual */}
        <div className="mt-20 flow-root sm:mt-32 animate-fade-in-up delay-600">
          <div className="relative -m-2 rounded-2xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10 lg:-m-4 lg:rounded-3xl lg:p-4 shadow-2xl">
            <div className="aspect-[16/9] rounded-xl bg-gradient-to-br from-protec-navy via-protec-navy to-protec-red p-8 shadow-2xl relative overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-grid-white/10 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
              
              {/* Content */}
              <div className="relative flex h-full items-center justify-center">
                <div className="text-center text-white">
                  <div className="text-6xl lg:text-8xl font-bold opacity-90 mb-4">PROTEC</div>
                  <div className="text-xl lg:text-2xl opacity-80 mb-6">Alumni Platform</div>
                  <div className="flex items-center justify-center gap-4 text-sm opacity-60">
                    <span>Connect</span>
                    <div className="w-2 h-2 bg-white rounded-full" />
                    <span>Grow</span>
                    <div className="w-2 h-2 bg-white rounded-full" />
                    <span>Give Back</span>
                  </div>
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute top-4 right-4 w-12 h-12 bg-white/10 rounded-full animate-pulse" />
              <div className="absolute bottom-4 left-4 w-8 h-8 bg-white/10 rounded-full animate-pulse delay-1000" />
              <div className="absolute top-1/2 left-4 w-6 h-6 bg-white/10 rounded-full animate-pulse delay-500" />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
