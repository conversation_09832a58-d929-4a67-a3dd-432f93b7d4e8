"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { HelpCircle, MessageCircle, Mail } from "lucide-react"
import Link from "next/link"

const faqs = [
  {
    question: "Who can join the PROTEC Alumni Network?",
    answer: "The network is open to all PROTEC graduates from any programme and graduation year. Whether you completed your studies in 1982 or 2024, you're part of our community and welcome to join."
  },
  {
    question: "Is there a cost to join the alumni platform?",
    answer: "No, the PROTEC Alumni Platform is completely free for all verified alumni. We believe in keeping our community connected without financial barriers."
  },
  {
    question: "How do I verify my alumni status?",
    answer: "During registration, you'll provide your graduation year and programme details. Our team will verify your information against our records. This process typically takes 1-2 business days."
  },
  {
    question: "What features are available on the platform?",
    answer: "The platform includes an alumni directory, networking tools, event listings, job board, mentorship matching, community feed, donation portal, and messaging system. All features are designed to help you stay connected and advance your career."
  },
  {
    question: "Can I update my profile information?",
    answer: "Yes, you can update your profile at any time. We encourage keeping your information current to help other alumni find and connect with you effectively."
  },
  {
    question: "How do I find alumni in my area or industry?",
    answer: "Use our advanced search filters in the alumni directory. You can search by location, industry, graduation year, programme, company, or skills to find relevant connections."
  },
  {
    question: "Are there networking events for alumni?",
    answer: "Yes! We host regular networking events, both virtual and in-person, across different regions. Check the events section for upcoming opportunities in your area."
  },
  {
    question: "How can I give back to current students?",
    answer: "There are several ways: mentor current students, donate to scholarship funds, participate in career talks, offer internships, or volunteer at PROTEC events. Visit the 'Give Back' section for more opportunities."
  },
  {
    question: "Is my personal information secure?",
    answer: "Absolutely. We take privacy seriously and use industry-standard security measures. You control what information is visible to other alumni and can adjust your privacy settings at any time."
  },
  {
    question: "How do I report inappropriate behavior or content?",
    answer: "We have a zero-tolerance policy for inappropriate behavior. Use the report function available on all content and profiles, or contact our support team directly. All reports are investigated promptly."
  }
]

const categories = [
  { name: "Getting Started", count: 4 },
  { name: "Platform Features", count: 3 },
  { name: "Privacy & Security", count: 2 },
  { name: "Community Guidelines", count: 1 }
]

export function LandingFAQ() {
  const [selectedCategory, setSelectedCategory] = useState("All")

  return (
    <section className="py-20 sm:py-32 bg-white">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto max-w-2xl text-center mb-16">
          <Badge variant="outline" className="mb-4 border-protec-navy/20 bg-protec-navy/5 text-protec-navy">
            <HelpCircle className="mr-2 h-4 w-4" />
            Support
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
            Frequently Asked Questions
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Find answers to common questions about the PROTEC Alumni Platform. 
            Can't find what you're looking for? We're here to help.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Categories Sidebar */}
          <div className="lg:col-span-1">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <h3 className="font-semibold text-protec-navy mb-4">Categories</h3>
                <div className="space-y-2">
                  <Button
                    variant={selectedCategory === "All" ? "default" : "ghost"}
                    className={`w-full justify-start ${
                      selectedCategory === "All" 
                        ? "bg-protec-navy hover:bg-protec-navy/90" 
                        : "hover:bg-protec-navy/10"
                    }`}
                    onClick={() => setSelectedCategory("All")}
                  >
                    All Questions
                    <Badge variant="secondary" className="ml-auto">
                      {faqs.length}
                    </Badge>
                  </Button>
                  {categories.map((category) => (
                    <Button
                      key={category.name}
                      variant={selectedCategory === category.name ? "default" : "ghost"}
                      className={`w-full justify-start ${
                        selectedCategory === category.name 
                          ? "bg-protec-navy hover:bg-protec-navy/90" 
                          : "hover:bg-protec-navy/10"
                      }`}
                      onClick={() => setSelectedCategory(category.name)}
                    >
                      {category.name}
                      <Badge variant="secondary" className="ml-auto">
                        {category.count}
                      </Badge>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Contact Support */}
            <Card className="border-0 shadow-lg mt-6">
              <CardContent className="p-6">
                <h3 className="font-semibold text-protec-navy mb-4">Still need help?</h3>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/contact">
                      <MessageCircle className="mr-2 h-4 w-4" />
                      Contact Support
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="mailto:<EMAIL>">
                      <Mail className="mr-2 h-4 w-4" />
                      Email Us
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* FAQ Content */}
          <div className="lg:col-span-3">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <Accordion type="single" collapsible className="w-full">
                  {faqs.map((faq, index) => (
                    <AccordionItem key={index} value={`item-${index}`}>
                      <AccordionTrigger className="text-left hover:text-protec-red transition-colors">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>

            {/* Additional Help */}
            <div className="mt-8 text-center">
              <p className="text-gray-600 mb-4">
                Didn't find the answer you were looking for?
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-protec-red hover:bg-protec-red/90" asChild>
                  <Link href="/contact">
                    Contact Support Team
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/help">
                    Browse Help Center
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}