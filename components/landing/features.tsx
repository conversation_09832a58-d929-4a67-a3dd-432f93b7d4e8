"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  Calendar, 
  MessageSquare, 
  Heart, 
  Search, 
  Briefcase,
  GraduationCap,
  Network,
  MapPin,
  ArrowRight,
  Sparkles,
  CheckCircle
} from "lucide-react"

const features = [
  {
    icon: Users,
    title: "Alumni Directory",
    description: "Connect with fellow PROTEC graduates across all programmes and graduation years. Find mentors, collaborators, and lifelong friends.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10",
    category: "networking",
    benefits: ["Advanced search filters", "Profile verification", "Contact management"]
  },
  {
    icon: Calendar,
    title: "Events & Networking",
    description: "Join exclusive alumni events, workshops, and networking sessions. Stay connected with the PROTEC community.",
    color: "text-protec-red",
    bgColor: "bg-protec-red/10",
    category: "events",
    benefits: ["Virtual & in-person events", "RSVP management", "Event networking"]
  },
  {
    icon: MessageSquare,
    title: "Community Feed",
    description: "Share achievements, ask questions, and engage with the community. Your success stories inspire others.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10",
    category: "community",
    benefits: ["Real-time updates", "Discussion threads", "Content moderation"]
  },
  {
    icon: Heart,
    title: "Give Back",
    description: "Support current students and the next generation through donations, mentorship, and scholarship programmes.",
    color: "text-protec-red",
    bgColor: "bg-protec-red/10",
    category: "giving",
    benefits: ["Secure donations", "Impact tracking", "Tax receipts"]
  },
  {
    icon: Briefcase,
    title: "Career Opportunities",
    description: "Discover job openings, internships, and career advancement opportunities shared by the alumni network.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10",
    category: "career",
    benefits: ["Job board access", "Career resources", "Industry insights"]
  },
  {
    icon: GraduationCap,
    title: "Mentorship Programme",
    description: "Connect with experienced professionals or mentor upcoming graduates. Share knowledge and grow together.",
    color: "text-protec-red",
    bgColor: "bg-protec-red/10",
    category: "mentorship",
    benefits: ["Smart matching", "Goal tracking", "Resource library"]
  }
]

const categories = [
  { id: "all", name: "All Features", icon: Sparkles },
  { id: "networking", name: "Networking", icon: Users },
  { id: "events", name: "Events", icon: Calendar },
  { id: "community", name: "Community", icon: MessageSquare },
  { id: "career", name: "Career", icon: Briefcase },
  { id: "giving", name: "Give Back", icon: Heart },
  { id: "mentorship", name: "Mentorship", icon: GraduationCap }
]

export function LandingFeatures() {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null)

  const filteredFeatures = selectedCategory === "all" 
    ? features 
    : features.filter(feature => feature.category === selectedCategory)

  return (
    <section className="py-20 sm:py-32 bg-gradient-to-b from-white to-protec-gray/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto max-w-3xl text-center mb-16">
          <Badge variant="outline" className="mb-4 border-protec-navy/20 bg-protec-navy/5 text-protec-navy">
            <Sparkles className="mr-2 h-4 w-4" />
            Platform Features
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
            Everything you need to stay connected
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Our comprehensive platform brings together all the tools and features you need to maintain 
            meaningful connections with the PROTEC alumni community and advance your career.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category) => {
            const Icon = category.icon
            return (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className={`${
                  selectedCategory === category.id 
                    ? "bg-protec-navy hover:bg-protec-navy/90" 
                    : "hover:bg-protec-navy/10"
                } transition-all duration-300`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <Icon className="mr-2 h-4 w-4" />
                {category.name}
              </Button>
            )
          })}
        </div>

        {/* Features Grid */}
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {filteredFeatures.map((feature, index) => {
              const Icon = feature.icon
              const isHovered = hoveredFeature === feature.title
              
              return (
                <Card 
                  key={feature.title} 
                  className="group relative overflow-hidden border-0 bg-white shadow-lg transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 cursor-pointer"
                  onMouseEnter={() => setHoveredFeature(feature.title)}
                  onMouseLeave={() => setHoveredFeature(null)}
                >
                  <CardHeader className="pb-4">
                    <div className={`inline-flex h-16 w-16 items-center justify-center rounded-xl ${feature.bgColor} transition-all duration-300 group-hover:scale-110 group-hover:rotate-3`}>
                      <Icon className={`h-8 w-8 ${feature.color}`} />
                    </div>
                    <CardTitle className="text-xl font-semibold text-protec-navy group-hover:text-protec-red transition-colors">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <CardDescription className="text-gray-600 leading-relaxed">
                      {feature.description}
                    </CardDescription>
                    
                    {/* Benefits List */}
                    <div className={`space-y-2 transition-all duration-300 ${
                      isHovered ? 'opacity-100 max-h-40' : 'opacity-0 max-h-0 overflow-hidden'
                    }`}>
                      {feature.benefits.map((benefit, idx) => (
                        <div key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <span>{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  
                  {/* Hover effect gradient */}
                  <div className="absolute inset-0 bg-gradient-to-br from-protec-navy/5 to-protec-red/5 opacity-0 transition-opacity group-hover:opacity-100" />
                  
                  {/* Animated border */}
                  <div className="absolute inset-0 rounded-lg border-2 border-transparent group-hover:border-protec-red/20 transition-all duration-300" />
                </Card>
              )
            })}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <div className="mx-auto max-w-2xl">
            <h3 className="text-2xl font-bold text-protec-navy mb-4">
              Ready to explore all features?
            </h3>
            <p className="text-gray-600 mb-8">
              Join thousands of PROTEC alumni who are already using these powerful tools 
              to advance their careers and stay connected.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-protec-red hover:bg-protec-red/90 text-white px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
                asChild
              >
                <a href="/auth/signup">
                  Get Started Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </a>
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
                asChild
              >
                <a href="/demo">
                  View Demo
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
