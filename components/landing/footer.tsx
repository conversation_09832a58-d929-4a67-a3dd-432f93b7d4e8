import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { 
  Facebook, 
  Twitter, 
  Linkedin, 
  Instagram, 
  Mail, 
  Phone, 
  MapPin,
  ExternalLink
} from "lucide-react"

const navigation = {
  platform: [
    { name: "Alumni Directory", href: "/directory" },
    { name: "Events", href: "/events" },
    { name: "Community Feed", href: "/feed" },
    { name: "Donations", href: "/donations" },
  ],
  support: [
    { name: "Help Center", href: "/help" },
    { name: "Contact Us", href: "/contact" },
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Terms of Service", href: "/terms" },
  ],
  company: [
    { name: "About PROTEC", href: "https://protec.co.za/about", external: true },
    { name: "Programmes", href: "https://protec.co.za/programmes", external: true },
    { name: "News & Updates", href: "https://protec.co.za/news", external: true },
    { name: "Careers", href: "https://protec.co.za/careers", external: true },
  ],
  social: [
    { name: "Facebook", href: "https://facebook.com/protecsa", icon: Facebook },
    { name: "Twitter", href: "https://twitter.com/protecsa", icon: Twitter },
    { name: "LinkedIn", href: "https://linkedin.com/company/protec-sa", icon: Linkedin },
    { name: "Instagram", href: "https://instagram.com/protecsa", icon: Instagram },
  ],
}

export function LandingFooter() {
  return (
    <footer className="bg-gray-900" aria-labelledby="footer-heading">
      <h2 id="footer-heading" className="sr-only">
        Footer
      </h2>
      <div className="mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          {/* Company info */}
          <div className="space-y-8">
            <div>
              <Link href="/" className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-md bg-protec-red">
                  <span className="text-sm font-bold text-white">P</span>
                </div>
                <span className="text-xl font-bold text-white">
                  PROTEC Alumni
                </span>
              </Link>
              <p className="mt-4 text-sm leading-6 text-gray-300">
                Connecting PROTEC graduates worldwide. Building careers, fostering innovation, 
                and empowering the next generation of STEM leaders since 1982.
              </p>
            </div>

            {/* Contact info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm text-gray-300">
                <Mail className="h-4 w-4 text-protec-red" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-gray-300">
                <Phone className="h-4 w-4 text-protec-red" />
                <span>+27 11 403 6861</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-gray-300">
                <MapPin className="h-4 w-4 text-protec-red" />
                <span>Johannesburg, South Africa</span>
              </div>
            </div>

            {/* Social links */}
            <div className="flex space-x-6">
              {navigation.social.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="text-gray-400 hover:text-protec-red transition-colors"
                  >
                    <span className="sr-only">{item.name}</span>
                    <Icon className="h-6 w-6" />
                  </Link>
                )
              })}
            </div>
          </div>

          {/* Navigation links */}
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">Platform</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.platform.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-gray-300 hover:text-white transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">Support</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.support.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-gray-300 hover:text-white transition-colors"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">PROTEC</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.company.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="flex items-center text-sm leading-6 text-gray-300 hover:text-white transition-colors"
                        {...(item.external && { target: "_blank", rel: "noopener noreferrer" })}
                      >
                        {item.name}
                        {item.external && <ExternalLink className="ml-1 h-3 w-3" />}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">Newsletter</h3>
                <p className="mt-2 text-sm leading-6 text-gray-300">
                  Stay updated with alumni news and events.
                </p>
                <form className="mt-6 sm:flex sm:max-w-md">
                  <label htmlFor="email-address" className="sr-only">
                    Email address
                  </label>
                  <Input
                    type="email"
                    name="email-address"
                    id="email-address"
                    autoComplete="email"
                    required
                    className="w-full min-w-0 bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                    placeholder="Enter your email"
                  />
                  <div className="mt-4 sm:ml-4 sm:mt-0 sm:flex-shrink-0">
                    <Button type="submit" className="bg-protec-red hover:bg-protec-red/90">
                      Subscribe
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <Separator className="my-8 border-gray-700" />

        <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
          <div className="flex space-x-6 text-sm text-gray-400">
            <span>&copy; 2024 PROTEC. All rights reserved.</span>
          </div>
          <div className="text-sm text-gray-400">
            Made with ❤️ for the PROTEC community
          </div>
        </div>
      </div>
    </footer>
  )
}
