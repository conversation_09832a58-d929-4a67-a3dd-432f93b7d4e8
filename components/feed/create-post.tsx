"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { api } from "@/components/providers/trpc-provider"
import { 
  Image, 
  Hash, 
  Send, 
  X,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

export function CreatePost() {
  const { data: session } = useSession()
  const [content, setContent] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState("")
  const [isExpanded, setIsExpanded] = useState(false)

  const createPostMutation = api.posts.create.useMutation()

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      const tag = newTag.trim().toLowerCase().replace(/[^a-z0-9]/g, '')
      if (tag && !tags.includes(tag) && tags.length < 10) {
        setTags([...tags, tag])
        setNewTag("")
      }
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!content.trim()) {
      toast.error("Please write something to share")
      return
    }

    try {
      await createPostMutation.mutateAsync({
        content: content.trim(),
        tags,
        mediaUrls: [], // TODO: Add media upload functionality
      })

      // Reset form
      setContent("")
      setTags([])
      setNewTag("")
      setIsExpanded(false)
      
      toast.success("Post shared successfully!")
    } catch (error) {
      console.error("Error creating post:", error)
      toast.error("Failed to share post. Please try again.")
    }
  }

  if (!session) {
    return null
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Share an Update
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-start space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={session.user?.image || ""} alt={session.user?.name || ""} />
              <AvatarFallback className="bg-protec-navy text-white">
                {getInitials(session.user?.name || "U")}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 space-y-3">
              <Textarea
                placeholder="What's happening in your career or studies? Share your achievements, insights, or questions..."
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onFocus={() => setIsExpanded(true)}
                rows={isExpanded ? 4 : 2}
                className="resize-none border-0 shadow-none focus-visible:ring-0 text-base placeholder:text-muted-foreground"
                maxLength={2000}
              />

              {isExpanded && (
                <div className="space-y-3">
                  {/* Tags Input */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Add tags (press Enter or comma to add)"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyDown={handleAddTag}
                        className="border-0 shadow-none focus-visible:ring-0 text-sm"
                        maxLength={20}
                      />
                    </div>
                    
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            #{tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="ml-1 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Character Count */}
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-4">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="text-muted-foreground hover:text-protec-navy"
                        disabled
                      >
                        <Image className="mr-2 h-4 w-4" />
                        Add Photo
                      </Button>
                    </div>
                    
                    <span className={content.length > 1800 ? "text-red-600" : ""}>
                      {content.length}/2000
                    </span>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={() => {
                        setIsExpanded(false)
                        setContent("")
                        setTags([])
                        setNewTag("")
                      }}
                      disabled={createPostMutation.isLoading}
                    >
                      Cancel
                    </Button>
                    
                    <Button
                      type="submit"
                      disabled={!content.trim() || createPostMutation.isLoading}
                      className="bg-protec-red hover:bg-protec-red/90"
                    >
                      {createPostMutation.isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sharing...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Share Post
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Quick Share Button (when not expanded) */}
          {!isExpanded && content.trim() && (
            <div className="flex justify-end">
              <Button
                type="submit"
                size="sm"
                disabled={createPostMutation.isLoading}
                className="bg-protec-red hover:bg-protec-red/90"
              >
                {createPostMutation.isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Share
              </Button>
            </div>
          )}
        </form>

        {/* Suggestions */}
        {isExpanded && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm font-medium text-blue-900 mb-2">💡 Sharing Tips</p>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Share career updates, achievements, or learning experiences</li>
              <li>• Ask questions to get advice from fellow alumni</li>
              <li>• Use relevant tags to help others discover your content</li>
              <li>• Be professional and respectful in your posts</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
