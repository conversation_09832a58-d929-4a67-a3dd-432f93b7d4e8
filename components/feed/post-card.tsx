"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { api } from "@/components/providers/trpc-provider"
import { 
  Heart, 
  MessageSquare, 
  Share, 
  MoreHorizontal,
  Send,
  Loader2,
  Edit,
  Trash2
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface PostCardProps {
  post: {
    id: string
    content: string
    createdAt: Date
    tags: string[]
    mediaUrls: string[]
    likes: string[]
    author: {
      id: string
      name: string
      photoUrl?: string | null
      currentRole?: string | null
      graduationYear: number
    }
    comments: Array<{
      id: string
      text: string
      createdAt: Date
      author: {
        id: string
        name: string
        photoUrl?: string | null
      }
    }>
    _count: {
      comments: number
    }
  }
}

export function PostCard({ post }: PostCardProps) {
  const { data: session } = useSession()
  const [showComments, setShowComments] = useState(false)
  const [newComment, setNewComment] = useState("")
  const [isLiked, setIsLiked] = useState(
    session?.user?.id ? post.likes.includes(session.user.id) : false
  )

  const likeMutation = api.posts.toggleLike.useMutation()
  const commentMutation = api.posts.addComment.useMutation()
  const deleteMutation = api.posts.delete.useMutation()

  const isAuthor = session?.user?.id === post.author.id

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleLike = async () => {
    if (!session) return
    
    try {
      const result = await likeMutation.mutateAsync({ postId: post.id })
      setIsLiked(result.liked)
    } catch (error) {
      console.error("Like error:", error)
    }
  }

  const handleComment = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!session || !newComment.trim()) return

    try {
      await commentMutation.mutateAsync({
        postId: post.id,
        text: newComment.trim()
      })
      setNewComment("")
      // Optionally refetch comments or update local state
    } catch (error) {
      console.error("Comment error:", error)
    }
  }

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this post?")) return

    try {
      await deleteMutation.mutateAsync({ id: post.id })
      // Post will be removed from the feed automatically via React Query
    } catch (error) {
      console.error("Delete error:", error)
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={post.author.photoUrl || ""} alt={post.author.name} />
              <AvatarFallback className="bg-protec-navy text-white">
                {getInitials(post.author.name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <Link 
                href={`/profile/${post.author.id}`}
                className="font-medium text-protec-navy hover:text-protec-red transition-colors"
              >
                {post.author.name}
              </Link>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <span>{post.author.currentRole || `Class of ${post.author.graduationYear}`}</span>
                <span>•</span>
                <span>{formatDate(post.createdAt)}</span>
              </div>
            </div>
          </div>

          {isAuthor && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Post
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={handleDelete}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Post
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Post Content */}
        <div className="prose prose-sm max-w-none">
          <p className="whitespace-pre-wrap">{post.content}</p>
        </div>

        {/* Media */}
        {post.mediaUrls.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {post.mediaUrls.map((url, index) => (
              <img
                key={index}
                src={url}
                alt={`Post media ${index + 1}`}
                className="rounded-lg object-cover w-full h-48"
              />
            ))}
          </div>
        )}

        {/* Tags */}
        {post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                #{tag}
              </Badge>
            ))}
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              disabled={!session || likeMutation.isLoading}
              className={isLiked ? "text-red-600 hover:text-red-700" : ""}
            >
              <Heart className={`mr-2 h-4 w-4 ${isLiked ? "fill-current" : ""}`} />
              {post.likes.length}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowComments(!showComments)}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              {post._count.comments}
            </Button>

            <Button variant="ghost" size="sm">
              <Share className="mr-2 h-4 w-4" />
              Share
            </Button>
          </div>
        </div>

        {/* Comments Section */}
        {showComments && (
          <div className="space-y-4 pt-4 border-t">
            {/* Existing Comments */}
            {post.comments.length > 0 && (
              <div className="space-y-3">
                {post.comments.map((comment) => (
                  <div key={comment.id} className="flex items-start space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={comment.author.photoUrl || ""} alt={comment.author.name} />
                      <AvatarFallback className="bg-protec-navy text-white text-xs">
                        {getInitials(comment.author.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-sm">{comment.author.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(comment.createdAt)}
                        </span>
                      </div>
                      <p className="text-sm">{comment.text}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Add Comment */}
            {session && (
              <form onSubmit={handleComment} className="flex items-start space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={session.user?.image || ""} alt={session.user?.name || ""} />
                  <AvatarFallback className="bg-protec-navy text-white text-xs">
                    {getInitials(session.user?.name || "U")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-2">
                  <Textarea
                    placeholder="Write a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    rows={2}
                    className="resize-none"
                  />
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      size="sm"
                      disabled={!newComment.trim() || commentMutation.isLoading}
                      className="bg-protec-red hover:bg-protec-red/90"
                    >
                      {commentMutation.isLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="mr-2 h-4 w-4" />
                      )}
                      Comment
                    </Button>
                  </div>
                </div>
              </form>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
