import { initTR<PERSON>, TRPCError } from '@trpc/server'
import { type CreateNextContextOptions } from '@trpc/server/adapters/next'
import { type Session } from 'next-auth'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@/lib/types/user'
import { hasPermission, hasAnyPermission, isAdmin } from '@/lib/auth/roles'
import superjson from 'superjson'
import { ZodError } from 'zod'

/**
 * 1. CONTEXT
 *
 * This section defines the "contexts" that are available in the backend API.
 *
 * These allow you to access things when processing a request, like the database, the session, etc.
 */

interface CreateContextOptions {
  session: Session | null
}

/**
 * This helper generates the "internals" for a tRPC context. If you need to use it, you can export
 * it from here.
 *
 * Examples of things you may need it for:
 * - testing, so we don't have to mock Next.js' req/res
 * - tRPC's `createSSGHelpers`, where we don't have req/res
 *
 * @see https://create.t3.gg/en/usage/trpc#-serverapitrpcts
 */
const createInnerTRPCContext = (opts: CreateContextOptions) => {
  return {
    session: opts.session,
    prisma,
  }
}

/**
 * This is the actual context you will use in your router. It will be used to process every request
 * that goes through your tRPC endpoint.
 *
 * @see https://trpc.io/docs/context
 */
export const createTRPCContext = async (opts: CreateNextContextOptions | { headers: Headers }) => {
  // Get the session from the server
  const session = await auth()

  return createInnerTRPCContext({
    session,
  })
}

/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */

const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }
  },
})

/**
 * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)
 *
 * These are the pieces you use to build your tRPC API. You should import these a lot in the
 * "/src/server/api/routers" directory.
 */

/**
 * This is how you create new routers and sub-routers in your tRPC API.
 *
 * @see https://trpc.io/docs/router
 */
export const createTRPCRouter = t.router

/**
 * Public (unauthenticated) procedure
 *
 * This is the base piece you use to build new queries and mutations on your tRPC API. It does not
 * guarantee that a user querying is authorized, but you can still access user session data if they
 * are logged in.
 */
export const publicProcedure = t.procedure

/**
 * Reusable middleware that enforces users are logged in before running the procedure.
 */
const enforceUserIsAuthed = t.middleware(async ({ ctx, next }) => {
  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }

  // Get user's role and status from database
  const alumni = await ctx.prisma.alumni.findUnique({
    where: { email: ctx.session.user.email! },
    select: { id: true, role: true, isActive: true }
  })

  if (!alumni || !alumni.isActive) {
    throw new TRPCError({ code: 'UNAUTHORIZED', message: 'User not found or inactive' })
  }

  return next({
    ctx: {
      // infers the `session` as non-nullable and adds user role
      session: { ...ctx.session, user: ctx.session.user },
      alumni,
    },
  })
})

/**
 * Role-based middleware factory
 */
const enforceUserHasRole = (allowedRoles: UserRole[]) => {
  return t.middleware(async ({ ctx, next }) => {
    if (!ctx.session || !ctx.session.user) {
      throw new TRPCError({ code: 'UNAUTHORIZED' })
    }

    const alumni = await ctx.prisma.alumni.findUnique({
      where: { email: ctx.session.user.email! },
      select: { id: true, role: true, isActive: true }
    })

    if (!alumni || !alumni.isActive) {
      throw new TRPCError({ code: 'UNAUTHORIZED', message: 'User not found or inactive' })
    }

    if (!allowedRoles.includes(alumni.role)) {
      throw new TRPCError({ code: 'FORBIDDEN', message: 'Insufficient permissions' })
    }

    return next({
      ctx: {
        session: { ...ctx.session, user: ctx.session.user },
        alumni,
      },
    })
  })
}

/**
 * Permission-based middleware factory
 */
const enforceUserHasPermission = (permission: string) => {
  return t.middleware(async ({ ctx, next }) => {
    if (!ctx.session || !ctx.session.user) {
      throw new TRPCError({ code: 'UNAUTHORIZED' })
    }

    const alumni = await ctx.prisma.alumni.findUnique({
      where: { email: ctx.session.user.email! },
      select: { id: true, role: true, isActive: true }
    })

    if (!alumni || !alumni.isActive) {
      throw new TRPCError({ code: 'UNAUTHORIZED', message: 'User not found or inactive' })
    }

    if (!hasPermission(alumni.role, permission)) {
      throw new TRPCError({ code: 'FORBIDDEN', message: 'Insufficient permissions' })
    }

    return next({
      ctx: {
        session: { ...ctx.session, user: ctx.session.user },
        alumni,
      },
    })
  })
}

/**
 * Protected (authenticated) procedure
 *
 * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies
 * the session is valid and guarantees `ctx.session.user` is not null.
 *
 * @see https://trpc.io/docs/procedures
 */
export const protectedProcedure = t.procedure.use(enforceUserIsAuthed)

/**
 * Admin-only procedure
 */
export const adminProcedure = t.procedure.use(enforceUserHasRole([UserRole.ADMIN]))

/**
 * Event organizer procedure (includes admins)
 */
export const eventOrganizerProcedure = t.procedure.use(
  enforceUserHasRole([UserRole.ADMIN, UserRole.EVENT_ORGANIZER])
)

/**
 * Donor coordinator procedure (includes admins)
 */
export const donorCoordinatorProcedure = t.procedure.use(
  enforceUserHasRole([UserRole.ADMIN, UserRole.DONOR_COORDINATOR])
)

/**
 * Permission-based procedure factory
 */
export const createPermissionProcedure = (permission: string) => {
  return t.procedure.use(enforceUserHasPermission(permission))
}
