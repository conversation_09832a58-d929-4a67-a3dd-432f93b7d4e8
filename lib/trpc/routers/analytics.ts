import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, adminProcedure } from '../server'
import { TRPCError } from '@trpc/server'

// Input validation schemas
const timeRangeSchema = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  timeRange: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
})

const overviewStatsSchema = z.object({
  compareWithPrevious: z.boolean().default(true),
  ...timeRangeSchema.shape,
})

export const analyticsRouter = createTRPCRouter({
  // Get overview statistics for admin dashboard
  getOverviewStats: adminProcedure
    .input(overviewStatsSchema)
    .query(async ({ ctx, input }) => {
      const { timeRange, compareWithPrevious } = input

      // Calculate date ranges
      const now = new Date()
      let currentPeriodStart: Date
      let previousPeriodStart: Date
      let previousPeriodEnd: Date

      switch (timeRange) {
        case '7d':
          currentPeriodStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          previousPeriodStart = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000)
          previousPeriodEnd = currentPeriodStart
          break
        case '90d':
          currentPeriodStart = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          previousPeriodStart = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000)
          previousPeriodEnd = currentPeriodStart
          break
        case '1y':
          currentPeriodStart = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          previousPeriodStart = new Date(now.getTime() - 730 * 24 * 60 * 60 * 1000)
          previousPeriodEnd = currentPeriodStart
          break
        default: // 30d
          currentPeriodStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          previousPeriodStart = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)
          previousPeriodEnd = currentPeriodStart
      }

      // Get current period stats
      const [
        totalUsers,
        activeUsers,
        totalEvents,
        totalPosts,
        totalDonationsAmount,
        totalDonationsCount,
      ] = await Promise.all([
        // Total users (all time)
        ctx.prisma.alumni.count({
          where: { isActive: true }
        }),
        
        // Active users (logged in during current period)
        ctx.prisma.alumni.count({
          where: {
            isActive: true,
            lastLoginAt: {
              gte: currentPeriodStart,
            },
          },
        }),
        
        // Total events in current period
        ctx.prisma.event.count({
          where: {
            createdAt: {
              gte: currentPeriodStart,
            },
          },
        }),
        
        // Total posts in current period
        ctx.prisma.post.count({
          where: {
            createdAt: {
              gte: currentPeriodStart,
            },
          },
        }),
        
        // Total donations amount in current period
        ctx.prisma.donation.aggregate({
          where: {
            status: 'completed',
            createdAt: {
              gte: currentPeriodStart,
            },
          },
          _sum: { amountZAR: true },
        }),
        
        // Total donations count in current period
        ctx.prisma.donation.count({
          where: {
            status: 'completed',
            createdAt: {
              gte: currentPeriodStart,
            },
          },
        }),
      ])

      let previousStats = null
      if (compareWithPrevious) {
        // Get previous period stats for comparison
        const [
          prevActiveUsers,
          prevTotalEvents,
          prevTotalPosts,
          prevTotalDonationsAmount,
        ] = await Promise.all([
          ctx.prisma.alumni.count({
            where: {
              isActive: true,
              lastLoginAt: {
                gte: previousPeriodStart,
                lt: previousPeriodEnd,
              },
            },
          }),
          
          ctx.prisma.event.count({
            where: {
              createdAt: {
                gte: previousPeriodStart,
                lt: previousPeriodEnd,
              },
            },
          }),
          
          ctx.prisma.post.count({
            where: {
              createdAt: {
                gte: previousPeriodStart,
                lt: previousPeriodEnd,
              },
            },
          }),
          
          ctx.prisma.donation.aggregate({
            where: {
              status: 'completed',
              createdAt: {
                gte: previousPeriodStart,
                lt: previousPeriodEnd,
              },
            },
            _sum: { amountZAR: true },
          }),
        ])

        previousStats = {
          activeUsers: prevActiveUsers,
          totalEvents: prevTotalEvents,
          totalPosts: prevTotalPosts,
          totalDonations: prevTotalDonationsAmount._sum.amountZAR || 0,
        }
      }

      // Calculate engagement rate (posts + comments + event RSVPs / active users)
      const [totalComments, totalRSVPs] = await Promise.all([
        ctx.prisma.comment.count({
          where: {
            createdAt: {
              gte: currentPeriodStart,
            },
          },
        }),
        ctx.prisma.eventRSVP.count({
          where: {
            createdAt: {
              gte: currentPeriodStart,
            },
          },
        }),
      ])

      const engagementActions = totalPosts + totalComments + totalRSVPs
      const engagementRate = activeUsers > 0 ? (engagementActions / activeUsers) * 100 : 0

      return {
        totalUsers: {
          current: totalUsers,
          previous: null, // Total users don't have a meaningful previous comparison
          change: null,
          trend: 'up' as const,
        },
        activeUsers: {
          current: activeUsers,
          previous: previousStats?.activeUsers || 0,
          change: previousStats ? 
            ((activeUsers - previousStats.activeUsers) / Math.max(previousStats.activeUsers, 1)) * 100 : 0,
          trend: (previousStats && activeUsers >= previousStats.activeUsers) ? 'up' as const : 'down' as const,
        },
        totalEvents: {
          current: totalEvents,
          previous: previousStats?.totalEvents || 0,
          change: previousStats ? 
            ((totalEvents - previousStats.totalEvents) / Math.max(previousStats.totalEvents, 1)) * 100 : 0,
          trend: (previousStats && totalEvents >= previousStats.totalEvents) ? 'up' as const : 'down' as const,
        },
        totalPosts: {
          current: totalPosts,
          previous: previousStats?.totalPosts || 0,
          change: previousStats ? 
            ((totalPosts - previousStats.totalPosts) / Math.max(previousStats.totalPosts, 1)) * 100 : 0,
          trend: (previousStats && totalPosts >= previousStats.totalPosts) ? 'up' as const : 'down' as const,
        },
        totalDonations: {
          current: totalDonationsAmount._sum.amountZAR || 0,
          previous: previousStats?.totalDonations || 0,
          change: previousStats ? 
            ((totalDonationsAmount._sum.amountZAR || 0) - previousStats.totalDonations) / Math.max(previousStats.totalDonations, 1) * 100 : 0,
          trend: (previousStats && (totalDonationsAmount._sum.amountZAR || 0) >= previousStats.totalDonations) ? 'up' as const : 'down' as const,
        },
        engagement: {
          current: Math.round(engagementRate * 10) / 10,
          previous: null, // Would need complex calculation for previous period
          change: null,
          trend: 'up' as const,
        },
      }
    }),

  // Get payment analytics for admin dashboard
  getPaymentAnalytics: adminProcedure
    .input(timeRangeSchema)
    .query(async ({ ctx, input }) => {
      const { timeRange } = input

      // Calculate date range
      const now = new Date()
      let startDate: Date

      switch (timeRange) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        default: // 30d
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      }

      // Get donations data
      const donations = await ctx.prisma.donation.findMany({
        where: {
          createdAt: { gte: startDate },
          status: 'completed'
        },
        select: {
          amountZAR: true,
          gateway: true,
          createdAt: true,
          alumni: {
            select: {
              name: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      // Calculate revenue by month
      const monthlyRevenue = donations.reduce((acc, donation) => {
        const monthKey = donation.createdAt.toISOString().slice(0, 7) // YYYY-MM
        if (!acc[monthKey]) {
          acc[monthKey] = { revenue: 0, donations: 0 }
        }
        acc[monthKey].revenue += donation.amountZAR
        acc[monthKey].donations += 1
        return acc
      }, {} as Record<string, { revenue: number; donations: number }>)

      // Convert to array format
      const revenueData = Object.entries(monthlyRevenue)
        .map(([month, data]) => ({
          month: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short' }),
          revenue: data.revenue,
          donations: data.donations
        }))
        .sort((a, b) => a.month.localeCompare(b.month))

      // Calculate gateway distribution
      const gatewayStats = donations.reduce((acc, donation) => {
        const gateway = donation.gateway
        if (!acc[gateway]) {
          acc[gateway] = { value: 0, amount: 0 }
        }
        acc[gateway].value += 1
        acc[gateway].amount += donation.amountZAR
        return acc
      }, {} as Record<string, { value: number; amount: number }>)

      // Convert to percentage
      const totalDonations = donations.length
      const gatewayData = Object.entries(gatewayStats).map(([name, stats]) => ({
        name: name === 'payfast' ? 'PayFast' : name === 'paypal' ? 'PayPal' : name,
        value: totalDonations > 0 ? Math.round((stats.value / totalDonations) * 100) : 0,
        amount: stats.amount
      }))

      // Recent transactions
      const transactionData = donations.slice(0, 10).map(donation => ({
        date: donation.createdAt.toISOString().split('T')[0],
        amount: donation.amountZAR,
        status: 'completed',
        gateway: donation.gateway,
        donor: donation.alumni.name
      }))

      return {
        revenueData,
        gatewayData,
        transactionData
      }
    }),

  // Get user dashboard analytics
  getUserDashboardStats: protectedProcedure
    .query(async ({ ctx }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const currentYear = new Date().getFullYear()
      const startOfYear = new Date(currentYear, 0, 1)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

      const [
        // Donation stats
        totalDonationAmount,
        thisYearDonationAmount,
        lastDonation,

        // Network stats
        totalConnections,
        recentConnections,

        // Activity stats
        recentActivities,

        // Event stats
        upcomingEvents,
      ] = await Promise.all([
        // Total donation amount
        ctx.prisma.donation.aggregate({
          where: {
            alumniId: currentAlumni.id,
            status: 'completed'
          },
          _sum: { amountZAR: true }
        }),

        // This year donation amount
        ctx.prisma.donation.aggregate({
          where: {
            alumniId: currentAlumni.id,
            status: 'completed',
            createdAt: { gte: startOfYear }
          },
          _sum: { amountZAR: true }
        }),

        // Last donation
        ctx.prisma.donation.findFirst({
          where: {
            alumniId: currentAlumni.id,
            status: 'completed'
          },
          orderBy: { createdAt: 'desc' }
        }),

        // Total connections (this would need a connections table)
        // For now, we'll use a placeholder
        Promise.resolve(0),

        // Recent connections (placeholder)
        Promise.resolve([]),

        // Recent activities
        ctx.prisma.activity.findMany({
          where: {
            alumniId: currentAlumni.id,
            createdAt: { gte: thirtyDaysAgo }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        }),

        // Upcoming events user has RSVP'd to
        ctx.prisma.event.findMany({
          where: {
            startTime: { gte: new Date() },
            rsvps: {
              some: {
                alumniId: currentAlumni.id,
                status: 'ATTENDING'
              }
            }
          },
          orderBy: { startTime: 'asc' },
          take: 5,
          include: {
            organizer: {
              select: {
                name: true
              }
            }
          }
        })
      ])

      return {
        donationProgress: {
          personalTotal: totalDonationAmount._sum.amountZAR || 0,
          personalGoal: 5000, // This could be stored in user preferences
          thisYearDonations: thisYearDonationAmount._sum.amountZAR || 0,
          lastDonation: lastDonation ? {
            amount: lastDonation.amountZAR,
            date: lastDonation.createdAt.toISOString(),
            purpose: lastDonation.purpose
          } : null
        },
        networkGrowth: {
          totalConnections,
          monthlyGrowth: 0, // Placeholder
          growthPercentage: 0, // Placeholder
          recentConnections
        },
        recentActivity: recentActivities.map(activity => ({
          id: activity.id,
          type: activity.type,
          time: activity.createdAt.toISOString(),
          refId: activity.refId
        })),
        upcomingEvents: upcomingEvents.map(event => ({
          id: event.id,
          title: event.title,
          date: event.startTime.toISOString().split('T')[0],
          time: event.startTime.toTimeString().slice(0, 5),
          location: event.location,
          organizer: event.organizer.name
        }))
      }
    }),
})
