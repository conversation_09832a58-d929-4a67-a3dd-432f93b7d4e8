import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, adminProcedure } from '../server'
import { TRPCError } from '@trpc/server'
import { UserRole } from '@/lib/types/user'
import { PERMISSIONS } from '@/lib/auth/roles'

// Input validation schemas
const getUsersSchema = z.object({
  search: z.string().optional(),
  role: z.nativeEnum(UserRole).optional(),
  isActive: z.boolean().optional(),
  limit: z.number().int().min(1).max(100).default(20),
  cursor: z.string().optional(),
})

const updateUserRoleSchema = z.object({
  userId: z.string().uuid(),
  role: z.nativeEnum(UserRole),
})

const updateUserStatusSchema = z.object({
  userId: z.string().uuid(),
  isActive: z.boolean(),
})

const getUserStatsSchema = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
})

export const usersRouter = createTRPCRouter({
  // Get all users (admin only)
  getAll: adminProcedure
    .input(getUsersSchema)
    .query(async ({ ctx, input }) => {
      const { search, role, isActive, limit, cursor } = input

      const where: any = {}

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { currentRole: { contains: search, mode: 'insensitive' } },
          { company: { contains: search, mode: 'insensitive' } },
        ]
      }

      if (role !== undefined) {
        where.role = role
      }

      if (isActive !== undefined) {
        where.isActive = isActive
      }

      const users = await ctx.prisma.alumni.findMany({
        where,
        select: {
          id: true,
          email: true,
          name: true,
          photoUrl: true,
          role: true,
          isActive: true,
          currentRole: true,
          company: true,
          graduationYear: true,
          province: true,
          city: true,
          lastLoginAt: true,
          createdAt: true,
          _count: {
            select: {
              posts: true,
              donations: true,
              connections: true,
              organizedEvents: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (users.length > limit) {
        const nextItem = users.pop()
        nextCursor = nextItem!.id
      }

      return {
        users,
        nextCursor,
      }
    }),

  // Get user statistics (admin only)
  getStats: adminProcedure
    .input(getUserStatsSchema)
    .query(async ({ ctx, input }) => {
      const { startDate, endDate } = input

      const dateFilter = startDate && endDate ? {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      } : {}

      const [
        totalUsers,
        activeUsers,
        newUsers,
        roleDistribution,
        topContributors,
      ] = await Promise.all([
        // Total users
        ctx.prisma.alumni.count(),
        
        // Active users (logged in within last 30 days)
        ctx.prisma.alumni.count({
          where: {
            lastLoginAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            },
          },
        }),
        
        // New users in date range
        ctx.prisma.alumni.count({
          where: dateFilter,
        }),
        
        // Role distribution
        ctx.prisma.alumni.groupBy({
          by: ['role'],
          _count: {
            role: true,
          },
        }),
        
        // Top contributors (by donations)
        ctx.prisma.alumni.findMany({
          select: {
            id: true,
            name: true,
            photoUrl: true,
            _count: {
              select: {
                donations: true,
                posts: true,
                organizedEvents: true,
              },
            },
          },
          orderBy: {
            donations: {
              _count: 'desc',
            },
          },
          take: 10,
        }),
      ])

      return {
        totalUsers,
        activeUsers,
        newUsers,
        roleDistribution: roleDistribution.map(item => ({
          role: item.role,
          count: item._count.role,
        })),
        topContributors,
      }
    }),

  // Update user role (admin only)
  updateRole: adminProcedure
    .input(updateUserRoleSchema)
    .mutation(async ({ ctx, input }) => {
      const { userId, role } = input

      // Prevent changing own role
      if (userId === ctx.alumni.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot change your own role',
        })
      }

      const user = await ctx.prisma.alumni.findUnique({
        where: { id: userId },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      const updatedUser = await ctx.prisma.alumni.update({
        where: { id: userId },
        data: { role },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'ROLE_UPDATED',
          refId: userId,
          alumniId: ctx.alumni.id,
        },
      })

      return updatedUser
    }),

  // Update user status (admin only)
  updateStatus: adminProcedure
    .input(updateUserStatusSchema)
    .mutation(async ({ ctx, input }) => {
      const { userId, isActive } = input

      // Prevent deactivating own account
      if (userId === ctx.alumni.id && !isActive) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot deactivate your own account',
        })
      }

      const user = await ctx.prisma.alumni.findUnique({
        where: { id: userId },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      const updatedUser = await ctx.prisma.alumni.update({
        where: { id: userId },
        data: { isActive },
        select: {
          id: true,
          name: true,
          email: true,
          isActive: true,
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: isActive ? 'USER_ACTIVATED' : 'USER_DEACTIVATED',
          refId: userId,
          alumniId: ctx.alumni.id,
        },
      })

      return updatedUser
    }),

  // Get current user's permissions
  getMyPermissions: protectedProcedure
    .query(async ({ ctx }) => {
      const userRole = ctx.alumni.role
      const permissions = await import('@/lib/auth/roles').then(
        module => module.ROLE_PERMISSIONS[userRole] || []
      )

      return {
        role: userRole,
        permissions,
      }
    }),
})
