import { StatsProps } from '../types'

export function createStats({ items }: StatsProps): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 30px 0;">
      <tr>
        <td>
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; background-color: #f9fafb; border-radius: 12px; overflow: hidden;">
            <tr>
              ${items.map((item, index) => `
                <td style="
                  padding: 24px 16px;
                  text-align: center;
                  vertical-align: top;
                  ${index < items.length - 1 ? 'border-right: 1px solid #e5e7eb;' : ''}
                  width: ${100 / items.length}%;
                ">
                  <div style="
                    font-size: 32px;
                    font-weight: bold;
                    color: #012A5B;
                    margin-bottom: 8px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ">
                    ${item.value}
                  </div>
                  <div style="
                    font-size: 14px;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 4px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ">
                    ${item.label}
                  </div>
                  ${item.description ? `
                    <div style="
                      font-size: 12px;
                      color: #6b7280;
                      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                    ">
                      ${item.description}
                    </div>
                  ` : ''}
                </td>
              `).join('')}
            </tr>
          </table>
        </td>
      </tr>
    </table>
  `
}

export function createTextStats({ items }: StatsProps): string {
  return `
STATISTICS:
${items.map(item => `• ${item.label}: ${item.value}${item.description ? ` (${item.description})` : ''}`).join('\n')}
  `.trim()
}

export function createProgressBar(
  label: string,
  current: number,
  target: number,
  color: string = '#C41E3A'
): string {
  const percentage = Math.min((current / target) * 100, 100)
  
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      <tr>
        <td>
          <div style="margin-bottom: 8px;">
            <span style="
              font-size: 14px;
              font-weight: 600;
              color: #374151;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            ">
              ${label}
            </span>
            <span style="
              float: right;
              font-size: 14px;
              color: #6b7280;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            ">
              ${current} / ${target}
            </span>
          </div>
          <div style="
            width: 100%;
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
          ">
            <div style="
              width: ${percentage}%;
              height: 100%;
              background-color: ${color};
              border-radius: 4px;
              transition: width 0.3s ease;
            "></div>
          </div>
        </td>
      </tr>
    </table>
  `
}

export function createMetricCard(
  title: string,
  value: string | number,
  change?: string,
  changeType?: 'positive' | 'negative' | 'neutral'
): string {
  const changeColors = {
    positive: '#22c55e',
    negative: '#ef4444',
    neutral: '#6b7280'
  }

  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 16px 0;">
      <tr>
        <td style="
          background-color: #ffffff;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        ">
          <div style="
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          ">
            ${title}
          </div>
          <div style="
            font-size: 28px;
            font-weight: bold;
            color: #012A5B;
            margin-bottom: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          ">
            ${value}
          </div>
          ${change && changeType ? `
            <div style="
              font-size: 12px;
              color: ${changeColors[changeType]};
              font-weight: 500;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            ">
              ${change}
            </div>
          ` : ''}
        </td>
      </tr>
    </table>
  `
}