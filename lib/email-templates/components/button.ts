import { ButtonProps } from '../types'

export function createButton({
  text,
  url,
  style = 'primary',
  size = 'medium'
}: ButtonProps): string {
  const styles = {
    primary: {
      background: 'linear-gradient(135deg, #C41E3A, #012A5B)',
      color: '#ffffff',
      border: 'none'
    },
    secondary: {
      background: '#f3f4f6',
      color: '#374151',
      border: '2px solid #d1d5db'
    },
    outline: {
      background: 'transparent',
      color: '#C41E3A',
      border: '2px solid #C41E3A'
    }
  }

  const sizes = {
    small: {
      padding: '8px 16px',
      fontSize: '14px'
    },
    medium: {
      padding: '12px 24px',
      fontSize: '16px'
    },
    large: {
      padding: '16px 32px',
      fontSize: '18px'
    }
  }

  const buttonStyle = styles[style]
  const buttonSize = sizes[size]

  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 20px auto;">
      <tr>
        <td style="border-radius: 8px; background: ${buttonStyle.background}; text-align: center;">
          <a href="${url}" 
             target="_blank" 
             style="
               display: inline-block;
               padding: ${buttonSize.padding};
               font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
               font-size: ${buttonSize.fontSize};
               font-weight: 600;
               color: ${buttonStyle.color};
               text-decoration: none;
               border-radius: 8px;
               border: ${buttonStyle.border};
               transition: all 0.3s ease;
               min-width: 120px;
               text-align: center;
             ">
            ${text}
          </a>
        </td>
      </tr>
    </table>
  `
}

export function createTextButton({ text, url }: { text: string; url: string }): string {
  return `${text}: ${url}`
}

export function createButtonGroup(buttons: ButtonProps[]): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 20px auto;">
      <tr>
        ${buttons.map(button => `
          <td style="padding: 0 10px;">
            ${createButton(button)}
          </td>
        `).join('')}
      </tr>
    </table>
  `
}