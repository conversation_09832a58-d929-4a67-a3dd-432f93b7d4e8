import { AlertProps } from '../types'

export function createAlert({ type, title, message }: AlertProps): string {
  const alertStyles = {
    info: {
      background: '#eff6ff',
      border: '#3b82f6',
      icon: 'ℹ️',
      titleColor: '#1e40af',
      textColor: '#1e3a8a'
    },
    success: {
      background: '#f0fdf4',
      border: '#22c55e',
      icon: '✅',
      titleColor: '#15803d',
      textColor: '#14532d'
    },
    warning: {
      background: '#fefce8',
      border: '#eab308',
      icon: '⚠️',
      titleColor: '#a16207',
      textColor: '#713f12'
    },
    error: {
      background: '#fef2f2',
      border: '#ef4444',
      icon: '❌',
      titleColor: '#dc2626',
      textColor: '#991b1b'
    }
  }

  const style = alertStyles[type]

  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      <tr>
        <td style="
          background-color: ${style.background};
          border-left: 4px solid ${style.border};
          border-radius: 8px;
          padding: 16px 20px;
        ">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
            <tr>
              <td style="width: 24px; vertical-align: top; padding-right: 12px;">
                <span style="font-size: 18px;">${style.icon}</span>
              </td>
              <td style="vertical-align: top;">
                ${title ? `
                  <h3 style="
                    margin: 0 0 8px 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: ${style.titleColor};
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ">
                    ${title}
                  </h3>
                ` : ''}
                <p style="
                  margin: 0;
                  font-size: 14px;
                  line-height: 1.5;
                  color: ${style.textColor};
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                ">
                  ${message}
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  `
}

export function createTextAlert({ type, title, message }: AlertProps): string {
  const icons = {
    info: '[INFO]',
    success: '[SUCCESS]',
    warning: '[WARNING]',
    error: '[ERROR]'
  }

  return `
${icons[type]} ${title ? `${title}: ` : ''}${message}
  `.trim()
}

export function createSecurityNotice(message: string): string {
  return createAlert({
    type: 'warning',
    title: 'Security Notice',
    message
  })
}

export function createSuccessMessage(message: string): string {
  return createAlert({
    type: 'success',
    message
  })
}