// Email template types and interfaces

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export interface EmailTemplateData {
  [key: string]: any
}

export interface TemplateConfig {
  name: string
  description: string
  variables: TemplateVariable[]
  category: EmailCategory
}

export interface TemplateVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'date'
  required: boolean
  description: string
  defaultValue?: any
}

export type EmailCategory = 
  | 'authentication'
  | 'welcome'
  | 'notification'
  | 'marketing'
  | 'system'
  | 'event'
  | 'donation'

export interface BrandingConfig {
  primaryColor: string
  secondaryColor: string
  logoUrl?: string
  organizationName: string
  websiteUrl: string
  supportEmail: string
  socialLinks?: {
    facebook?: string
    twitter?: string
    linkedin?: string
    instagram?: string
  }
}

export interface EmailLayoutProps {
  title: string
  preheader?: string
  content: string
  branding: BrandingConfig
  footer?: {
    unsubscribeUrl?: string
    customText?: string
  }
}

export interface ButtonProps {
  text: string
  url: string
  style?: 'primary' | 'secondary' | 'outline'
  size?: 'small' | 'medium' | 'large'
}

export interface AlertProps {
  type: 'info' | 'success' | 'warning' | 'error'
  title?: string
  message: string
}

export interface StatsProps {
  items: Array<{
    label: string
    value: string | number
    description?: string
  }>
}

export interface FeatureListProps {
  items: Array<{
    icon: string
    title: string
    description: string
  }>
}