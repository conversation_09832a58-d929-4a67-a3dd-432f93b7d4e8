import { BrandingConfig } from './types'

// PROTEC branding configuration
export const protecBranding: BrandingConfig = {
  primaryColor: '#012A5B',
  secondaryColor: '#C41E3A',
  organizationName: 'PROTEC Alumni Network',
  websiteUrl: process.env.NEXTAUTH_URL || 'https://protec.co.za',
  supportEmail: '<EMAIL>',
  socialLinks: {
    facebook: 'https://facebook.com/protec',
    twitter: 'https://twitter.com/protec',
    linkedin: 'https://linkedin.com/company/protec',
  }
}

// Email template configurations
export const templateConfigs = {
  magicLink: {
    name: 'Magic Link Authentication',
    description: 'Passwordless sign-in email with secure link',
    category: 'authentication' as const,
    variables: [
      {
        name: 'url',
        type: 'url' as const,
        required: true,
        description: 'The magic link URL for authentication'
      },
      {
        name: 'host',
        type: 'string' as const,
        required: true,
        description: 'The host domain name'
      },
      {
        name: 'email',
        type: 'email' as const,
        required: true,
        description: 'User email address'
      },
      {
        name: 'expiresIn',
        type: 'string' as const,
        required: false,
        description: 'Link expiration time',
        defaultValue: '24 hours'
      }
    ]
  },
  
  welcome: {
    name: 'Welcome Email',
    description: 'Welcome new users to the platform',
    category: 'welcome' as const,
    variables: [
      {
        name: 'name',
        type: 'string' as const,
        required: true,
        description: 'User full name'
      },
      {
        name: 'email',
        type: 'email' as const,
        required: true,
        description: 'User email address'
      },
      {
        name: 'dashboardUrl',
        type: 'url' as const,
        required: false,
        description: 'Dashboard URL',
        defaultValue: '/dashboard'
      }
    ]
  },

  passwordReset: {
    name: 'Password Reset',
    description: 'Password reset request email',
    category: 'authentication' as const,
    variables: [
      {
        name: 'name',
        type: 'string' as const,
        required: true,
        description: 'User full name'
      },
      {
        name: 'resetUrl',
        type: 'url' as const,
        required: true,
        description: 'Password reset URL'
      },
      {
        name: 'expiresIn',
        type: 'string' as const,
        required: false,
        description: 'Reset link expiration time',
        defaultValue: '1 hour'
      }
    ]
  },

  eventInvitation: {
    name: 'Event Invitation',
    description: 'Invitation to PROTEC events',
    category: 'event' as const,
    variables: [
      {
        name: 'name',
        type: 'string' as const,
        required: true,
        description: 'User full name'
      },
      {
        name: 'eventTitle',
        type: 'string' as const,
        required: true,
        description: 'Event title'
      },
      {
        name: 'eventDate',
        type: 'date' as const,
        required: true,
        description: 'Event date and time'
      },
      {
        name: 'eventLocation',
        type: 'string' as const,
        required: true,
        description: 'Event location'
      },
      {
        name: 'eventDescription',
        type: 'string' as const,
        required: true,
        description: 'Event description'
      },
      {
        name: 'rsvpUrl',
        type: 'url' as const,
        required: true,
        description: 'RSVP URL'
      }
    ]
  },

  donationReceipt: {
    name: 'Donation Receipt',
    description: 'Receipt for donations made to PROTEC',
    category: 'donation' as const,
    variables: [
      {
        name: 'name',
        type: 'string' as const,
        required: true,
        description: 'Donor full name'
      },
      {
        name: 'amount',
        type: 'number' as const,
        required: true,
        description: 'Donation amount'
      },
      {
        name: 'currency',
        type: 'string' as const,
        required: false,
        description: 'Currency code',
        defaultValue: 'ZAR'
      },
      {
        name: 'transactionId',
        type: 'string' as const,
        required: true,
        description: 'Transaction ID'
      },
      {
        name: 'date',
        type: 'date' as const,
        required: true,
        description: 'Donation date'
      },
      {
        name: 'taxDeductible',
        type: 'boolean' as const,
        required: false,
        description: 'Whether donation is tax deductible',
        defaultValue: true
      }
    ]
  },

  newsletter: {
    name: 'Newsletter',
    description: 'Monthly newsletter for alumni',
    category: 'marketing' as const,
    variables: [
      {
        name: 'name',
        type: 'string' as const,
        required: true,
        description: 'User full name'
      },
      {
        name: 'month',
        type: 'string' as const,
        required: true,
        description: 'Newsletter month'
      },
      {
        name: 'year',
        type: 'number' as const,
        required: true,
        description: 'Newsletter year'
      },
      {
        name: 'highlights',
        type: 'string' as const,
        required: true,
        description: 'Newsletter highlights HTML content'
      },
      {
        name: 'unsubscribeUrl',
        type: 'url' as const,
        required: true,
        description: 'Unsubscribe URL'
      }
    ]
  },

  systemNotification: {
    name: 'System Notification',
    description: 'System-wide notifications and announcements',
    category: 'system' as const,
    variables: [
      {
        name: 'name',
        type: 'string' as const,
        required: true,
        description: 'User full name'
      },
      {
        name: 'title',
        type: 'string' as const,
        required: true,
        description: 'Notification title'
      },
      {
        name: 'message',
        type: 'string' as const,
        required: true,
        description: 'Notification message'
      },
      {
        name: 'priority',
        type: 'string' as const,
        required: false,
        description: 'Notification priority (low, medium, high)',
        defaultValue: 'medium'
      },
      {
        name: 'actionUrl',
        type: 'url' as const,
        required: false,
        description: 'Action URL if applicable'
      },
      {
        name: 'actionText',
        type: 'string' as const,
        required: false,
        description: 'Action button text'
      }
    ]
  }
}

// Default email settings
export const emailDefaults = {
  fromName: 'PROTEC Alumni Network',
  fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
  replyTo: '<EMAIL>',
  charset: 'UTF-8',
  encoding: 'base64'
}