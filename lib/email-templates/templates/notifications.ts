import { EmailTemplate, EmailTemplateData } from '../types'
import { protecBranding } from '../config'
import { createEmailLayout, createTextLayout } from '../components/layout'
import { createButton } from '../components/button'
import { createAlert } from '../components/alert'
import { createHeading, createParagraph, createDivider } from '../components/content'

interface NotificationData extends EmailTemplateData {
  name: string
  title: string
  message: string
  priority?: 'low' | 'medium' | 'high'
  actionUrl?: string
  actionText?: string
  category?: 'system' | 'security' | 'feature' | 'maintenance'
}

export function createSystemNotificationTemplate(data: NotificationData): EmailTemplate {
  const {
    name,
    title,
    message,
    priority = 'medium',
    actionUrl,
    actionText,
    category = 'system'
  } = data

  const priorityConfig = {
    low: { type: 'info' as const, icon: 'ℹ️' },
    medium: { type: 'warning' as const, icon: '⚠️' },
    high: { type: 'error' as const, icon: '🚨' }
  }

  const categoryLabels = {
    system: 'System Notification',
    security: 'Security Alert',
    feature: 'New Feature',
    maintenance: 'Maintenance Notice'
  }

  const config = priorityConfig[priority]

  const content = `
    ${createHeading(title, 1)}
    
    ${createParagraph(
      `Hi ${name},`,
      { fontSize: '16px', margin: '0 0 16px 0' }
    )}

    ${createAlert({
      type: config.type,
      title: categoryLabels[category],
      message: message
    })}

    ${actionUrl && actionText ? `
      <div style="text-align: center; margin: 32px 0;">
        ${createButton({
          text: actionText,
          url: actionUrl,
          style: priority === 'high' ? 'primary' : 'outline',
          size: 'medium'
        })}
      </div>
    ` : ''}

    ${createParagraph(
      'If you have any questions or concerns, please don\'t hesitate to contact our support team.',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: `${categoryLabels[category]}: ${title}`,
    preheader: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
${categoryLabels[category]}: ${title}

Hi ${name},

${message}

${actionUrl && actionText ? `${actionText}: ${actionUrl}` : ''}

If you have any questions, contact our support team at ${protecBranding.supportEmail}
  `, protecBranding)

  return {
    subject: `${config.icon} ${categoryLabels[category]}: ${title}`,
    html,
    text
  }
}

export function createSecurityAlertTemplate(data: NotificationData & {
  ipAddress?: string
  location?: string
  device?: string
  timestamp?: string
}): EmailTemplate {
  const {
    name,
    title,
    message,
    actionUrl,
    actionText = 'Secure My Account',
    ipAddress,
    location,
    device,
    timestamp
  } = data

  const content = `
    ${createHeading('🔒 Security Alert', 1)}
    
    ${createParagraph(
      `Hi ${name},`,
      { fontSize: '16px', margin: '0 0 16px 0' }
    )}

    ${createAlert({
      type: 'error',
      title: title,
      message: message
    })}

    ${(ipAddress || location || device || timestamp) ? `
      ${createHeading('Activity Details:', 3)}
      
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 16px 0; background-color: #f9fafb; border-radius: 8px; padding: 16px;">
        ${timestamp ? `
        <tr>
          <td style="padding: 4px 0; font-weight: 600; color: #374151; width: 120px;">Time:</td>
          <td style="padding: 4px 0; color: #6b7280;">${timestamp}</td>
        </tr>
        ` : ''}
        ${ipAddress ? `
        <tr>
          <td style="padding: 4px 0; font-weight: 600; color: #374151; width: 120px;">IP Address:</td>
          <td style="padding: 4px 0; color: #6b7280;">${ipAddress}</td>
        </tr>
        ` : ''}
        ${location ? `
        <tr>
          <td style="padding: 4px 0; font-weight: 600; color: #374151; width: 120px;">Location:</td>
          <td style="padding: 4px 0; color: #6b7280;">${location}</td>
        </tr>
        ` : ''}
        ${device ? `
        <tr>
          <td style="padding: 4px 0; font-weight: 600; color: #374151; width: 120px;">Device:</td>
          <td style="padding: 4px 0; color: #6b7280;">${device}</td>
        </tr>
        ` : ''}
      </table>
    ` : ''}

    ${actionUrl ? `
      <div style="text-align: center; margin: 32px 0;">
        ${createButton({
          text: actionText,
          url: actionUrl,
          style: 'primary',
          size: 'large'
        })}
      </div>
    ` : ''}

    ${createAlert({
      type: 'info',
      title: 'What should you do?',
      message: 'If this was you, you can ignore this email. If this wasn\'t you, please secure your account immediately and contact our support team.'
    })}

    ${createParagraph(
      'For immediate assistance, contact our security <NAME_EMAIL>',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: `Security Alert: ${title}`,
    preheader: 'Unusual activity detected on your account',
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
🔒 Security Alert: ${title}

Hi ${name},

${message}

Activity Details:
${timestamp ? `Time: ${timestamp}` : ''}
${ipAddress ? `IP Address: ${ipAddress}` : ''}
${location ? `Location: ${location}` : ''}
${device ? `Device: ${device}` : ''}

${actionUrl ? `${actionText}: ${actionUrl}` : ''}

If this was you, you can ignore this email. If this wasn't you, please secure your account immediately.

For immediate assistance: <EMAIL>
  `, protecBranding)

  return {
    subject: `🔒 Security Alert: ${title}`,
    html,
    text
  }
}

export function createMaintenanceNotificationTemplate(data: NotificationData & {
  startTime?: string
  endTime?: string
  affectedServices?: string[]
}): EmailTemplate {
  const {
    name,
    title,
    message,
    startTime,
    endTime,
    affectedServices = []
  } = data

  const content = `
    ${createHeading('🔧 Scheduled Maintenance', 1)}
    
    ${createParagraph(
      `Hi ${name},`,
      { fontSize: '16px', margin: '0 0 16px 0' }
    )}

    ${createAlert({
      type: 'warning',
      title: title,
      message: message
    })}

    ${(startTime || endTime) ? `
      ${createHeading('Maintenance Window:', 3)}
      
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 16px 0; background-color: #fef3c7; border-radius: 8px; padding: 16px; border: 1px solid #f59e0b;">
        ${startTime ? `
        <tr>
          <td style="padding: 4px 0; font-weight: 600; color: #92400e; width: 100px;">Start:</td>
          <td style="padding: 4px 0; color: #78350f;">${startTime}</td>
        </tr>
        ` : ''}
        ${endTime ? `
        <tr>
          <td style="padding: 4px 0; font-weight: 600; color: #92400e; width: 100px;">End:</td>
          <td style="padding: 4px 0; color: #78350f;">${endTime}</td>
        </tr>
        ` : ''}
      </table>
    ` : ''}

    ${affectedServices.length > 0 ? `
      ${createHeading('Affected Services:', 3)}
      
      <ul style="margin: 16px 0; padding-left: 20px; color: #374151;">
        ${affectedServices.map(service => `<li style="margin-bottom: 4px;">${service}</li>`).join('')}
      </ul>
    ` : ''}

    ${createAlert({
      type: 'info',
      title: 'What to expect',
      message: 'During this maintenance window, you may experience temporary service interruptions. We apologize for any inconvenience and appreciate your patience.'
    })}

    ${createParagraph(
      'We\'ll send you another notification once the maintenance is complete.',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: `Maintenance Notice: ${title}`,
    preheader: `Scheduled maintenance ${startTime ? `starting ${startTime}` : 'notification'}`,
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
🔧 Scheduled Maintenance: ${title}

Hi ${name},

${message}

Maintenance Window:
${startTime ? `Start: ${startTime}` : ''}
${endTime ? `End: ${endTime}` : ''}

${affectedServices.length > 0 ? `
Affected Services:
${affectedServices.map(service => `• ${service}`).join('\n')}
` : ''}

During this maintenance window, you may experience temporary service interruptions.

We'll notify you once maintenance is complete.
  `, protecBranding)

  return {
    subject: `🔧 Maintenance Notice: ${title}`,
    html,
    text
  }
}

export function createFeatureAnnouncementTemplate(data: NotificationData & {
  featureName?: string
  benefits?: string[]
  learnMoreUrl?: string
}): EmailTemplate {
  const {
    name,
    title,
    message,
    featureName,
    benefits = [],
    learnMoreUrl,
    actionUrl,
    actionText = 'Try It Now'
  } = data

  const content = `
    ${createHeading('🎉 New Feature Available!', 1)}
    
    ${createParagraph(
      `Hi ${name},`,
      { fontSize: '16px', margin: '0 0 16px 0' }
    )}

    ${createAlert({
      type: 'success',
      title: title,
      message: message
    })}

    ${benefits.length > 0 ? `
      ${createHeading('Key Benefits:', 3)}
      
      <ul style="margin: 16px 0; padding-left: 20px; color: #374151;">
        ${benefits.map(benefit => `<li style="margin-bottom: 8px;">${benefit}</li>`).join('')}
      </ul>
    ` : ''}

    ${actionUrl ? `
      <div style="text-align: center; margin: 32px 0;">
        ${createButton({
          text: actionText,
          url: actionUrl,
          style: 'primary',
          size: 'large'
        })}
      </div>
    ` : ''}

    ${learnMoreUrl ? `
      ${createParagraph(
        `Want to learn more? <a href="${learnMoreUrl}" style="color: #C41E3A; text-decoration: none;">Check out our detailed guide</a> for tips and best practices.`,
        { fontSize: '14px', color: '#6b7280', margin: '24px 0' }
      )}
    ` : ''}

    ${createParagraph(
      'We\'re always working to improve your experience. Let us know what you think!',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: `New Feature: ${featureName || title}`,
    preheader: `Introducing ${featureName || 'a new feature'} to enhance your experience`,
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
🎉 New Feature: ${featureName || title}

Hi ${name},

${message}

${benefits.length > 0 ? `
Key Benefits:
${benefits.map(benefit => `• ${benefit}`).join('\n')}
` : ''}

${actionUrl ? `${actionText}: ${actionUrl}` : ''}
${learnMoreUrl ? `Learn more: ${learnMoreUrl}` : ''}

We're always working to improve your experience!
  `, protecBranding)

  return {
    subject: `🎉 New Feature: ${featureName || title}`,
    html,
    text
  }
}