import { EmailTemplate, EmailTemplateData } from '../types'
import { protecBranding } from '../config'
import { createEmailLayout, createTextLayout } from '../components/layout'
import { createButton } from '../components/button'
import { createFeatureList } from '../components/feature-list'
import { createStats } from '../components/stats'
import { createHeading, createParagraph, createDivider } from '../components/content'

interface WelcomeData extends EmailTemplateData {
  name: string
  email: string
  dashboardUrl?: string
}

export function createWelcomeTemplate(data: WelcomeData): EmailTemplate {
  const { name, email, dashboardUrl = `${protecBranding.websiteUrl}/dashboard` } = data

  const content = `
    ${createHeading(`Welcome to the network, ${name}!`, 1)}
    
    ${createParagraph(
      'Congratulations on joining the PROTEC Alumni Network! You\'re now part of a vibrant community of over 15,000 PROTEC graduates worldwide.',
      { fontSize: '18px', color: '#6b7280', margin: '0 0 32px 0' }
    )}

    ${createStats({
      items: [
        { label: 'Alumni Members', value: '15,000+', description: 'Worldwide' },
        { label: 'Countries', value: '50+', description: 'Global reach' },
        { label: 'Years of Impact', value: '40+', description: 'Since 1982' },
        { label: 'Success Stories', value: '1,000+', description: 'And counting' }
      ]
    })}

    ${createHeading('What you can do now:', 2)}

    ${createFeatureList({
      items: [
        {
          icon: '👥',
          title: 'Connect with Alumni',
          description: 'Find and connect with fellow PROTEC graduates in your field, location, or areas of interest.'
        },
        {
          icon: '📅',
          title: 'Join Events',
          description: 'Attend networking events, workshops, webinars, and professional development sessions.'
        },
        {
          icon: '💼',
          title: 'Discover Opportunities',
          description: 'Access exclusive job postings, internships, and career opportunities shared by the community.'
        },
        {
          icon: '🎓',
          title: 'Give Back',
          description: 'Mentor current students and recent graduates, sharing your knowledge and experience.'
        },
        {
          icon: '📚',
          title: 'Access Resources',
          description: 'Explore our library of career resources, industry insights, and professional development materials.'
        },
        {
          icon: '🌟',
          title: 'Share Your Story',
          description: 'Inspire others by sharing your career journey and achievements with the community.'
        }
      ]
    })}

    <div style="text-align: center; margin: 40px 0;">
      ${createButton({
        text: 'Complete Your Profile',
        url: dashboardUrl,
        style: 'primary',
        size: 'large'
      })}
    </div>

    ${createDivider()}

    ${createHeading('Get Started Checklist', 3)}
    
    ${createParagraph(
      'Here are some quick steps to help you make the most of your membership:',
      { fontSize: '14px', color: '#6b7280' }
    )}

    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      <tr>
        <td style="padding: 8px 0;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="width: 24px; vertical-align: top; padding-right: 12px;">
                <span style="font-size: 16px; color: #d1d5db;">☐</span>
              </td>
              <td style="font-size: 14px; line-height: 1.5; color: #374151;">
                Complete your profile with education, career history, and skills
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td style="padding: 8px 0;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="width: 24px; vertical-align: top; padding-right: 12px;">
                <span style="font-size: 16px; color: #d1d5db;">☐</span>
              </td>
              <td style="font-size: 14px; line-height: 1.5; color: #374151;">
                Browse the alumni directory and connect with peers
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td style="padding: 8px 0;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="width: 24px; vertical-align: top; padding-right: 12px;">
                <span style="font-size: 16px; color: #d1d5db;">☐</span>
              </td>
              <td style="font-size: 14px; line-height: 1.5; color: #374151;">
                Join relevant groups and communities within the network
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td style="padding: 8px 0;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="width: 24px; vertical-align: top; padding-right: 12px;">
                <span style="font-size: 16px; color: #d1d5db;">☐</span>
              </td>
              <td style="font-size: 14px; line-height: 1.5; color: #374151;">
                Set up your notification preferences
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td style="padding: 8px 0;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="width: 24px; vertical-align: top; padding-right: 12px;">
                <span style="font-size: 16px; color: #d1d5db;">☐</span>
              </td>
              <td style="font-size: 14px; line-height: 1.5; color: #374151;">
                Explore upcoming events and register for those that interest you
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    ${createParagraph(
      'Need help getting started? Our support team is here to assist you every step of the way.',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: 'Welcome to PROTEC Alumni Network',
    preheader: `Welcome ${name}! You're now part of our global community`,
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
Welcome to PROTEC Alumni Network!

Congratulations ${name}! You're now part of a community of 15,000+ PROTEC alumni worldwide.

Here's what you can do now:

• Connect with Alumni: Find and connect with fellow PROTEC graduates
• Join Events: Attend networking events and professional development sessions  
• Discover Opportunities: Access job postings and career opportunities
• Give Back: Mentor current students and recent graduates
• Access Resources: Explore career resources and industry insights
• Share Your Story: Inspire others with your career journey

Complete your profile: ${dashboardUrl}

Get Started Checklist:
☐ Complete your profile with education and career history
☐ Browse the alumni directory and connect with peers
☐ Join relevant groups and communities
☐ Set up your notification preferences
☐ Explore upcoming events and register

Need help? Contact us at ${protecBranding.supportEmail}
  `, protecBranding)

  return {
    subject: 'Welcome to PROTEC Alumni Network!',
    html,
    text
  }
}

export function createWelcomeBackTemplate(data: WelcomeData): EmailTemplate {
  const { name, dashboardUrl = `${protecBranding.websiteUrl}/dashboard` } = data

  const content = `
    ${createHeading(`Welcome back, ${name}!`, 1)}
    
    ${createParagraph(
      'It\'s great to see you again! We\'ve been busy improving the PROTEC Alumni Network with new features and opportunities.',
      { fontSize: '18px', color: '#6b7280', margin: '0 0 32px 0' }
    )}

    ${createHeading('What\'s New:', 2)}

    ${createFeatureList({
      items: [
        {
          icon: '🆕',
          title: 'Enhanced Networking',
          description: 'New advanced search filters to find alumni by industry, skills, and location.'
        },
        {
          icon: '📱',
          title: 'Mobile App',
          description: 'Access the network on-the-go with our new mobile application.'
        },
        {
          icon: '🎯',
          title: 'Career Matching',
          description: 'AI-powered job recommendations based on your profile and preferences.'
        }
      ]
    })}

    <div style="text-align: center; margin: 40px 0;">
      ${createButton({
        text: 'Explore What\'s New',
        url: dashboardUrl,
        style: 'primary',
        size: 'large'
      })}
    </div>

    ${createParagraph(
      'We\'re excited to have you back and look forward to seeing you engage with the community!',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: 'Welcome Back to PROTEC Alumni',
    preheader: `Welcome back ${name}! Check out what's new`,
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
Welcome back, ${name}!

It's great to see you again! We've been busy improving the PROTEC Alumni Network.

What's New:
• Enhanced Networking: Advanced search filters for finding alumni
• Mobile App: Access the network on-the-go
• Career Matching: AI-powered job recommendations

Explore what's new: ${dashboardUrl}

We're excited to have you back!
  `, protecBranding)

  return {
    subject: 'Welcome Back to PROTEC Alumni Network!',
    html,
    text
  }
}