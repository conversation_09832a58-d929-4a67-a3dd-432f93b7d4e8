import { EmailTemplate, EmailTemplateData } from '../types'
import { protecBranding } from '../config'
import { createEmailLayout, createTextLayout } from '../components/layout'
import { createButton } from '../components/button'
import { createSecurityNotice } from '../components/alert'
import { createHeading, createParagraph, createDivider, createCodeBlock } from '../components/content'

interface MagicLinkData extends EmailTemplateData {
  url: string
  host: string
  email: string
  expiresIn?: string
}

export function createMagicLinkTemplate(data: MagicLinkData): EmailTemplate {
  const { url, host, email, expiresIn = '24 hours' } = data

  const content = `
    ${createHeading('Welcome back!', 1)}
    
    ${createParagraph(
      'Click the button below to sign in to your PROTEC Alumni account. This secure link will log you in automatically.',
      { fontSize: '18px', color: '#6b7280', margin: '0 0 32px 0' }
    )}

    <div style="text-align: center; margin: 32px 0;">
      ${createButton({
        text: 'Sign in to PROTEC Alumni',
        url,
        style: 'primary',
        size: 'large'
      })}
    </div>

    ${createSecurityNotice(`
      <strong>Security Notice:</strong> This link will expire in ${expiresIn} and can only be used once. 
      If you didn't request this email, you can safely ignore it.
    `)}

    ${createDivider()}

    ${createHeading('Having trouble?', 3)}
    
    ${createParagraph(
      'If the button above doesn\'t work, copy and paste this link into your browser:',
      { fontSize: '14px', color: '#6b7280' }
    )}

    ${createCodeBlock(url)}

    ${createParagraph(
      'This email was sent because you requested to sign in to the PROTEC Alumni Network. If you have any questions, contact our support team.',
      { fontSize: '14px', color: '#6b7280', margin: '24px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: 'Sign in to PROTEC Alumni Network',
    preheader: 'Your secure sign-in link is ready',
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
Sign in to PROTEC Alumni Network

Welcome back! Click the link below to sign in to your account:

${url}

This link will expire in ${expiresIn} and can only be used once.

If you didn't request this email, you can safely ignore it.

If you have any questions, contact us at ${protecBranding.supportEmail}
  `, protecBranding)

  return {
    subject: 'Sign in to PROTEC Alumni Network',
    html,
    text
  }
}

export function createMagicLinkVerificationTemplate(data: MagicLinkData): EmailTemplate {
  const { email } = data

  const content = `
    ${createHeading('Email Verification Required', 1)}
    
    ${createParagraph(
      `We received a sign-in request for ${email}. To complete the verification process, please click the button below.`,
      { fontSize: '16px', margin: '0 0 24px 0' }
    )}

    <div style="text-align: center; margin: 32px 0;">
      ${createButton({
        text: 'Verify Email Address',
        url: data.url,
        style: 'primary',
        size: 'large'
      })}
    </div>

    ${createSecurityNotice(`
      This verification link will expire in ${data.expiresIn || '24 hours'} for your security.
    `)}

    ${createParagraph(
      'If you didn\'t request this verification, please ignore this email or contact our support team if you have concerns.',
      { fontSize: '14px', color: '#6b7280', margin: '24px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: 'Verify Your Email Address',
    preheader: 'Complete your email verification',
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
Email Verification Required

We received a sign-in request for ${email}. To complete the verification process, click the link below:

${data.url}

This verification link will expire in ${data.expiresIn || '24 hours'} for your security.

If you didn't request this verification, please ignore this email.
  `, protecBranding)

  return {
    subject: 'Verify Your Email Address - PROTEC Alumni',
    html,
    text
  }
}