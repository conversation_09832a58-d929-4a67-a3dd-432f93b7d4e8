import { EmailTemplate, EmailTemplateData } from '../types'
import { protecBranding } from '../config'
import { createEmailLayout, createTextLayout } from '../components/layout'
import { createButton, createButtonGroup } from '../components/button'
import { createHeading, createParagraph, createDivider, createCard } from '../components/content'
import { createAlert } from '../components/alert'

interface EventData extends EmailTemplateData {
  name: string
  eventTitle: string
  eventDate: string
  eventTime?: string
  eventLocation: string
  eventDescription: string
  rsvpUrl: string
  eventType?: 'networking' | 'workshop' | 'webinar' | 'conference' | 'social'
  isVirtual?: boolean
  eventImage?: string
  organizer?: string
  capacity?: number
  spotsRemaining?: number
}

export function createEventInvitationTemplate(data: EventData): EmailTemplate {
  const {
    name,
    eventTitle,
    eventDate,
    eventTime,
    eventLocation,
    eventDescription,
    rsvpUrl,
    eventType = 'networking',
    isVirtual = false,
    organizer,
    capacity,
    spotsRemaining
  } = data

  const eventTypeLabels = {
    networking: 'Networking Event',
    workshop: 'Workshop',
    webinar: 'Webinar',
    conference: 'Conference',
    social: 'Social Event'
  }

  const content = `
    ${createHeading(`You're Invited, ${name}!`, 1)}
    
    ${createParagraph(
      `We're excited to invite you to an exclusive ${eventTypeLabels[eventType].toLowerCase()} for PROTEC alumni.`,
      { fontSize: '18px', color: '#6b7280', margin: '0 0 32px 0' }
    )}

    ${createCard(eventTitle, `
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
        <tr>
          <td style="padding: 8px 0;">
            <strong style="color: #012A5B;">📅 Date:</strong> ${eventDate}${eventTime ? ` at ${eventTime}` : ''}
          </td>
        </tr>
        <tr>
          <td style="padding: 8px 0;">
            <strong style="color: #012A5B;">${isVirtual ? '💻' : '📍'} Location:</strong> ${eventLocation}
          </td>
        </tr>
        ${organizer ? `
        <tr>
          <td style="padding: 8px 0;">
            <strong style="color: #012A5B;">👤 Organizer:</strong> ${organizer}
          </td>
        </tr>
        ` : ''}
        ${capacity && spotsRemaining ? `
        <tr>
          <td style="padding: 8px 0;">
            <strong style="color: #012A5B;">🎫 Availability:</strong> ${spotsRemaining} of ${capacity} spots remaining
          </td>
        </tr>
        ` : ''}
      </table>
      
      ${createDivider('#e5e7eb', '16px 0')}
      
      ${createParagraph(eventDescription, { fontSize: '14px', margin: '0' })}
    `, {
      backgroundColor: '#f8fafc',
      borderColor: '#C41E3A',
      padding: '24px'
    })}

    ${spotsRemaining && spotsRemaining <= 10 ? createAlert({
      type: 'warning',
      title: 'Limited Spots Available',
      message: `Only ${spotsRemaining} spots remaining! Register soon to secure your place.`
    }) : ''}

    <div style="text-align: center; margin: 32px 0;">
      ${createButtonGroup([
        {
          text: 'RSVP Now',
          url: rsvpUrl,
          style: 'primary',
          size: 'large'
        },
        {
          text: 'View Details',
          url: `${rsvpUrl}?view=details`,
          style: 'outline',
          size: 'large'
        }
      ])}
    </div>

    ${isVirtual ? createAlert({
      type: 'info',
      title: 'Virtual Event',
      message: 'This is a virtual event. You\'ll receive joining instructions after you RSVP.'
    }) : ''}

    ${createParagraph(
      'We look forward to seeing you there! This is a great opportunity to connect with fellow alumni and expand your professional network.',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: `You're Invited: ${eventTitle}`,
    preheader: `Join us for ${eventTitle} on ${eventDate}`,
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
You're Invited: ${eventTitle}

Hi ${name},

We're excited to invite you to an exclusive ${eventTypeLabels[eventType].toLowerCase()} for PROTEC alumni.

Event Details:
• Title: ${eventTitle}
• Date: ${eventDate}${eventTime ? ` at ${eventTime}` : ''}
• Location: ${eventLocation}
${organizer ? `• Organizer: ${organizer}` : ''}
${capacity && spotsRemaining ? `• Availability: ${spotsRemaining} of ${capacity} spots remaining` : ''}

Description:
${eventDescription}

RSVP: ${rsvpUrl}

We look forward to seeing you there!
  `, protecBranding)

  return {
    subject: `You're Invited: ${eventTitle}`,
    html,
    text
  }
}

export function createEventReminderTemplate(data: EventData): EmailTemplate {
  const {
    name,
    eventTitle,
    eventDate,
    eventTime,
    eventLocation,
    isVirtual = false
  } = data

  const content = `
    ${createHeading(`Reminder: ${eventTitle}`, 1)}
    
    ${createParagraph(
      `Hi ${name}, this is a friendly reminder about the upcoming event you registered for.`,
      { fontSize: '18px', color: '#6b7280', margin: '0 0 32px 0' }
    )}

    ${createAlert({
      type: 'info',
      title: 'Event Tomorrow',
      message: `Don't forget about ${eventTitle} happening ${eventDate}${eventTime ? ` at ${eventTime}` : ''}.`
    })}

    ${createCard('Event Details', `
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
        <tr>
          <td style="padding: 8px 0;">
            <strong style="color: #012A5B;">📅 When:</strong> ${eventDate}${eventTime ? ` at ${eventTime}` : ''}
          </td>
        </tr>
        <tr>
          <td style="padding: 8px 0;">
            <strong style="color: #012A5B;">${isVirtual ? '💻' : '📍'} Where:</strong> ${eventLocation}
          </td>
        </tr>
      </table>
    `)}

    ${isVirtual ? `
      ${createAlert({
        type: 'success',
        title: 'Virtual Event Access',
        message: 'Join the event using the link that will be sent to you 30 minutes before the start time.'
      })}
    ` : `
      ${createParagraph(
        'Please plan to arrive 15 minutes early for registration and networking.',
        { fontSize: '14px', color: '#6b7280' }
      )}
    `}

    <div style="text-align: center; margin: 32px 0;">
      ${createButton({
        text: 'View Event Details',
        url: data.rsvpUrl,
        style: 'primary',
        size: 'medium'
      })}
    </div>

    ${createParagraph(
      'Looking forward to seeing you there!',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: `Reminder: ${eventTitle}`,
    preheader: `Don't forget about ${eventTitle} ${eventDate}`,
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
Reminder: ${eventTitle}

Hi ${name},

This is a friendly reminder about the upcoming event you registered for.

Event Details:
• When: ${eventDate}${eventTime ? ` at ${eventTime}` : ''}
• Where: ${eventLocation}

${isVirtual ? 
  'You\'ll receive joining instructions 30 minutes before the event.' :
  'Please plan to arrive 15 minutes early for registration.'
}

View details: ${data.rsvpUrl}

Looking forward to seeing you there!
  `, protecBranding)

  return {
    subject: `Reminder: ${eventTitle} - Tomorrow`,
    html,
    text
  }
}

export function createEventFollowUpTemplate(data: EventData & { feedbackUrl?: string }): EmailTemplate {
  const { name, eventTitle, feedbackUrl } = data

  const content = `
    ${createHeading('Thank you for attending!', 1)}
    
    ${createParagraph(
      `Hi ${name}, thank you for attending ${eventTitle}. We hope you found it valuable and made some great connections!`,
      { fontSize: '18px', color: '#6b7280', margin: '0 0 32px 0' }
    )}

    ${createCard('What\'s Next?', `
      <ul style="margin: 0; padding-left: 20px; color: #374151;">
        <li style="margin-bottom: 8px;">Connect with the alumni you met on our platform</li>
        <li style="margin-bottom: 8px;">Join relevant groups and communities</li>
        <li style="margin-bottom: 8px;">Check out upcoming events</li>
        <li style="margin-bottom: 8px;">Share your experience with the community</li>
      </ul>
    `)}

    ${feedbackUrl ? `
      ${createParagraph(
        'Your feedback helps us improve future events. Please take a moment to share your thoughts:',
        { fontSize: '14px', color: '#6b7280', margin: '24px 0 16px 0' }
      )}

      <div style="text-align: center; margin: 24px 0;">
        ${createButton({
          text: 'Share Feedback',
          url: feedbackUrl,
          style: 'primary',
          size: 'medium'
        })}
      </div>
    ` : ''}

    ${createParagraph(
      'Stay tuned for more exciting events and opportunities!',
      { fontSize: '14px', color: '#6b7280', margin: '32px 0 0 0' }
    )}
  `

  const html = createEmailLayout({
    title: 'Thank you for attending!',
    preheader: `Thanks for joining ${eventTitle}`,
    content,
    branding: protecBranding
  })

  const text = createTextLayout(`
Thank you for attending!

Hi ${name},

Thank you for attending ${eventTitle}. We hope you found it valuable!

What's Next:
• Connect with the alumni you met on our platform
• Join relevant groups and communities  
• Check out upcoming events
• Share your experience with the community

${feedbackUrl ? `Share your feedback: ${feedbackUrl}` : ''}

Stay tuned for more exciting events!
  `, protecBranding)

  return {
    subject: `Thank you for attending ${eventTitle}!`,
    html,
    text
  }
}