import Pusher from 'pusher-js'

export interface MessageEvent {
  id: string
  conversationId: string
  senderId: string
  content: string
  messageType: 'text' | 'image' | 'file'
  createdAt: string
  sender: {
    id: string
    name: string
    photoUrl?: string
  }
}

export interface TypingEvent {
  conversationId: string
  userId: string
  userName: string
  isTyping: boolean
}

export interface OnlineStatusEvent {
  userId: string
  isOnline: boolean
  lastSeen?: string
}

export interface ConversationEvent {
  conversationId: string
  type: 'created' | 'updated' | 'participant_added' | 'participant_removed'
  data: any
}

class PusherService {
  private pusher: Pusher | null = null
  private channels: Map<string, any> = new Map()
  private isInitialized = false
  private currentUserId: string | null = null

  // Event handlers
  private messageHandlers: Map<string, (event: MessageEvent) => void> = new Map()
  private typingHandlers: Map<string, (event: TypingEvent) => void> = new Map()
  private statusHandlers: Map<string, (event: OnlineStatusEvent) => void> = new Map()
  private conversationHandlers: Map<string, (event: ConversationEvent) => void> = new Map()
  private connectionHandlers: Map<string, (connected: boolean) => void> = new Map()

  constructor() {
    if (typeof window !== 'undefined') {
      this.initialize()
    }
  }

  private initialize() {
    if (this.isInitialized) return

    try {
      const key = process.env.NEXT_PUBLIC_PUSHER_KEY
      const cluster = process.env.NEXT_PUBLIC_PUSHER_CLUSTER

      if (!key || !cluster) {
        console.error('Pusher configuration missing. Please set NEXT_PUBLIC_PUSHER_KEY and NEXT_PUBLIC_PUSHER_CLUSTER')
        return
      }

      this.pusher = new Pusher(key, {
        cluster,
        encrypted: true,
        authEndpoint: '/api/pusher/auth',
        auth: {
          headers: {
            'Content-Type': 'application/json',
          }
        }
      })

      this.setupConnectionHandlers()
      this.isInitialized = true
      console.log('Pusher service initialized')
    } catch (error) {
      console.error('Failed to initialize Pusher:', error)
    }
  }

  private setupConnectionHandlers() {
    if (!this.pusher) return

    this.pusher.connection.bind('connected', () => {
      console.log('Pusher connected')
      this.notifyConnectionHandlers(true)
    })

    this.pusher.connection.bind('disconnected', () => {
      console.log('Pusher disconnected')
      this.notifyConnectionHandlers(false)
    })

    this.pusher.connection.bind('error', (error: any) => {
      console.error('Pusher connection error:', error)
      this.notifyConnectionHandlers(false)
    })
  }

  // Authentication
  setCurrentUser(userId: string) {
    this.currentUserId = userId
  }

  getCurrentUser(): string | null {
    return this.currentUserId
  }

  // Channel management
  subscribeToConversation(conversationId: string) {
    if (!this.pusher || this.channels.has(`conversation-${conversationId}`)) {
      return
    }

    try {
      // Subscribe to private conversation channel
      const channel = this.pusher.subscribe(`private-conversation-${conversationId}`)
      this.channels.set(`conversation-${conversationId}`, channel)

      // Bind message events
      channel.bind('new-message', (data: MessageEvent) => {
        this.notifyMessageHandlers(data)
      })

      channel.bind('message-updated', (data: MessageEvent) => {
        this.notifyMessageHandlers({ ...data, isUpdate: true } as any)
      })

      // Bind typing events
      channel.bind('user-typing', (data: TypingEvent) => {
        // Don't notify about own typing
        if (data.userId !== this.currentUserId) {
          this.notifyTypingHandlers({ ...data, isTyping: true })
        }
      })

      channel.bind('user-stop-typing', (data: TypingEvent) => {
        // Don't notify about own typing
        if (data.userId !== this.currentUserId) {
          this.notifyTypingHandlers({ ...data, isTyping: false })
        }
      })

      console.log(`Subscribed to conversation: ${conversationId}`)
    } catch (error) {
      console.error(`Failed to subscribe to conversation ${conversationId}:`, error)
    }
  }

  unsubscribeFromConversation(conversationId: string) {
    const channelName = `conversation-${conversationId}`
    const channel = this.channels.get(channelName)

    if (channel && this.pusher) {
      this.pusher.unsubscribe(`private-conversation-${conversationId}`)
      this.channels.delete(channelName)
      console.log(`Unsubscribed from conversation: ${conversationId}`)
    }
  }

  // Subscribe to user presence (online status)
  subscribeToPresence() {
    if (!this.pusher || !this.currentUserId || this.channels.has('presence')) {
      return
    }

    try {
      const channel = this.pusher.subscribe('presence-alumni-online')
      this.channels.set('presence', channel)

      channel.bind('pusher:member_added', (member: any) => {
        this.notifyStatusHandlers({
          userId: member.id,
          isOnline: true
        })
      })

      channel.bind('pusher:member_removed', (member: any) => {
        this.notifyStatusHandlers({
          userId: member.id,
          isOnline: false,
          lastSeen: new Date().toISOString()
        })
      })

      console.log('Subscribed to presence channel')
    } catch (error) {
      console.error('Failed to subscribe to presence channel:', error)
    }
  }

  // Subscribe to user-specific notifications
  subscribeToUserChannel(userId: string) {
    if (!this.pusher || this.channels.has(`user-${userId}`)) {
      return
    }

    try {
      const channel = this.pusher.subscribe(`private-user-${userId}`)
      this.channels.set(`user-${userId}`, channel)

      // Bind conversation events
      channel.bind('conversation-created', (data: ConversationEvent) => {
        this.notifyConversationHandlers(data)
      })

      channel.bind('conversation-updated', (data: ConversationEvent) => {
        this.notifyConversationHandlers(data)
      })

      console.log(`Subscribed to user channel: ${userId}`)
    } catch (error) {
      console.error(`Failed to subscribe to user channel ${userId}:`, error)
    }
  }

  // Typing indicators
  startTyping(conversationId: string) {
    const channel = this.channels.get(`conversation-${conversationId}`)
    if (channel && this.currentUserId) {
      channel.trigger('client-typing', {
        conversationId,
        userId: this.currentUserId,
        userName: 'Current User' // This should be the actual user name
      })
    }
  }

  stopTyping(conversationId: string) {
    const channel = this.channels.get(`conversation-${conversationId}`)
    if (channel && this.currentUserId) {
      channel.trigger('client-stop-typing', {
        conversationId,
        userId: this.currentUserId,
        userName: 'Current User' // This should be the actual user name
      })
    }
  }

  // Connection status
  isConnected(): boolean {
    return this.pusher?.connection.state === 'connected'
  }

  getConnectionState(): string {
    return this.pusher?.connection.state || 'disconnected'
  }

  // Event handlers registration
  onMessage(id: string, handler: (event: MessageEvent) => void) {
    this.messageHandlers.set(id, handler)
  }

  onTyping(id: string, handler: (event: TypingEvent) => void) {
    this.typingHandlers.set(id, handler)
  }

  onStatusChange(id: string, handler: (event: OnlineStatusEvent) => void) {
    this.statusHandlers.set(id, handler)
  }

  onConversationChange(id: string, handler: (event: ConversationEvent) => void) {
    this.conversationHandlers.set(id, handler)
  }

  onConnectionChange(id: string, handler: (connected: boolean) => void) {
    this.connectionHandlers.set(id, handler)
  }

  // Remove handlers
  removeMessageHandler(id: string) {
    this.messageHandlers.delete(id)
  }

  removeTypingHandler(id: string) {
    this.typingHandlers.delete(id)
  }

  removeStatusHandler(id: string) {
    this.statusHandlers.delete(id)
  }

  removeConversationHandler(id: string) {
    this.conversationHandlers.delete(id)
  }

  removeConnectionHandler(id: string) {
    this.connectionHandlers.delete(id)
  }

  // Cleanup
  disconnect() {
    if (this.pusher) {
      this.pusher.disconnect()
      this.channels.clear()
      this.isInitialized = false
      console.log('Pusher disconnected')
    }
  }

  // Notification methods
  private notifyMessageHandlers(event: MessageEvent) {
    this.messageHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('Error in message handler:', error)
      }
    })
  }

  private notifyTypingHandlers(event: TypingEvent) {
    this.typingHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('Error in typing handler:', error)
      }
    })
  }

  private notifyStatusHandlers(event: OnlineStatusEvent) {
    this.statusHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('Error in status handler:', error)
      }
    })
  }

  private notifyConversationHandlers(event: ConversationEvent) {
    this.conversationHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('Error in conversation handler:', error)
      }
    })
  }

  private notifyConnectionHandlers(connected: boolean) {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected)
      } catch (error) {
        console.error('Error in connection handler:', error)
      }
    })
  }
}

// Export singleton instance
export const pusherService = new PusherService()
export default pusherService
