import { io, Socket } from 'socket.io-client'

export interface MessageEvent {
  type: 'message' | 'typing' | 'stop_typing' | 'user_online' | 'user_offline' | 'conversation_updated'
  data: any
  conversationId?: string
  userId?: string
  timestamp: number
}

export interface TypingEvent {
  conversationId: string
  userId: string
  userName: string
  isTyping: boolean
}

export interface OnlineStatusEvent {
  userId: string
  isOnline: boolean
  lastSeen?: Date
}

class WebSocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private isConnecting = false
  private messageHandlers: Map<string, (event: MessageEvent) => void> = new Map()
  private typingHandlers: Map<string, (event: TypingEvent) => void> = new Map()
  private statusHandlers: Map<string, (event: OnlineStatusEvent) => void> = new Map()
  private connectionHandlers: Map<string, (connected: boolean) => void> = new Map()

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeConnection()
    }
  }

  private initializeConnection() {
    if (this.isConnecting || this.socket?.connected) {
      return
    }

    this.isConnecting = true

    try {
      const token = this.getAuthToken()
      if (!token) {
        console.warn('No auth token available for WebSocket connection')
        this.isConnecting = false
        return
      }

      this.socket = io(process.env.NEXT_PUBLIC_WS_URL || window.location.origin, {
        auth: {
          token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: false,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      })

      this.setupEventListeners()
      this.isConnecting = false
    } catch (error) {
      console.error('Failed to initialize WebSocket connection:', error)
      this.isConnecting = false
    }
  }

  private setupEventListeners() {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
      this.notifyConnectionHandlers(true)
    })

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason)
      this.notifyConnectionHandlers(false)
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnection()
      }
    })

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      this.notifyConnectionHandlers(false)
      this.handleReconnection()
    })

    // Message events
    this.socket.on('new_message', (data) => {
      this.notifyMessageHandlers({
        type: 'message',
        data,
        conversationId: data.conversationId,
        userId: data.senderId,
        timestamp: Date.now()
      })
    })

    this.socket.on('message_updated', (data) => {
      this.notifyMessageHandlers({
        type: 'message',
        data: { ...data, isUpdate: true },
        conversationId: data.conversationId,
        userId: data.senderId,
        timestamp: Date.now()
      })
    })

    // Typing events
    this.socket.on('user_typing', (data) => {
      this.notifyTypingHandlers({
        conversationId: data.conversationId,
        userId: data.userId,
        userName: data.userName,
        isTyping: true
      })
    })

    this.socket.on('user_stop_typing', (data) => {
      this.notifyTypingHandlers({
        conversationId: data.conversationId,
        userId: data.userId,
        userName: data.userName,
        isTyping: false
      })
    })

    // Online status events
    this.socket.on('user_online', (data) => {
      this.notifyStatusHandlers({
        userId: data.userId,
        isOnline: true
      })
    })

    this.socket.on('user_offline', (data) => {
      this.notifyStatusHandlers({
        userId: data.userId,
        isOnline: false,
        lastSeen: data.lastSeen ? new Date(data.lastSeen) : undefined
      })
    })

    // Conversation events
    this.socket.on('conversation_updated', (data) => {
      this.notifyMessageHandlers({
        type: 'conversation_updated',
        data,
        conversationId: data.conversationId,
        timestamp: Date.now()
      })
    })
  }

  private handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      this.initializeConnection()
    }, delay)
  }

  private getAuthToken(): string | null {
    // Get token from localStorage, sessionStorage, or cookies
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth-token') || 
             sessionStorage.getItem('auth-token') ||
             this.getCookieValue('next-auth.session-token')
    }
    return null
  }

  private getCookieValue(name: string): string | null {
    if (typeof document === 'undefined') return null
    
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null
    }
    return null
  }

  // Public methods
  connect() {
    if (!this.socket || !this.socket.connected) {
      this.initializeConnection()
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  isConnected(): boolean {
    return this.socket?.connected || false
  }

  // Join/leave conversation rooms
  joinConversation(conversationId: string) {
    if (this.socket?.connected) {
      this.socket.emit('join_conversation', { conversationId })
    }
  }

  leaveConversation(conversationId: string) {
    if (this.socket?.connected) {
      this.socket.emit('leave_conversation', { conversationId })
    }
  }

  // Send message
  sendMessage(conversationId: string, content: string, messageType: string = 'text') {
    if (this.socket?.connected) {
      this.socket.emit('send_message', {
        conversationId,
        content,
        messageType,
        timestamp: Date.now()
      })
    }
  }

  // Typing indicators
  startTyping(conversationId: string) {
    if (this.socket?.connected) {
      this.socket.emit('start_typing', { conversationId })
    }
  }

  stopTyping(conversationId: string) {
    if (this.socket?.connected) {
      this.socket.emit('stop_typing', { conversationId })
    }
  }

  // Event handlers
  onMessage(id: string, handler: (event: MessageEvent) => void) {
    this.messageHandlers.set(id, handler)
  }

  onTyping(id: string, handler: (event: TypingEvent) => void) {
    this.typingHandlers.set(id, handler)
  }

  onStatusChange(id: string, handler: (event: OnlineStatusEvent) => void) {
    this.statusHandlers.set(id, handler)
  }

  onConnectionChange(id: string, handler: (connected: boolean) => void) {
    this.connectionHandlers.set(id, handler)
  }

  // Remove handlers
  removeMessageHandler(id: string) {
    this.messageHandlers.delete(id)
  }

  removeTypingHandler(id: string) {
    this.typingHandlers.delete(id)
  }

  removeStatusHandler(id: string) {
    this.statusHandlers.delete(id)
  }

  removeConnectionHandler(id: string) {
    this.connectionHandlers.delete(id)
  }

  // Notify handlers
  private notifyMessageHandlers(event: MessageEvent) {
    this.messageHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('Error in message handler:', error)
      }
    })
  }

  private notifyTypingHandlers(event: TypingEvent) {
    this.typingHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('Error in typing handler:', error)
      }
    })
  }

  private notifyStatusHandlers(event: OnlineStatusEvent) {
    this.statusHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('Error in status handler:', error)
      }
    })
  }

  private notifyConnectionHandlers(connected: boolean) {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected)
      } catch (error) {
        console.error('Error in connection handler:', error)
      }
    })
  }
}

// Export singleton instance
export const websocketService = new WebSocketService()
export default websocketService
