import nodemailer from 'nodemailer'
import { createTransport } from 'nodemailer'
import { createEmailTemplate, TemplateType, EmailTemplateData } from '../email-templates'

// Create email transporter
function createEmailTransporter() {
  const config = {
    host: process.env.EMAIL_SERVER_HOST,
    port: Number(process.env.EMAIL_SERVER_PORT) || 587,
    secure: process.env.EMAIL_SERVER_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD,
    },
    // Additional configuration for better deliverability
    pool: true,
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 1000,
    rateLimit: 5,
  }

  return createTransport(config)
}

// Email service functions
export const emailService = {
  // Send templated email
  async sendTemplatedEmail(
    templateType: TemplateType,
    to: string,
    data: EmailTemplateData,
    options?: {
      from?: string
      replyTo?: string
      cc?: string[]
      bcc?: string[]
      priority?: 'high' | 'normal' | 'low'
    }
  ) {
    const transporter = createEmailTransporter()
    
    try {
      const template = createEmailTemplate(templateType, data)
      
      const mailOptions = {
        from: options?.from || {
          name: 'PROTEC Alumni Network',
          address: process.env.EMAIL_FROM || '<EMAIL>'
        },
        to,
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: options?.replyTo,
        cc: options?.cc,
        bcc: options?.bcc,
        headers: options?.priority === 'high' ? {
          'X-Priority': '1',
          'X-MSMail-Priority': 'High',
          'Importance': 'high'
        } : undefined
      }

      const info = await transporter.sendMail(mailOptions)
      console.log(`${templateType} email sent:`, info.messageId)
      return { success: true, messageId: info.messageId }
    } catch (error) {
      console.error(`Failed to send ${templateType} email:`, error)
      throw new Error(`Failed to send ${templateType} email`)
    } finally {
      transporter.close()
    }
  },

  // Send magic link email
  async sendMagicLink(email: string, url: string, host: string) {
    return this.sendTemplatedEmail('magic-link', email, {
      name: email.split('@')[0], // Use email prefix as fallback name
      email,
      url,
      host
    }, { priority: 'high' })
  },

  // Send welcome email
  async sendWelcomeEmail(email: string, name: string) {
    return this.sendTemplatedEmail('welcome', email, {
      name,
      email
    })
  },

  // Send event invitation
  async sendEventInvitation(
    email: string,
    eventData: {
      name: string
      eventTitle: string
      eventDate: string
      eventTime?: string
      eventLocation: string
      eventDescription: string
      rsvpUrl: string
      eventType?: 'networking' | 'workshop' | 'webinar' | 'conference' | 'social'
      isVirtual?: boolean
      organizer?: string
      capacity?: number
      spotsRemaining?: number
    }
  ) {
    return this.sendTemplatedEmail('event-invitation', email, eventData)
  },

  // Send event reminder
  async sendEventReminder(
    email: string,
    eventData: {
      name: string
      eventTitle: string
      eventDate: string
      eventTime?: string
      eventLocation: string
      rsvpUrl: string
      isVirtual?: boolean
    }
  ) {
    return this.sendTemplatedEmail('event-reminder', email, eventData)
  },

  // Send system notification
  async sendSystemNotification(
    email: string,
    notificationData: {
      name: string
      title: string
      message: string
      priority?: 'low' | 'medium' | 'high'
      actionUrl?: string
      actionText?: string
      category?: 'system' | 'security' | 'feature' | 'maintenance'
    }
  ) {
    return this.sendTemplatedEmail('system-notification', email, notificationData, {
      priority: notificationData.priority === 'high' ? 'high' : 'normal'
    })
  },

  // Send security alert
  async sendSecurityAlert(
    email: string,
    alertData: {
      name: string
      title: string
      message: string
      actionUrl?: string
      actionText?: string
      ipAddress?: string
      location?: string
      device?: string
      timestamp?: string
    }
  ) {
    return this.sendTemplatedEmail('security-alert', email, alertData, {
      priority: 'high'
    })
  },

  // Test email configuration
  async testConnection() {
    const transporter = createEmailTransporter()
    
    try {
      await transporter.verify()
      console.log('Email server connection verified')
      return { success: true, message: 'Email server connection verified' }
    } catch (error) {
      console.error('Email server connection failed:', error)
      throw new Error('Email server connection failed')
    } finally {
      transporter.close()
    }
  },

  // Send custom email
  async sendCustomEmail(
    to: string, 
    subject: string, 
    html: string, 
    text?: string,
    options?: {
      from?: string
      replyTo?: string
      cc?: string[]
      bcc?: string[]
    }
  ) {
    const transporter = createEmailTransporter()
    
    try {
      const info = await transporter.sendMail({
        from: options?.from || {
          name: 'PROTEC Alumni Network',
          address: process.env.EMAIL_FROM || '<EMAIL>'
        },
        to,
        subject,
        html,
        text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML if no text provided
        replyTo: options?.replyTo,
        cc: options?.cc,
        bcc: options?.bcc,
      })

      console.log('Custom email sent:', info.messageId)
      return { success: true, messageId: info.messageId }
    } catch (error) {
      console.error('Failed to send custom email:', error)
      throw new Error('Failed to send custom email')
    } finally {
      transporter.close()
    }
  }
}