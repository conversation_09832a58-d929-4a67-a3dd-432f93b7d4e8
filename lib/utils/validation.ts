import { z } from 'zod'
import { ApiError } from './api-errors'

// Common validation schemas
export const commonSchemas = {
  // Basic types
  uuid: z.string().uuid('Invalid UUID format'),
  email: z.string().email('Invalid email format'),
  url: z.string().url('Invalid URL format'),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format'),
  
  // Text fields
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  shortText: z.string().min(1).max(255),
  mediumText: z.string().min(1).max(500),
  longText: z.string().min(1).max(2000),
  
  // Numbers
  positiveInt: z.number().int().positive(),
  nonNegativeInt: z.number().int().min(0),
  year: z.number().int().min(1982).max(new Date().getFullYear() + 10),
  
  // Dates
  pastDate: z.date().max(new Date(), 'Date cannot be in the future'),
  futureDate: z.date().min(new Date(), 'Date cannot be in the past'),
  
  // Arrays
  stringArray: z.array(z.string()).default([]),
  nonEmptyStringArray: z.array(z.string()).min(1, 'At least one item required'),
  
  // Pagination
  pagination: z.object({
    limit: z.number().int().min(1).max(100).default(20),
    cursor: z.string().optional(),
  }),
  
  // Search
  searchQuery: z.string().min(1).max(100).optional(),
}

// Alumni-specific schemas
export const alumniSchemas = {
  profile: z.object({
    name: commonSchemas.name,
    bio: commonSchemas.mediumText.optional(),
    photoUrl: commonSchemas.url.optional(),
    currentRole: commonSchemas.shortText.optional(),
    company: commonSchemas.shortText.optional(),
    industry: commonSchemas.shortText.optional(),
    graduationYear: commonSchemas.year,
    programmes: commonSchemas.nonEmptyStringArray,
    skills: commonSchemas.stringArray,
    interests: commonSchemas.stringArray,
    province: commonSchemas.shortText.optional(),
    city: commonSchemas.shortText.optional(),
    country: commonSchemas.shortText.default('South Africa'),
  }),
  
  socialLinks: z.object({
    linkedin: commonSchemas.url.optional(),
    twitter: commonSchemas.url.optional(),
    github: commonSchemas.url.optional(),
    website: commonSchemas.url.optional(),
  }),
  
  privacy: z.object({
    showEmail: z.boolean().default(false),
    showPhone: z.boolean().default(false),
    showLocation: z.boolean().default(true),
    showConnections: z.boolean().default(true),
    showCareerHistory: z.boolean().default(true),
    showEducation: z.boolean().default(true),
    showProtecInvolvement: z.boolean().default(true),
  }),
  
  search: z.object({
    query: commonSchemas.searchQuery,
    graduationYear: commonSchemas.year.optional(),
    programme: z.string().optional(),
    province: z.string().optional(),
    city: z.string().optional(),
    skills: commonSchemas.stringArray.optional(),
    ...commonSchemas.pagination.shape,
  }),
}

// Event-specific schemas
export const eventSchemas = {
  category: z.enum(['networking', 'workshop', 'conference', 'social', 'career', 'mentorship']),
  
  location: z.object({
    type: z.enum(['physical', 'virtual', 'hybrid']),
    address: z.string().optional(),
    city: z.string().optional(),
    province: z.string().optional(),
    virtualLink: commonSchemas.url.optional(),
    coordinates: z.object({
      lat: z.number().min(-90).max(90),
      lng: z.number().min(-180).max(180),
    }).optional(),
  }),
  
  create: z.object({
    title: z.string().min(1).max(200),
    description: commonSchemas.longText,
    category: z.enum(['networking', 'workshop', 'conference', 'social', 'career', 'mentorship']),
    startTime: z.date(),
    endTime: z.date(),
    location: z.object({
      type: z.enum(['physical', 'virtual', 'hybrid']),
      address: z.string().optional(),
      city: z.string().optional(),
      province: z.string().optional(),
      virtualLink: commonSchemas.url.optional(),
      coordinates: z.object({
        lat: z.number().min(-90).max(90),
        lng: z.number().min(-180).max(180),
      }).optional(),
    }),
  }).refine(data => data.endTime > data.startTime, {
    message: 'End time must be after start time',
    path: ['endTime'],
  }),
}

// Post-specific schemas
export const postSchemas = {
  create: z.object({
    content: commonSchemas.longText,
    mediaUrls: z.array(commonSchemas.url).default([]),
    tags: commonSchemas.stringArray,
    isPublic: z.boolean().default(true),
  }),
  
  update: z.object({
    id: commonSchemas.uuid,
    content: commonSchemas.longText.optional(),
    mediaUrls: z.array(commonSchemas.url).optional(),
    tags: commonSchemas.stringArray.optional(),
    isPublic: z.boolean().optional(),
  }),
  
  comment: z.object({
    postId: commonSchemas.uuid,
    text: commonSchemas.mediumText,
  }),
}

// Message-specific schemas
export const messageSchemas = {
  send: z.object({
    conversationId: commonSchemas.uuid,
    content: z.string().min(1).max(5000),
    messageType: z.enum(['text', 'image', 'file']).default('text'),
    attachmentUrl: commonSchemas.url.optional(),
  }),
  
  conversation: z.object({
    participantIds: z.array(commonSchemas.uuid).min(1).max(10),
    title: z.string().max(100).optional(),
  }),
}

// Donation-specific schemas
export const donationSchemas = {
  create: z.object({
    amount: z.number().positive().min(10).max(100000),
    currency: z.enum(['ZAR', 'USD']).default('ZAR'),
    gateway: z.enum(['payfast', 'snapscan', 'ozow', 'paypal']),
    purpose: z.enum(['general', 'scholarship', 'infrastructure', 'events', 'mentorship']).default('general'),
    frequency: z.enum(['ONE_TIME', 'MONTHLY', 'QUARTERLY', 'ANNUALLY']).default('ONE_TIME'),
    isAnonymous: z.boolean().default(false),
    dedicationMessage: commonSchemas.mediumText.optional(),
  }),
}

// Validation helper functions
export function validateInput<T>(schema: z.ZodSchema<T>, input: unknown): T {
  try {
    return schema.parse(input)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw ApiError.fromZodError(error)
    }
    throw error
  }
}

export function validatePartialInput<T>(schema: z.ZodSchema<T>, input: unknown): Partial<T> {
  try {
    return schema.partial().parse(input)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw ApiError.fromZodError(error)
    }
    throw error
  }
}

// Custom validation functions
export function validateDateRange(startDate: Date, endDate: Date): void {
  if (endDate <= startDate) {
    throw ApiError.badRequest('End date must be after start date')
  }
}

export function validateFileSize(size: number, maxSize: number = 10 * 1024 * 1024): void {
  if (size > maxSize) {
    throw ApiError.badRequest(`File size exceeds maximum allowed size of ${maxSize / 1024 / 1024}MB`)
  }
}

export function validateFileType(mimeType: string, allowedTypes: string[]): void {
  if (!allowedTypes.includes(mimeType)) {
    throw ApiError.badRequest(`File type ${mimeType} is not allowed. Allowed types: ${allowedTypes.join(', ')}`)
  }
}

export function validateImageDimensions(width: number, height: number, maxWidth: number = 2048, maxHeight: number = 2048): void {
  if (width > maxWidth || height > maxHeight) {
    throw ApiError.badRequest(`Image dimensions ${width}x${height} exceed maximum allowed size of ${maxWidth}x${maxHeight}`)
  }
}

// Sanitization functions
export function sanitizeHtml(input: string): string {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
}

export function sanitizeSearchQuery(query: string): string {
  // Remove special characters that could cause issues with search
  return query
    .replace(/[<>\"'%;()&+]/g, '')
    .trim()
    .substring(0, 100)
}

export function normalizeEmail(email: string): string {
  return email.toLowerCase().trim()
}

export function normalizePhoneNumber(phone: string): string {
  // Remove all non-digit characters except +
  return phone.replace(/[^\d+]/g, '')
}

// Validation middleware for tRPC
export function createValidationMiddleware<T>(schema: z.ZodSchema<T>) {
  return (opts: { input: unknown; next: () => Promise<any> }) => {
    const validatedInput = validateInput(schema, opts.input)
    return opts.next()
  }
}
