import { ApiError } from './api-errors'

// Rate limiting configuration
export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (identifier: string) => string // Custom key generator
  skipSuccessfulRequests?: boolean // Don't count successful requests
  skipFailedRequests?: boolean // Don't count failed requests
}

// Default rate limit configurations
export const RATE_LIMIT_CONFIGS = {
  // Authentication endpoints
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
  },
  
  // General API endpoints
  GENERAL: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  },
  
  // Search endpoints
  SEARCH: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 searches per minute
  },
  
  // Message sending
  MESSAGING: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20, // 20 messages per minute
  },
  
  // File uploads
  UPLOAD: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  },
  
  // Payment processing
  PAYMENT: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 payment attempts per minute
  },
  
  // Admin operations
  ADMIN: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200, // 200 admin operations per minute
  },
} as const

// In-memory store for rate limiting (in production, use Redis)
class MemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>()

  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    const data = this.store.get(key)
    if (!data) return null
    
    // Clean up expired entries
    if (Date.now() > data.resetTime) {
      this.store.delete(key)
      return null
    }
    
    return data
  }

  async set(key: string, count: number, windowMs: number): Promise<void> {
    this.store.set(key, {
      count,
      resetTime: Date.now() + windowMs,
    })
  }

  async increment(key: string, windowMs: number): Promise<{ count: number; resetTime: number }> {
    const existing = await this.get(key)
    
    if (!existing) {
      const data = { count: 1, resetTime: Date.now() + windowMs }
      await this.set(key, 1, windowMs)
      return data
    }
    
    existing.count++
    this.store.set(key, existing)
    return existing
  }

  // Cleanup expired entries periodically
  cleanup(): void {
    const now = Date.now()
    for (const [key, data] of this.store.entries()) {
      if (now > data.resetTime) {
        this.store.delete(key)
      }
    }
  }
}

// Global store instance
const store = new MemoryStore()

// Cleanup expired entries every 5 minutes
setInterval(() => store.cleanup(), 5 * 60 * 1000)

// Rate limiter class
export class RateLimiter {
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = config
  }

  async checkLimit(identifier: string): Promise<{
    allowed: boolean
    limit: number
    remaining: number
    resetTime: number
  }> {
    const key = this.config.keyGenerator 
      ? this.config.keyGenerator(identifier)
      : `rate_limit:${identifier}`

    const data = await store.increment(key, this.config.windowMs)
    
    const allowed = data.count <= this.config.maxRequests
    const remaining = Math.max(0, this.config.maxRequests - data.count)

    return {
      allowed,
      limit: this.config.maxRequests,
      remaining,
      resetTime: data.resetTime,
    }
  }

  async enforce(identifier: string): Promise<void> {
    const result = await this.checkLimit(identifier)
    
    if (!result.allowed) {
      throw ApiError.rateLimitExceeded()
    }
  }
}

// Pre-configured rate limiters
export const rateLimiters = {
  auth: new RateLimiter(RATE_LIMIT_CONFIGS.AUTH),
  general: new RateLimiter(RATE_LIMIT_CONFIGS.GENERAL),
  search: new RateLimiter(RATE_LIMIT_CONFIGS.SEARCH),
  messaging: new RateLimiter(RATE_LIMIT_CONFIGS.MESSAGING),
  upload: new RateLimiter(RATE_LIMIT_CONFIGS.UPLOAD),
  payment: new RateLimiter(RATE_LIMIT_CONFIGS.PAYMENT),
  admin: new RateLimiter(RATE_LIMIT_CONFIGS.ADMIN),
}

// Middleware factory for tRPC
export function createRateLimitMiddleware(limiter: RateLimiter) {
  return async (opts: { ctx: any; next: () => Promise<any> }) => {
    const { ctx } = opts
    
    // Get identifier (user ID, IP address, etc.)
    const identifier = ctx.session?.user?.email || 
                      ctx.req?.headers?.['x-forwarded-for'] || 
                      ctx.req?.connection?.remoteAddress || 
                      'anonymous'

    // Check rate limit
    await limiter.enforce(identifier)

    // Continue to next middleware/procedure
    return opts.next()
  }
}

// Utility functions for different rate limiting strategies
export function createUserRateLimit(config: RateLimitConfig) {
  return new RateLimiter({
    ...config,
    keyGenerator: (identifier) => `user:${identifier}`,
  })
}

export function createIPRateLimit(config: RateLimitConfig) {
  return new RateLimiter({
    ...config,
    keyGenerator: (identifier) => `ip:${identifier}`,
  })
}

export function createEndpointRateLimit(endpoint: string, config: RateLimitConfig) {
  return new RateLimiter({
    ...config,
    keyGenerator: (identifier) => `endpoint:${endpoint}:${identifier}`,
  })
}

// Rate limit headers for REST APIs
export function getRateLimitHeaders(result: {
  limit: number
  remaining: number
  resetTime: number
}): Record<string, string> {
  return {
    'X-RateLimit-Limit': result.limit.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
  }
}

// Express middleware for REST endpoints
export function expressRateLimit(limiter: RateLimiter) {
  return async (req: any, res: any, next: any) => {
    try {
      const identifier = req.user?.email || 
                        req.headers['x-forwarded-for'] || 
                        req.connection.remoteAddress || 
                        'anonymous'

      const result = await limiter.checkLimit(identifier)
      
      // Add rate limit headers
      const headers = getRateLimitHeaders(result)
      Object.entries(headers).forEach(([key, value]) => {
        res.setHeader(key, value)
      })

      if (!result.allowed) {
        return res.status(429).json({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded',
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
        })
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}
