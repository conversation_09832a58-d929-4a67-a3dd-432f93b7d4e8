import { TRPCError } from '@trpc/server'
import { z } from 'zod'

// Standardized error codes
export const API_ERROR_CODES = {
  // Authentication & Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  INVALID_TOKEN: 'INVALID_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // Validation
  VALIDATION_ERROR: 'BAD_REQUEST',
  INVALID_INPUT: 'BAD_REQUEST',
  MISSING_REQUIRED_FIELD: 'BAD_REQUEST',
  
  // Resource Management
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'CONFLICT',
  RESOURCE_LIMIT_EXCEEDED: 'BAD_REQUEST',
  
  // Business Logic
  OPERATION_NOT_ALLOWED: 'FORBIDDEN',
  INSUFFICIENT_PERMISSIONS: 'FORBIDDEN',
  ACCOUNT_SUSPENDED: 'FORBIDDEN',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'TOO_MANY_REQUESTS',
  
  // Server Errors
  INTERNAL_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR: 'INTERNAL_SERVER_ERROR',
} as const

// Error message templates
export const ERROR_MESSAGES = {
  // Authentication
  UNAUTHORIZED: 'Authentication required',
  FORBIDDEN: 'Access denied',
  INVALID_TOKEN: 'Invalid authentication token',
  TOKEN_EXPIRED: 'Authentication token has expired',
  
  // Validation
  VALIDATION_ERROR: 'Invalid input data',
  MISSING_REQUIRED_FIELD: (field: string) => `Required field missing: ${field}`,
  INVALID_FORMAT: (field: string) => `Invalid format for field: ${field}`,
  
  // Resources
  NOT_FOUND: (resource: string) => `${resource} not found`,
  ALREADY_EXISTS: (resource: string) => `${resource} already exists`,
  RESOURCE_LIMIT_EXCEEDED: (limit: number) => `Resource limit exceeded (max: ${limit})`,
  
  // Business Logic
  OPERATION_NOT_ALLOWED: 'Operation not allowed',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions for this operation',
  ACCOUNT_SUSPENDED: 'Account has been suspended',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'Too many requests. Please try again later.',
  
  // Server
  INTERNAL_ERROR: 'Internal server error',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  DATABASE_ERROR: 'Database operation failed',
} as const

// Standardized error response interface
export interface ApiErrorResponse {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: string
  requestId?: string
}

// Error factory functions
export class ApiError {
  static unauthorized(message?: string): TRPCError {
    return new TRPCError({
      code: 'UNAUTHORIZED',
      message: message || ERROR_MESSAGES.UNAUTHORIZED,
    })
  }

  static forbidden(message?: string): TRPCError {
    return new TRPCError({
      code: 'FORBIDDEN',
      message: message || ERROR_MESSAGES.FORBIDDEN,
    })
  }

  static notFound(resource?: string): TRPCError {
    return new TRPCError({
      code: 'NOT_FOUND',
      message: resource ? ERROR_MESSAGES.NOT_FOUND(resource) : 'Resource not found',
    })
  }

  static badRequest(message?: string): TRPCError {
    return new TRPCError({
      code: 'BAD_REQUEST',
      message: message || ERROR_MESSAGES.VALIDATION_ERROR,
    })
  }

  static conflict(resource?: string): TRPCError {
    return new TRPCError({
      code: 'CONFLICT',
      message: resource ? ERROR_MESSAGES.ALREADY_EXISTS(resource) : 'Resource conflict',
    })
  }

  static rateLimitExceeded(): TRPCError {
    return new TRPCError({
      code: 'TOO_MANY_REQUESTS',
      message: ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,
    })
  }

  static internalError(message?: string): TRPCError {
    return new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: message || ERROR_MESSAGES.INTERNAL_ERROR,
    })
  }

  static validationError(errors: z.ZodError): TRPCError {
    const details = errors.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
    }))

    return new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Validation failed',
      cause: { details },
    })
  }

  static fromZodError(error: z.ZodError): TRPCError {
    return this.validationError(error)
  }
}

// Error logging utility
export function logError(error: Error, context?: Record<string, any>) {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    context,
  }

  // In production, this would integrate with your logging service
  console.error('API Error:', errorInfo)
  
  // You could also send to external services like Sentry, LogRocket, etc.
  // sentry.captureException(error, { extra: context })
}

// Error handler middleware for tRPC
export function handleTRPCError(error: unknown, context?: Record<string, any>): TRPCError {
  // Log the error
  if (error instanceof Error) {
    logError(error, context)
  }

  // Handle different error types
  if (error instanceof TRPCError) {
    return error
  }

  if (error instanceof z.ZodError) {
    return ApiError.fromZodError(error)
  }

  if (error instanceof Error) {
    // Check for specific database errors
    if (error.message.includes('Unique constraint')) {
      return ApiError.conflict()
    }
    
    if (error.message.includes('Record to update not found')) {
      return ApiError.notFound()
    }
    
    if (error.message.includes('Foreign key constraint')) {
      return ApiError.badRequest('Invalid reference to related resource')
    }
  }

  // Default to internal server error
  return ApiError.internalError()
}

// Validation helpers
export function validateRequired<T>(value: T | undefined | null, fieldName: string): T {
  if (value === undefined || value === null) {
    throw ApiError.badRequest(ERROR_MESSAGES.MISSING_REQUIRED_FIELD(fieldName))
  }
  return value
}

export function validateUUID(value: string, fieldName: string): string {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(value)) {
    throw ApiError.badRequest(`Invalid UUID format for ${fieldName}`)
  }
  return value
}

export function validateEmail(value: string, fieldName: string): string {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(value)) {
    throw ApiError.badRequest(`Invalid email format for ${fieldName}`)
  }
  return value
}

// Permission helpers
export function requirePermission(hasPermission: boolean, operation: string): void {
  if (!hasPermission) {
    throw ApiError.forbidden(`Insufficient permissions for ${operation}`)
  }
}

export function requireOwnership(userId: string, resourceOwnerId: string, resource: string): void {
  if (userId !== resourceOwnerId) {
    throw ApiError.forbidden(`You can only access your own ${resource}`)
  }
}
